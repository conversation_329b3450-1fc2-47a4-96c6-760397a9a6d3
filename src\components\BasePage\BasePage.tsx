import { View } from "@tarojs/components"
import { createContext, createMemo, createSignal, JSXElement, mergeProps } from "solid-js"
import { $isDarkTheme, $themeVars } from "@/store/appStore"
import { useStore } from "@nanostores/solid"


export interface TBasePageProps {
  title?: JSXElement
  children?: JSXElement
  background?: string
  color?: string
  className?: string
}

export const BasePageContext = createContext<any>()

export const BasePage = (_props: TBasePageProps) => {
  const [basePageRef, setBasePageRef] = createSignal()
  const themeVars = useStore($themeVars)
  const isDark = useStore($isDarkTheme)

  const props = mergeProps({}, _props)

  const background = createMemo(() => props.background || themeVars().bgColor)
  const color = createMemo(() => props.color || themeVars().textColor)

  return (
    <View class={`${isDark() ? 'dark' : ''} ${props.className || ''}`} style={{ 'background': background(), color: color(), 'min-height': '100vh' }} ref={setBasePageRef}>
      <BasePageContext.Provider value={basePageRef}>
        {props.children}
      </BasePageContext.Provider>
    </View>
  )
}