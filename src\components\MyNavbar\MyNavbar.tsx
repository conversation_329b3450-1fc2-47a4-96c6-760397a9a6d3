import { $themeVars, $userInfo } from "@/store/appStore"
import { Utils } from "@/utils/utils"
import { View, Text } from "@tarojs/components"
import { JSXElement, mergeProps, Show } from "solid-js"
import { useStore } from '@nanostores/solid'
import { Iconfont } from "../Iconfont"
import Taro from "@tarojs/taro"


export interface TMyNavbarProps {
  // TODO: add props
  title?: JSXElement;
  children?: JSXElement;
  background?: string;
  color?: string;
}

export const MyNavbar = (_props: TMyNavbarProps) => {

  const userInfo = useStore($userInfo)

  const isTabbarPage = Utils.isTabbarPage()

  const props = mergeProps({
    // TODO: default props
  }, _props)

  const themeVars = useStore($themeVars)



  return (
    <View>
      <View class="box-border fixed top-0 left-0 right-0 z-999"
        style={{
          'padding-top': `${Utils.pageSize.statusBarHeight}px`,
          height: `${Utils.pageSize.navbarHeight}px`,
          'background': props.background || themeVars().bgColor,
          color: props.color || themeVars().textColor
        }}>
        <View class="w-full h-full relative flex items-center px-4 gap-2">
          <Show when={!isTabbarPage}>
            <View class="flex-inline items-center h-full -mx-2 px-2" onClick={() => Taro.navigateBack()}>
              <Iconfont name="arrowleft" size={40} />
            </View>
          </Show>
          <Show when={props.title}>
            <View class="flex-inline items-center gap-2 h-full">
              <Text class="text-lg font-bold">{props.title}</Text>
            </View>
          </Show>
          <Show when={props.children}>
            {props.children}
          </Show>
          <Show when={Utils.isDevelop || (Utils.isDev && ((userInfo()?.roles || []).includes('DEV') || Utils.cacheGet("DEV_ROLE")))}>
            <View class="ml-auto mr-130px" onClick={Utils.showDevTools}>
              <Iconfont name="question-circle" size={40} />
            </View>
          </Show>
        </View>
      </View>
      <View style={{ height: `${Utils.pageSize.navbarHeight}px` }} />
    </View>
  )
}