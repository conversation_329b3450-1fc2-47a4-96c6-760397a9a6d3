```tsx
const form = useMyForm()

  createEffect(() => {
    console.log("form.values() changed", form.values())
    console.log("form.errors() changed", form.errors())
  })

  const onSubmit = (values: any) => {
    console.log(values)
  }

  onMount(() => {
    form.setFieldsValue({
      name: '123',
      hobby: 'running'
    })
  })

  return (
      <MyForm
        form={form}
        onSubmit={onSubmit}
      >
        <MyForm.Item label="名称" name="name" requiredSgin>
          <MyForm.Input placeholder="请输入名称" />
        </MyForm.Item>
        <MyForm.Item label="性别" name="sex" rules={[{ required: true, message: '请选择性别!' }]}>
          <MyForm.Select placeholder="请选择" options={[{ label: '男', value: '1' }, { label: '女', value: '2' }]} />
        </MyForm.Item>
        <View class="mt-2">
          <MyForm.Item label="爱好" name="hobby" rules={[{ required: true, message: 'Please input your hobby!' }]}>
            <MyForm.Input />
          </MyForm.Item>
        </View>
        <View>
          <Button type="primary" onClick={form.submit} loading={form.submitting()}>提交</Button>
        </View>
      </MyForm>

      <MyForm.Item form={form} label="爱好" name="hobby" rules={[{ required: true, message: 'Please input your hobby!' }]}>
        <MyForm.Input />
      </MyForm.Item>
  )
```
