!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).F2={})}(this,(function(t){"use strict";var e=function(t){return null!==t&&"function"!=typeof t&&isFinite(t.length)},n=function(t,n){if(!e(t))return t;for(var i=[],r=0;r<t.length;r++){var a=t[r];n(a,r)&&i.push(a)}return i},i={}.toString,r=function(t,e){return i.call(t)==="[object "+e+"]"},a=function(t){return r(t,"Function")},s=function(t){return null==t},o=function(t){return Array.isArray?Array.isArray(t):r(t,"Array")},h=function(t){var e=typeof t;return null!==t&&"object"===e||"function"===e};function u(t,e){if(t)if(o(t))for(var n=0,i=t.length;n<i&&!1!==e(t[n],n);n++);else if(h(t))for(var r in t)if(t.hasOwnProperty(r)&&!1===e(t[r],r))break}var l=Object.keys?function(t){return Object.keys(t)}:function(t){var e=[];return u(t,(function(n,i){a(t)&&"prototype"===i||e.push(i)})),e};var c=function(t){return"object"==typeof t&&null!==t},f=function(t){if(!c(t)||!r(t,"Object"))return!1;if(null===Object.getPrototypeOf(t))return!0;for(var e=t;null!==Object.getPrototypeOf(e);)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e};var g=function(t){var e=t.filter((function(t){return!isNaN(t)}));if(!e.length)return{min:0,max:0};if(o(t[0])){for(var n=[],i=0;i<t.length;i++)n=n.concat(t[i]);e=n}var r=function(t){if(o(t))return t.reduce((function(t,e){return Math.max(t,e)}),t[0])}(e);return{min:function(t){if(o(t))return t.reduce((function(t,e){return Math.min(t,e)}),t[0])}(e),max:r}},p=function(t){return r(t,"String")};function d(t,e){void 0===e&&(e=new Map);var n=[];if(Array.isArray(t))for(var i=0,r=t.length;i<r;i++){var a=t[i];e.has(a)||(n.push(a),e.set(a,!0))}return n}function v(t){if(e(t))return t[0]}function y(t){if(e(t)){return t[t.length-1]}}var m=function(t,e){var n=e.toString(),i=n.indexOf(".");if(-1===i)return Math.round(t);var r=n.substr(i+1).length;return r>20&&(r=20),parseFloat(t.toFixed(r))},x=function(t){return r(t,"Number")},_=Object.values?function(t){return Object.values(t)}:function(t){var e=[];return u(t,(function(n,i){a(t)&&"prototype"===i||e.push(n)})),e},S=function(t){return s(t)?"":t.toString()},M=function(t){var e=S(t);return e.charAt(0).toLowerCase()+e.substring(1)};function w(t,e){return t&&e?t.replace(/\\?\{([^{}]+)\}/g,(function(t,n){return"\\"===t.charAt(0)?t.slice(1):void 0===e[n]?"":e[n]})):t}var b=function(t){var e=S(t);return e.charAt(0).toUpperCase()+e.substring(1)},C={}.toString,P=function(t){return r(t,"Boolean")},k=function(t){return r(t,"Date")},T=Object.prototype;function A(t,e){for(var n in e)e.hasOwnProperty(n)&&"constructor"!==n&&void 0!==e[n]&&(t[n]=e[n])}function D(t,e,n,i){return e&&A(t,e),n&&A(t,n),i&&A(t,i),t}function I(t,e,n,i){for(var r in n=n||0,i=i||5,e)if(e.hasOwnProperty(r)){var a=e[r];null!==a&&f(a)?(f(t[r])||(t[r]={}),n<i?I(t[r],a,n+1,i):t[r]=e[r]):o(a)?(t[r]=[],t[r]=t[r].concat(a)):void 0!==a&&(t[r]=a)}}var Y=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];for(var i=0;i<e.length;i+=1)I(t,e[i]);return t},O=function(t,n){if(!e(t))return-1;var i=Array.prototype.indexOf;if(i)return i.call(t,n);for(var r=-1,a=0;a<t.length;a++)if(t[a]===n){r=a;break}return r},E=Object.prototype.hasOwnProperty;function N(t){if(s(t))return!0;if(e(t))return!t.length;var n=function(t){return C.call(t).replace(/^\[object /,"").replace(/]$/,"")}(t);if("Map"===n||"Set"===n)return!t.size;if(function(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||T)}(t))return!Object.keys(t).length;for(var i in t)if(E.call(t,i))return!1;return!0}var F=function(t,n){if(t===n)return!0;if(!t||!n)return!1;if(p(t)||p(n))return!1;if(e(t)||e(n)){if(t.length!==n.length)return!1;for(var i=!0,r=0;r<t.length&&(i=F(t[r],n[r]));r++);return i}if(c(t)||c(n)){var a=Object.keys(t),s=Object.keys(n);if(a.length!==s.length)return!1;for(i=!0,r=0;r<a.length&&(i=F(t[a[r]],n[a[r]]));r++);return i}return!1};function z(t){return s(t)?0:e(t)?t.length:Object.keys(t).length}var B=function(t,e){return(B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)};function L(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}B(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var X,j=function(){return(j=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var r in e=arguments[n])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)};function G(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;var i=Array(t),r=0;for(e=0;e<n;e++)for(var a=arguments[e],s=0,o=a.length;s<o;s++,r++)i[r]=a[s];return i}function R(t){for(var e=[],n=0,i=t.length;n<i;n++)e=e.concat(t[n]);return e}function H(t,e){for(var n=[],i={},r=0,a=t.length;r<a;r++){var h=t[r][e];s(h)||(o(h)?u(h,(function(t){i[t]||(n.push(t),i[t]=!0)})):i[h]||(n.push(h),i[h]=!0))}return n}function W(t,e){for(var n=null,i=0,r=t.length;i<r;i++){var a=t[i][e];if(!s(a)){n=o(a)?a[0]:a;break}}return n}function V(t,e){if(!e)return{0:t};for(var n=function(t){for(var n="_",i=0,r=e.length;i<r;i++)n+=t[e[i]]&&t[e[i]].toString();return n},i={},r=0,a=t.length;r<a;r++){var s=t[r],o=n(s);i[o]?i[o].push(s):i[o]=[s]}return i}function q(t,e,n){if(void 0===n&&(n={}),!e)return[t];var i=V(t,e),r=[];if(1===e.length&&n[e[0]])u(n[e[0]],(function(t){t="_"+t,r.push(i[t])}));else for(var a in i)r.push(i[a]);return r}function Z(t,e){if(t){var n=t.indexOf(e);-1!==n&&t.splice(n,1)}}function U(t){if(!t.length)return{min:0,max:0};var e=Math.max.apply(null,t);return{min:Math.min.apply(null,t),max:e}}!function(t,e){if(!a(t))throw new TypeError("Expected a function");var n=function(){for(var i=[],r=0;r<arguments.length;r++)i[r]=arguments[r];var a=e?e.apply(this,i):i[0],s=n.cache;if(s.has(a))return s.get(a);var o=t.apply(this,i);return s.set(a,o),o};n.cache=new Map}((function(t,e){void 0===e&&(e={});var n=e.fontSize,i=e.fontFamily,r=e.fontWeight,a=e.fontStyle,s=e.fontVariant;return X||(X=document.createElement("canvas").getContext("2d")),X.font=[a,s,r,n+"px",i].join(" "),X.measureText(p(t)?t:"").width}),(function(t,e){return void 0===e&&(e={}),G([t],_(e)).join("")}));var J=Object.freeze({__proto__:null,merge:R,values:H,firstValue:W,group:q,groupToMap:V,remove:Z,getRange:U}),$=!!function(){var t=!1;try{var e=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("e",null,e)}catch(t){}return t}()&&{passive:!0},K="object"==typeof wx&&"function"==typeof wx.getSystemInfoSync,Q="object"==typeof my&&"function"==typeof my.getSystemInfoSync,tt=typeof global&&!1,et="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.sessionStorage;function nt(t){return!(!t||"object"!=typeof t)&&(!(1!==t.nodeType||!t.nodeName)||!!t.isCanvasElement)}function it(){return window&&window.devicePixelRatio||1}function rt(t,e){return t.currentStyle?t.currentStyle[e]:document.defaultView.getComputedStyle(t,null).getPropertyValue(e)}function at(t){var e=rt(t,"width");return"auto"===e&&(e=t.offsetWidth),parseFloat(e)}function st(t){var e=rt(t,"height");return"auto"===e&&(e=t.offsetHeight),parseFloat(e)}function ot(t){return t?document.getElementById(t):null}function ht(t,e){var n=e.get("el");if(!n)return t;var i=n.getBoundingClientRect(),r=i.top,a=i.left,s=parseFloat(rt(n,"padding-left")),o=parseFloat(rt(n,"padding-top"));return{x:t.x-a-s,y:t.y-r-o}}function ut(t,e,n){t.addEventListener(e,n,$)}function lt(t,e,n){t.removeEventListener(e,n,$)}function ct(t,e){var n=e.get("landscape");if(!n)return t;if(a(n))return n(t,e);var i=e.get("height");return{x:t.y,y:i-t.x}}function ft(t,e){var n=t.touches;if(!n)return[ct(ht({x:t.clientX,y:t.clientY},e),e)];n.length||(n=t.changedTouches||[]);for(var i=[],r=0,a=n.length;r<a;r++){var s=n[r],o=s.x,h=s.y,u=s.clientX,l=s.clientY,c=void 0;c=x(o)||x(h)?{x:o,y:h}:ht({x:u,y:l},e),i.push(ct(c,e))}return i}function gt(t,e){var n=ft(t,e.get("canvas"))[0]||{};return{type:t.type,chart:e,native:t,x:n.x,y:n.y}}function pt(t,e,n){return n||(n=document.createElement("canvas").getContext("2d")),n.font=e||"12px sans-serif",n.measureText(t)}function dt(t){var e,n,i,r;return x(t)||p(t)?e=i=r=n=t:o(t)&&(e=t[0],n=s(t[1])?t[0]:t[1],i=s(t[2])?t[0]:t[2],r=s(t[3])?n:t[3]),[e,n,i,r]}function vt(t,e){return void 0===t||"string"==typeof t&&-1!==t.indexOf(e)}function yt(t){return p(t)&&(t=t.indexOf("T")>0?new Date(t).getTime():new Date(t.replace(/-/gi,"/")).getTime()),k(t)&&(t=t.getTime()),t}var mt=Object.freeze({__proto__:null,Array:J,upperFirst:b,lowerFirst:M,isString:p,isNumber:x,isBoolean:P,isFunction:a,isDate:k,isArray:o,isNil:s,isObject:h,isPlainObject:f,isEqual:F,deepMix:Y,mix:D,each:u,uniq:d,find:function(t,e){if(!o(t))return null;var n;if(a(e)&&(n=e),f(e)&&(n=function(t){return function(t,e){var n=l(e),i=n.length;if(s(t))return!i;for(var r=0;r<i;r+=1){var a=n[r];if(e[a]!==t[a]||!(a in t))return!1}return!0}(t,e)}),n)for(var i=0;i<t.length;i+=1)if(n(t[i]))return t[i];return null},isObjectValueEqual:function(t,e){t=Object.assign({},t),e=Object.assign({},e);var n=Object.getOwnPropertyNames(t),i=Object.getOwnPropertyNames(e);if(n.length!==i.length)return!1;for(var r=0,a=n.length;r<a;r++){var s=n[r];if(t[s]!==e[s])return!1}return!0},parsePadding:dt,directionEnabled:vt,toTimeStamp:yt,substitute:w,isWx:K,isMy:Q,isNode:tt,isBrowser:et,isCanvasElement:nt,getPixelRatio:it,getStyle:rt,getWidth:at,getHeight:st,getDomById:ot,getRelativePosition:ht,addEventListener:ut,removeEventListener:lt,createEvent:gt,convertPoints:ft,measureText:pt}),xt={label:{fill:"#808080",fontSize:10},line:{stroke:"#E8E8E8",lineWidth:1},grid:{type:"line",stroke:"#E8E8E8",lineWidth:1,lineDash:[2]},tickLine:null,labelOffset:7.5},_t={fontFamily:'"Helvetica Neue", "San Francisco", Helvetica, Tahoma, Arial, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", sans-serif',defaultColor:"#1890FF",pixelRatio:1,padding:"auto",appendPadding:15,colors:["#1890FF","#2FC25B","#FACC14","#223273","#8543E0","#13C2C2","#3436C7","#F04864"],shapes:{line:["line","dash"],point:["circle","hollowCircle"]},sizes:[4,10],axis:{common:xt,bottom:D({},xt,{grid:null}),left:D({},xt,{line:null}),right:D({},xt,{line:null}),circle:D({},xt,{line:null}),radius:D({},xt,{labelOffset:4})},shape:{line:{lineWidth:2,lineJoin:"round",lineCap:"round"},point:{lineWidth:0,size:3},area:{fillOpacity:.1}},_defaultAxis:xt},St={general:{title:"这是一个图表，",withTitle:"这是一个关于“{title}”的图表。"},coord:{cartesian:"X轴是{xLabel}Y轴是{yLabel}"},scale:{linear:"数值型，数据最小值为{min}，最大值为{max}；",cat:"分类型, 分类类型有：{values}；",timeCat:"时间型，时间范围从{start}到{end}；"},geometry:{prefix:"共有{count}种分类组成，",oneData:"第{index}类是{name}，数据是{values};",partData:"第{index}类是{name}，共有{count}项数据，前{part}项是{values};",allData:"第{index}类是{name}，有{count}项数据，分别是{values};"},legend:{prefix:"图例分类有："}},Mt={version:"3.8.13",scales:{},widthRatio:{column:.5,rose:.999999,multiplePie:3/4},lineDash:[4,4],lang:St};function wt(t,e,n){return(e=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function bt(){return(bt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t}).apply(this,arguments)}function Ct(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,Pt(t,e)}function Pt(t,e){return(Pt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function kt(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}Mt.setTheme=function(t){Y(Mt,t)},Mt.setTheme(_t);var Tt=function(){function t(){this.__events={}}var e=t.prototype;return e.on=function(t,e){if(t&&e){var n=this.__events[t]||[];n.push(e),this.__events[t]=n}},e.emit=function(t,e){var n=this;if(h(t)&&(t=(e=t)&&e.type),t){var i=this.__events[t];i&&i.length&&i.forEach((function(t){t.call(n,e)}))}},e.off=function(t,e){var n=this.__events,i=n[t];if(i&&i.length)if(e)for(var r=0,a=i.length;r<a;r++)i[r]===e&&(i.splice(r,1),r--);else delete n[t]},t}(),At=function(t){Ct(n,t);var e=n.prototype;function n(e){var n,i={},r=(n=t.call(this)||this).getDefaultCfg();return n._attrs=i,D(i,r,e),n}return e.getDefaultCfg=function(){return{}},e.get=function(t){return this._attrs[t]},e.set=function(t,e){this._attrs[t]=e},e.destroy=function(){this._attrs={},this.destroyed=!0},n}(Tt),Dt=function(){function t(t){D(this,t),this._init()}var e=t.prototype;return e._init=function(){var t=this.start,e=this.end,n=Math.min(t.x,e.x),i=Math.max(t.x,e.x),r=Math.min(t.y,e.y),a=Math.max(t.y,e.y);this.tl={x:n,y:r},this.tr={x:i,y:r},this.bl={x:n,y:a},this.br={x:i,y:a},this.width=i-n,this.height=a-r},e.reset=function(t,e){this.start=t,this.end=e,this._init()},e.isInRange=function(t,e){h(t)&&(e=t.y,t=t.x);var n=this.tl,i=this.br;return n.x<=t&&t<=i.x&&n.y<=e&&e<=i.y},t}(),It={generateDefault:function(){return[1,0,0,1,0,0]},isChanged:function(t){return 1!==t[0]||0!==t[1]||0!==t[2]||1!==t[3]||0!==t[4]||0!==t[5]},multiply:function(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]},scale:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[0],t[2]=e[2]*n[1],t[3]=e[3]*n[1],t[4]=e[4],t[5]=e[5],t},rotate:function(t,e,n){var i=Math.cos(n),r=Math.sin(n),a=e[0]*i+e[2]*r,s=e[1]*i+e[3]*r,o=e[0]*-r+e[2]*i,h=e[1]*-r+e[3]*i;return t[0]=a,t[1]=s,t[2]=o,t[3]=h,t[4]=e[4],t[5]=e[5],t},translate:function(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+e[0]*n[0]+e[2]*n[1],t[5]=e[5]+e[1]*n[0]+e[3]*n[1],t},transform:function(t,e){for(var n=[].concat(t),i=0,r=e.length;i<r;i++){var a=e[i];switch(a[0]){case"t":It.translate(n,n,[a[1],a[2]]);break;case"s":It.scale(n,n,[a[1],a[2]]);break;case"r":It.rotate(n,n,a[1])}}return n}},Yt={create:function(){return[0,0]},length:function(t){var e=t[0],n=t[1];return Math.sqrt(e*e+n*n)},normalize:function(t,e){var n=this.length(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t},add:function(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t},sub:function(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t},scale:function(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},direction:function(t,e){return t[0]*e[1]-e[0]*t[1]},angle:function(t,e){var n=this.dot(t,e)/(this.length(t)*this.length(e));return Math.acos(n)},angleTo:function(t,e,n){var i=this.angle(t,e),r=this.direction(t,e)>=0;return n?r?2*Math.PI-i:i:r?i:2*Math.PI-i},zero:function(t){return 0===t[0]&&0===t[1]},distance:function(t,e){var n=e[0]-t[0],i=e[1]-t[1];return Math.sqrt(n*n+i*i)},clone:function(t){return[t[0],t[1]]},min:function(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t},max:function(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t},transformMat2d:function(t,e,n){var i=e[0],r=e[1];return t[0]=n[0]*i+n[2]*r+n[4],t[1]=n[1]*i+n[3]*r+n[5],t}},Ot=[1,0,0,1,0,0],Et=function(){var t=e.prototype;function e(t){var e,n;this._initDefaultCfg(),D(this,t),this.plot?(e=this.plot.bl,n=this.plot.tr,this.start=e,this.end=n):(e=this.start,n=this.end),this.init(e,n)}return t._initDefaultCfg=function(){},t._scale=function(t,e){var n=this.matrix,i=this.center;It.translate(n,n,[i.x,i.y]),It.scale(n,n,[t,e]),It.translate(n,n,[-i.x,-i.y])},t.init=function(t,e){this.matrix=[].concat(Ot),this.center={x:(e.x-t.x)/2+t.x,y:(e.y-t.y)/2+t.y},this.scale&&this._scale(this.scale[0],this.scale[1])},t.convertPoint=function(t){var e=this._convertPoint(t),n=e.x,i=e.y;if(!It.isChanged(this.matrix))return{x:n,y:i};var r=[n,i];return Yt.transformMat2d(r,r,this.matrix),{x:r[0],y:r[1]}},t.invertPoint=function(t){return this._invertPoint(t)},t._convertPoint=function(t){return t},t._invertPoint=function(t){return t},t.reset=function(t){this.plot=t;var e=t.bl,n=t.tr;this.start=e,this.end=n,this.init(e,n)},e}(),Nt=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initDefaultCfg=function(){this.type="cartesian",this.transposed=!1,this.isRect=!0},n.init=function(e,n){t.prototype.init.call(this,e,n),this.x={start:e.x,end:n.x},this.y={start:e.y,end:n.y}},n._convertPoint=function(t){var e=this.transposed,n=e?"y":"x",i=e?"x":"y",r=this.x,a=this.y;return{x:r.start+(r.end-r.start)*t[n],y:a.start+(a.end-a.start)*t[i]}},n._invertPoint=function(t){var e=this.transposed,n=e?"y":"x",i=e?"x":"y",r=this.x,a=this.y,s={};return s[n]=(t.x-r.start)/(r.end-r.start),s[i]=(t.y-a.start)/(a.end-a.start),s},e}(Et);function Ft(t,e){return p(e)?e:t.invert(t.scale(e))}Et.Cartesian=Nt,Et.Rect=Nt;var zt=function(){function t(t){var e=this;this.type="base",this.name=null,this.method=null,this.values=[],this.scales=[],this.linear=null;var n=null,i=this.callback;if(t.callback){var r=t.callback;n=function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];var o=r.apply(void 0,n);return s(o)&&(o=i.apply(e,n)),o}}D(this,t),n&&D(this,{callback:n})}var e=t.prototype;return e._getAttrValue=function(t,e){var n=this.values;if(t.isCategory&&!this.linear)return n[t.translate(e)%n.length];var i=t.scale(e);return this.getLinearValue(i)},e.getLinearValue=function(t){var e=this.values,n=e.length-1,i=Math.floor(n*t),r=n*t-i,a=e[i];return a+((i===n?a:e[i+1])-a)*r},e.callback=function(t){var e=this.scales[0];return"identity"===e.type?e.value:this._getAttrValue(e,t)},e.getNames=function(){for(var t=this.scales,e=this.names,n=Math.min(t.length,e.length),i=[],r=0;r<n;r++)i.push(e[r]);return i},e.getFields=function(){var t=this.scales,e=[];return u(t,(function(t){e.push(t.field)})),e},e.getScale=function(t){return this.scales[this.names.indexOf(t)]},e.mapping=function(){for(var t=this.scales,e=this.callback,n=arguments.length,i=new Array(n),r=0;r<n;r++)i[r]=arguments[r];var a=i;if(e){for(var s=0,o=i.length;s<o;s++)i[s]=this._toOriginParam(i[s],t[s]);a=e.apply(this,i)}return a=[].concat(a)},e._toOriginParam=function(t,e){var n=t;if(!e.isLinear)if(o(t)){n=[];for(var i=0,r=t.length;i<r;i++)n.push(Ft(e,t[i]))}else n=Ft(e,t);return n},t}(),Bt=function(t){function e(e){var n;return(n=t.call(this,e)||this).names=["x","y"],n.type="position",n}return Ct(e,t),e.prototype.mapping=function(t,e){var n,i,r,a=this.scales,h=this.coord,l=a[0],c=a[1];if(s(t)||s(e))return[];if(o(e)&&o(t)){n=[],i=[];for(var f=0,g=0,p=t.length,d=e.length;f<p&&g<d;f++,g++)r=h.convertPoint({x:l.scale(t[f]),y:c.scale(e[g])}),n.push(r.x),i.push(r.y)}else if(o(e))t=l.scale(t),i=[],u(e,(function(e){e=c.scale(e),r=h.convertPoint({x:t,y:e}),n&&n!==r.x?(o(n)||(n=[n]),n.push(r.x)):n=r.x,i.push(r.y)}));else if(o(t))e=c.scale(e),n=[],u(t,(function(t){t=l.scale(t),r=h.convertPoint({x:t,y:e}),i&&i!==r.y?(o(i)||(i=[i]),i.push(r.y)):i=r.y,n.push(r.x)}));else{t=l.scale(t),e=c.scale(e);var v=h.convertPoint({x:t,y:e});n=v.x,i=v.y}return[n,i]},e}(zt),Lt=function(t){function e(e){var n;return(n=t.call(this,e)||this).names=["shape"],n.type="shape",n.gradient=null,n}return Ct(e,t),e.prototype.getLinearValue=function(t){var e=this.values;return e[Math.round((e.length-1)*t)]},e}(zt),Xt=function(t){function e(e){var n;return(n=t.call(this,e)||this).names=["size"],n.type="size",n.gradient=null,n}return Ct(e,t),e}(zt);function jt(t,e,n,i){return t[i]+(e[i]-t[i])*n}function Gt(t){return"#"+Rt(t[0])+Rt(t[1])+Rt(t[2])}function Rt(t){return 1===(t=(t=Math.round(t)).toString(16)).length&&(t="0"+t),t}var Ht={black:"#000000",blue:"#0000ff",grey:"#808080",green:"#008000",orange:"#ffa500",pink:"#ffc0cb",purple:"#800080",red:"#ff0000",white:"#ffffff",yellow:"#ffff00"};function Wt(t){var e=[];return p(t)&&(t=t.split("-")),u(t,(function(t){var n,i;-1===t.indexOf("#")&&(t=function(t){if(Ht[t])return Ht[t];if("#"===t[0]){if(7===t.length)return t;var e=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,(function(t,e,n,i){return"#"+e+e+n+n+i+i}));return Ht[t]=e,e}var n=t.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i);return n.shift(),n=Gt(n),Ht[t]=n,n}(t)),e.push((n=t,(i=[]).push(parseInt(n.substr(1,2),16)),i.push(parseInt(n.substr(3,2),16)),i.push(parseInt(n.substr(5,2),16)),i))})),function(t){return function(t,e){var n=t.length-1,i=Math.floor(n*e),r=n*e-i,a=t[i],s=i===n?a:t[i+1];return Gt([jt(a,s,r,0),jt(a,s,r,1),jt(a,s,r,2)])}(e,t)}}var Vt=function(t){function e(e){var n;return(n=t.call(this,e)||this).names=["color"],n.type="color",n.gradient=null,p(n.values)&&(n.linear=!0),n}return Ct(e,t),e.prototype.getLinearValue=function(t){var e=this.gradient;e||(e=Wt(this.values),this.gradient=e);return e(t)},e}(zt),qt=Object.freeze({__proto__:null,Position:Bt,Shape:Lt,Size:Xt,Color:Vt}),Zt={},Ut={_coord:null,draw:function(t,e){this.drawShape&&this.drawShape(t,e)},setCoord:function(t){this._coord=t},parsePoint:function(t){var e=this._coord;return e.isPolar&&(1===t.x&&(t.x=.9999999),1===t.y&&(t.y=.9999999)),e.convertPoint(t)},parsePoints:function(t){if(!t)return!1;var e=this,n=[];return t.forEach((function(t){n.push(e.parsePoint(t))})),n}},Jt={defaultShapeType:null,setCoord:function(t){this._coord=t},getShape:function(t){o(t)&&(t=t[0]);var e=this[t]||this[this.defaultShapeType];return e._coord=this._coord,e},getShapePoints:function(t,e){var n=this.getShape(t);return(n.getPoints||n.getShapePoints||this.getDefaultPoints)(e)},getDefaultPoints:function(){return[]},drawShape:function(t,e,n){var i=this.getShape(t);return e.color||(e.color=Mt.colors[0]),i.draw(e,n)}};function $t(t,e){for(var n in e)e.hasOwnProperty(n)&&"constructor"!==n&&void 0!==e[n]&&(t[n]=e[n])}Zt.registerFactory=function(t,e){var n=b(t),i=D({},Jt,e);return Zt[n]=i,i.name=t,i},Zt.registerShape=function(t,e,n){var i=b(t),r=Zt[i],a=D({},Ut,n);return r[e]=a,a},Zt.registShape=Zt.registerShape,Zt.getShapeFactory=function(t){return this[b(t=t||"point")]};var Kt=function(t,e,n,i){return e&&$t(t,e),n&&$t(t,n),i&&$t(t,i),t},Qt=function(){var t=e.prototype;function e(t){this._initDefaultCfg(),Kt(this,t)}return t._initDefaultCfg=function(){this.adjustNames=["x","y"]},t.processAdjust=function(){},e}();function te(t){var e=t.type,n=t.values;if("linear"===e)return w(St.scale.linear,t);if("cat"===e)return w(St.scale.cat,{values:n.slice(0,10).join(" ")});if("timeCat"===e){var i=t.getText(n[0]),r=t.getText(n[n.length-1]);return w(St.scale.timeCat,{start:i,end:r})}return""}var ee=["color","size","shape"];function ne(t){return o(t)?t:p(t)?t.split("*"):[t]}var ie=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n.getDefaultCfg=function(){return{type:null,data:null,attrs:{},scales:{},container:null,styleOptions:null,chart:null,shapeType:"",generatePoints:!1,attrOptions:{},sortable:!1,startOnZero:!0,visible:!0,connectNulls:!1,ignoreEmptyGroup:!1,isInit:!1}},n.init=function(){this.get("isInit")||(this._initAttrs(),this._processData(),this.set("isInit",!0))},n._getGroupScales=function(){var t=this,e=[];return u(ee,(function(n){var i=t.getAttr(n);i&&u(i.scales,(function(t){t&&t.isCategory&&-1===e.indexOf(t)&&e.push(t)}))})),e},n._groupData=function(t){var e=this.get("colDefs"),n=this._getGroupScales();if(n.length){var i={},r=[];return u(n,(function(t){var n=t.field;r.push(n),e&&e[n]&&e[n].values&&(i[t.field]=e[n].values)})),q(t,r,i)}return[t]},n._setAttrOptions=function(t,e){this.get("attrOptions")[t]=e;var n=this.get("attrs");Object.keys(n).length&&this._createAttr(t,e)},n._createAttrOption=function(t,e,n,i){var r={};r.field=e,n?a(n)?r.callback=n:r.values=n:r.values=i,this._setAttrOptions(t,r)},n._createAttr=function(t,e){var n=this.get("attrs"),i=this.get("coord"),r=b(t),a=ne(e.field);"position"===t&&(e.coord=i);for(var s=[],o=0,h=a.length;o<h;o++){var u=a[o],l=this._createScale(u);s.push(l)}if("position"===t){var c=s[1];"polar"===i.type&&i.transposed&&this.hasAdjust("stack")&&c.values.length&&c.change({nice:!1,min:0,max:Math.max.apply(null,c.values)})}e.scales=s;var f=new qt[r](e);return n[t]=f,f},n._initAttrs=function(){var t=this.get("attrOptions");for(var e in t)t.hasOwnProperty(e)&&this._createAttr(e,t[e])},n._createScale=function(t){var e=this.get("scales"),n=e[t];return n||(n=this.get("chart").createScale(t),e[t]=n),n},n._processData=function(){var t=this.get("data"),e=[],n=this._groupData(t);if(this.get("ignoreEmptyGroup")){var i=this.getYScale();n=n.filter((function(t){return t.some((function(t){return void 0!==t[i.field]}))}))}for(var r=0,a=n.length;r<a;r++){var s=n[r],o=this._saveOrigin(s);this.hasAdjust("dodge")&&this._numberic(o),e.push(o)}return this.get("adjust")&&this._adjustData(e),this.get("sortable")&&this._sort(e),this.emit("afterprocessdata",{dataArray:e}),this.set("mappingData",e),this.set("dataArray",e),e},n._saveOrigin=function(t){for(var e=[],n=0,i=t.length;n<i;n++){var r=t[n],a={};for(var s in r)a[s]=r[s];a._origin=r,e.push(a)}return e},n._numberic=function(t){for(var e=this.getAttr("position").scales,n=0,i=t.length;n<i;n++)for(var r=t[n],a=Math.min(2,e.length),s=0;s<a;s++){var o=e[s];if(o.isCategory){var h=o.field;r[h]=o.translate(r[h])}}},n._adjustData=function(t){var e=this.get("adjust");if(e){var n=b(e.type);if(!Qt[n])throw new Error("not support such adjust : "+e);var i=this.getXScale(),r=this.getYScale(),a=D({xField:i.field,yField:r.field},e);new Qt[n](a).processAdjust(t),"Stack"===n&&this._updateStackRange(r.field,r,t)}},n._updateStackRange=function(t,e,n){for(var i=R(n),r=e.min,a=e.max,s=0,o=i.length;s<o;s++){var h=i[s],u=Math.min.apply(null,h[t]),l=Math.max.apply(null,h[t]);u<r&&(r=u),l>a&&(a=l)}(r<e.min||a>e.max)&&e.change({min:r,max:a})},n._sort=function(t){var e=this.getXScale(),n=e.field,i=e.type;"identity"!==i&&e.values.length>1&&u(t,(function(t){t.sort((function(t,r){return"timeCat"===i?yt(t._origin[n])-yt(r._origin[n]):e.translate(t._origin[n])-e.translate(r._origin[n])}))})),this.set("hasSorted",!0),this.set("dataArray",t)},n.paint=function(){var t=this.get("mappingData"),e=[],n=this.getShapeFactory();n.setCoord(this.get("coord")),this._beforeMapping(t);for(var i=0,r=t.length;i<r;i++){var a=t[i];if(a.length){var s=this._mapping(a);e.push(s),this.draw(s,n)}}this.set("dataArray",e),this.generateAria()},n.getShapeFactory=function(){var t=this.get("shapeFactory");if(!t){var e=this.get("shapeType");t=Zt.getShapeFactory(e),this.set("shapeFactory",t)}return t},n._mapping=function(t){var e=this.get("attrs"),n=this.getYScale().field,i={},r=new Array(t.length);for(var a in e)if(e.hasOwnProperty(a))for(var s=e[a],h=s.names,u=s.scales,l=0,c=t.length;l<c;l++){var f=t[l],g=bt({},f,r[l]);if(g._originY=f[n],"position"===s.type)for(var p=this._getAttrValues(s,f),d=0,v=p.length;d<v;d++){var y=p[d];g[h[d]]=o(y)&&1===y.length?y[0]:y}else{var m=h[0],x=""+m+f[u[0].field],_=i[x];_||(_=this._getAttrValues(s,f),i[x]=_),g[m]=_[0]}r[l]=g}return r},n._getAttrValues=function(t,e){for(var n=t.scales,i=[],r=0,a=n.length;r<a;r++){var s=n[r],o=s.field;"identity"===s.type?i.push(s.value):i.push(e[o])}return t.mapping.apply(t,i)},n.getAttrValue=function(t,e){var n=this.getAttr(t),i=null;n&&(i=this._getAttrValues(n,e)[0]);return i},n._beforeMapping=function(t){this.get("generatePoints")&&this._generatePoints(t)},n.isInCircle=function(){var t=this.get("coord");return t&&t.isPolar},n.getCallbackCfg=function(t,e,n){if(!t)return e;var i={},r=t.map((function(t){return n[t]}));return u(e,(function(t,e){a(t)?i[e]=t.apply(null,r):i[e]=t})),i},n.getDrawCfg=function(t){var e=this.isInCircle(),n={origin:t,x:t.x,y:t.y,color:t.color,size:t.size,shape:t.shape,isInCircle:e,opacity:t.opacity},i=this.get("styleOptions");return i&&i.style&&(n.style=this.getCallbackCfg(i.fields,i.style,t._origin)),this.get("generatePoints")&&(n.points=t.points,n.nextPoints=t.nextPoints),e&&(n.center=this.get("coord").center),n},n.draw=function(t,e){var n=this,i=n.get("container"),r=n.getYScale();u(t,(function(t,a){if(!r||!s(t._origin[r.field])){t.index=a;var o=n.getDrawCfg(t),h=t.shape;n.drawShape(h,t,o,i,e)}}))},n.drawShape=function(t,e,n,i,r){var a=r.drawShape(t,n,i);a&&u([].concat(a),(function(t){t.set("origin",e)}))},n._generatePoints=function(t){var e=this,n=e.getShapeFactory(),i=e.getAttr("shape");u(t,(function(t){for(var r=0,a=t.length;r<a;r++){var s=t[r],o=e.createShapePointsCfg(s),h=i?e._getAttrValues(i,s):null,u=n.getShapePoints(h,o);s.points=u}})),u(t,(function(e,n){var i=t[n+1];i&&(e[0].nextPoints=i[0].points)}))},n.generateAria=function(){var t=this.get("container");if(t.get("aria")){var e=[],n=this.get("coord"),i=this.getXScale(),r=this.getYScale(),a=function(t,e,n){var i=t.type;return St.coord[i]?w(St.coord[i],{xLabel:te(e),yLabel:te(n)}):""}(n,i,r);e.push(a);var s=St.geometry,o=s.prefix,h=s.oneData,l=s.partData,c=s.allData,f=this.get("dataArray"),g=f.length,p=this._getGroupScales()[0];if(p){var d=w(o,{count:g});e.push(d),u(f,(function(t,n){var a=t.length;if(a){var s=t[0]._origin;if(1===a)e.push(w(h,{index:n+1,count:a,name:s[p.field],values:s[r.field]}));else{var o=a>5?l:c,u=t.slice(0,5).map((function(t){var e=t._origin;return i.getText(e[i.field])+":"+r.getText(e[r.field])}));e.push(w(o,{index:n+1,count:a,part:3,name:s[p.field],values:u.join(" ")}))}}}))}t.set("ariaLabel",e.join(""))}},n.createShapePointsCfg=function(t){var e=this.getXScale(),n=this.getYScale();return{x:this._normalizeValues(t[e.field],e),y:n?this._normalizeValues(t[n.field],n):t.y?t.y:.1,y0:n?n.scale(this.getYMinValue()):void 0}},n.getYMinValue=function(){var t=this.getYScale(),e=t.min,n=t.max;return this.get("startOnZero")?n<=0&&e<=0?n:e>=0?e:0:e},n._normalizeValues=function(t,e){var n=[];if(o(t))for(var i=0,r=t.length;i<r;i++){var a=t[i];n.push(e.scale(a))}else n=e.scale(t);return n},n.getAttr=function(t){return this.get("attrs")[t]},n.getXScale=function(){return this.getAttr("position").scales[0]},n.getYScale=function(){return this.getAttr("position").scales[1]},n.hasAdjust=function(t){return this.get("adjust")&&this.get("adjust").type===t},n._getSnap=function(t,e,n){var i,r=0,a=this.getYScale().field;if(this.hasAdjust("stack")&&t.field===a){i=[],n.forEach((function(t){i.push(t._originY)}));for(var s=i.length;r<s&&!(i[0][0]>e);r++){if(i[i.length-1][1]<=e){r=i.length-1;break}if(i[r][0]<=e&&i[r][1]>e)break}}else{(i=t.values).sort((function(t,e){return t-e}));for(var o=i.length;r<o&&!(o<=1)&&!((i[0]+i[1])/2>e)&&!((i[r-1]+i[r])/2<=e&&(i[r+1]+i[r])/2>e);r++)if((i[i.length-2]+i[i.length-1])/2<=e){r=i.length-1;break}}return i[r]},n.getSnapRecords=function(t){var e=this,n=e.get("coord"),i=e.getXScale(),r=e.getYScale(),a=i.field,h=e.get("dataArray");this.get("hasSorted")||this._sort(h);var u=[],l=n.invertPoint(t),c=l.x;if(e.isInCircle()&&!n.transposed&&c>(1+i.rangeMax())/2&&(c=i.rangeMin()),i.isCategory){var f=i.rangeMin(),g=i.rangeMax();c<f&&(c=f),c>g&&(c=g)}var p=i.invert(c);i.isCategory||(p=e._getSnap(i,p));var d=[];if(h.forEach((function(t){t.forEach((function(t){var n=s(t._origin)?t[a]:t._origin[a];e._isEqual(n,p,i)&&d.push(t)}))})),this.hasAdjust("stack")&&n.isPolar&&n.transposed){if(c>=0&&c<=1){var v=r.invert(l.y);v=e._getSnap(r,v,d),d.forEach((function(t){(o(v)?t._originY.toString()===v.toString():t._originY===v)&&u.push(t)}))}}else u=d;return u},n.getRecords=function(t){var e=this,n=this.getXScale(),i=this.get("dataArray"),r=n.field;return i.map((function(i){for(var a=i.length-1;a>=0;a--){var o=i[a],h=s(o._origin)?o[r]:o._origin[r];if(e._isEqual(h,t,n))return o}return null}))},n._isEqual=function(t,e,n){return"timeCat"===n.type?yt(t)===e:e===t},n.position=function(t){return this._setAttrOptions("position",{field:t}),this},n.color=function(t,e){return this._createAttrOption("color",t,e,Mt.colors),this},n.size=function(t,e){return this._createAttrOption("size",t,e,Mt.sizes),this},n.shape=function(t,e){var n=this.get("type"),i=Mt.shapes[n]||[];return this._createAttrOption("shape",t,e,i),this},n.style=function(t,e){var n,i=this.get("styleOptions");return i||(i={},this.set("styleOptions",i)),h(t)&&(e=t,t=null),t&&(n=ne(t)),i.fields=n,i.style=e,this},n.adjust=function(t){return p(t)&&(t={type:t}),this.set("adjust",t),this},n.animate=function(t){return this.set("animateCfg",t),this},n.changeData=function(t){this.set("data",t),this.set("scales",{}),this.get("isInit")&&(this.set("isInit",!1),this.init())},n.clearInner=function(){var t=this.get("container");t&&t.clear()},n.reset=function(){this.set("isInit",!1),this.set("attrs",{}),this.set("attrOptions",{}),this.set("adjust",null),this.clearInner()},n.clear=function(){this.clearInner()},n.destroy=function(){this.set("isInit",!1),this.clear(),t.prototype.destroy.call(this)},n._display=function(t){this.set("visible",t);var e=this.get("container"),n=e.get("canvas");e.set("visible",t),n.draw()},n.show=function(){this._display(!0)},n.hide=function(){this._display(!1)},e}(At),re={};function ae(t){return re[t]}function se(t,e){re[t]=e}var oe=function(){function t(t){this.type="base",this.isCategory=!1,this.isLinear=!1,this.isContinuous=!1,this.isIdentity=!1,this.values=[],this.range=[0,1],this.ticks=[],this.__cfg__=t,this.initCfg(),this.init()}return t.prototype.translate=function(t){return t},t.prototype.change=function(t){D(this.__cfg__,t),this.init()},t.prototype.clone=function(){return this.constructor(this.__cfg__)},t.prototype.getTicks=function(){var t=this;return function(t,n){if(!e(t))return t;for(var i=[],r=0;r<t.length;r++){var a=t[r];i.push(n(a,r))}return i}(this.ticks,(function(e,n){return h(e)?e:{text:t.getText(e,n),tickValue:e,value:t.scale(e)}}))},t.prototype.getText=function(t,e){var n=this.formatter,i=n?n(t,e):t;return s(i)||!a(i.toString)?"":i.toString()},t.prototype.getConfig=function(t){return this.__cfg__[t]},t.prototype.init=function(){D(this,this.__cfg__),this.setDomain(),N(this.getConfig("ticks"))&&(this.ticks=this.calculateTicks())},t.prototype.initCfg=function(){},t.prototype.setDomain=function(){},t.prototype.calculateTicks=function(){var t=this.tickMethod,e=[];if(p(t)){var n=ae(t);if(!n)throw new Error("There is no method to to calculate ticks!");e=n(this)}else a(t)&&(e=t(this));return e},t.prototype.rangeMin=function(){return this.range[0]},t.prototype.rangeMax=function(){return this.range[1]},t.prototype.calcPercent=function(t,e,n){return x(t)?(t-e)/(n-e):NaN},t.prototype.calcValue=function(t,e,n){return e+t*(n-e)},t}(),he=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="cat",e.isCategory=!0,e}return L(e,t),e.prototype.buildIndexMap=function(){if(!this.translateIndexMap){this.translateIndexMap=new Map;for(var t=0;t<this.values.length;t++)this.translateIndexMap.set(this.values[t],t)}},e.prototype.translate=function(t){this.buildIndexMap();var e=this.translateIndexMap.get(t);return void 0===e&&(e=x(t)?t:NaN),e},e.prototype.scale=function(t){var e=this.translate(t),n=this.calcPercent(e,this.min,this.max);return this.calcValue(n,this.rangeMin(),this.rangeMax())},e.prototype.invert=function(t){var e=this.max-this.min,n=this.calcPercent(t,this.rangeMin(),this.rangeMax()),i=Math.round(e*n)+this.min;return i<this.min||i>this.max?NaN:this.values[i]},e.prototype.getText=function(e){for(var n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];var r=e;return x(e)&&!this.values.includes(e)&&(r=this.values[r]),t.prototype.getText.apply(this,G([r],n))},e.prototype.initCfg=function(){this.tickMethod="cat"},e.prototype.setDomain=function(){if(s(this.getConfig("min"))&&(this.min=0),s(this.getConfig("max"))){var t=this.values.length;this.max=t>1?t-1:t}this.translateIndexMap&&(this.translateIndexMap=void 0)},e}(oe),ue=/d{1,4}|M{1,4}|YY(?:YY)?|S{1,3}|Do|ZZ|Z|([HhMsDm])\1?|[aA]|"[^"]*"|'[^']*'/g,le="[^\\s]+",ce=/\[([^]*?)\]/gm;function fe(t,e){for(var n=[],i=0,r=t.length;i<r;i++)n.push(t[i].substr(0,e));return n}var ge=function(t){return function(e,n){var i=n[t].map((function(t){return t.toLowerCase()})).indexOf(e.toLowerCase());return i>-1?i:null}};function pe(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];for(var i=0,r=e;i<r.length;i++){var a=r[i];for(var s in a)t[s]=a[s]}return t}var de=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],ve=["January","February","March","April","May","June","July","August","September","October","November","December"],ye=fe(ve,3),me={dayNamesShort:fe(de,3),dayNames:de,monthNamesShort:ye,monthNames:ve,amPm:["am","pm"],DoFn:function(t){return t+["th","st","nd","rd"][t%10>3?0:(t-t%10!=10?1:0)*t%10]}},xe=pe({},me),_e=function(t){return xe=pe(xe,t)},Se=function(t){return t.replace(/[|\\{()[^$+*?.-]/g,"\\$&")},Me=function(t,e){for(void 0===e&&(e=2),t=String(t);t.length<e;)t="0"+t;return t},we={D:function(t){return String(t.getDate())},DD:function(t){return Me(t.getDate())},Do:function(t,e){return e.DoFn(t.getDate())},d:function(t){return String(t.getDay())},dd:function(t){return Me(t.getDay())},ddd:function(t,e){return e.dayNamesShort[t.getDay()]},dddd:function(t,e){return e.dayNames[t.getDay()]},M:function(t){return String(t.getMonth()+1)},MM:function(t){return Me(t.getMonth()+1)},MMM:function(t,e){return e.monthNamesShort[t.getMonth()]},MMMM:function(t,e){return e.monthNames[t.getMonth()]},YY:function(t){return Me(String(t.getFullYear()),4).substr(2)},YYYY:function(t){return Me(t.getFullYear(),4)},h:function(t){return String(t.getHours()%12||12)},hh:function(t){return Me(t.getHours()%12||12)},H:function(t){return String(t.getHours())},HH:function(t){return Me(t.getHours())},m:function(t){return String(t.getMinutes())},mm:function(t){return Me(t.getMinutes())},s:function(t){return String(t.getSeconds())},ss:function(t){return Me(t.getSeconds())},S:function(t){return String(Math.round(t.getMilliseconds()/100))},SS:function(t){return Me(Math.round(t.getMilliseconds()/10),2)},SSS:function(t){return Me(t.getMilliseconds(),3)},a:function(t,e){return t.getHours()<12?e.amPm[0]:e.amPm[1]},A:function(t,e){return t.getHours()<12?e.amPm[0].toUpperCase():e.amPm[1].toUpperCase()},ZZ:function(t){var e=t.getTimezoneOffset();return(e>0?"-":"+")+Me(100*Math.floor(Math.abs(e)/60)+Math.abs(e)%60,4)},Z:function(t){var e=t.getTimezoneOffset();return(e>0?"-":"+")+Me(Math.floor(Math.abs(e)/60),2)+":"+Me(Math.abs(e)%60,2)}},be=function(t){return+t-1},Ce=[null,"\\d\\d?"],Pe=[null,le],ke=["isPm",le,function(t,e){var n=t.toLowerCase();return n===e.amPm[0]?0:n===e.amPm[1]?1:null}],Te=["timezoneOffset","[^\\s]*?[\\+\\-]\\d\\d:?\\d\\d|[^\\s]*?Z?",function(t){var e=(t+"").match(/([+-]|\d\d)/gi);if(e){var n=60*+e[1]+parseInt(e[2],10);return"+"===e[0]?n:-n}return 0}],Ae={D:["day","\\d\\d?"],DD:["day","\\d\\d"],Do:["day","\\d\\d?"+le,function(t){return parseInt(t,10)}],M:["month","\\d\\d?",be],MM:["month","\\d\\d",be],YY:["year","\\d\\d",function(t){var e=+(""+(new Date).getFullYear()).substr(0,2);return+(""+(+t>68?e-1:e)+t)}],h:["hour","\\d\\d?",void 0,"isPm"],hh:["hour","\\d\\d",void 0,"isPm"],H:["hour","\\d\\d?"],HH:["hour","\\d\\d"],m:["minute","\\d\\d?"],mm:["minute","\\d\\d"],s:["second","\\d\\d?"],ss:["second","\\d\\d"],YYYY:["year","\\d{4}"],S:["millisecond","\\d",function(t){return 100*+t}],SS:["millisecond","\\d\\d",function(t){return 10*+t}],SSS:["millisecond","\\d{3}"],d:Ce,dd:Ce,ddd:Pe,dddd:Pe,MMM:["month",le,ge("monthNamesShort")],MMMM:["month",le,ge("monthNames")],a:ke,A:ke,ZZ:Te,Z:Te},De={default:"ddd MMM DD YYYY HH:mm:ss",shortDate:"M/D/YY",mediumDate:"MMM D, YYYY",longDate:"MMMM D, YYYY",fullDate:"dddd, MMMM D, YYYY",isoDate:"YYYY-MM-DD",isoDateTime:"YYYY-MM-DDTHH:mm:ssZ",shortTime:"HH:mm",mediumTime:"HH:mm:ss",longTime:"HH:mm:ss.SSS"},Ie=function(t){return pe(De,t)},Ye=function(t,e,n){if(void 0===e&&(e=De.default),void 0===n&&(n={}),"number"==typeof t&&(t=new Date(t)),"[object Date]"!==Object.prototype.toString.call(t)||isNaN(t.getTime()))throw new Error("Invalid Date pass to format");var i=[];e=(e=De[e]||e).replace(ce,(function(t,e){return i.push(e),"@@@"}));var r=pe(pe({},xe),n);return(e=e.replace(ue,(function(e){return we[e](t,r)}))).replace(/@@@/g,(function(){return i.shift()}))};function Oe(t,e,n){if(void 0===n&&(n={}),"string"!=typeof e)throw new Error("Invalid format in fecha parse");if(e=De[e]||e,t.length>1e3)return null;var i={year:(new Date).getFullYear(),month:0,day:1,hour:0,minute:0,second:0,millisecond:0,isPm:null,timezoneOffset:null},r=[],a=[],s=e.replace(ce,(function(t,e){return a.push(Se(e)),"@@@"})),o={},h={};s=Se(s).replace(ue,(function(t){var e=Ae[t],n=e[0],i=e[1],a=e[3];if(o[n])throw new Error("Invalid format. "+n+" specified twice in format");return o[n]=!0,a&&(h[a]=!0),r.push(e),"("+i+")"})),Object.keys(h).forEach((function(t){if(!o[t])throw new Error("Invalid format. "+t+" is required in specified format")})),s=s.replace(/@@@/g,(function(){return a.shift()}));var u=t.match(new RegExp(s,"i"));if(!u)return null;for(var l,c=pe(pe({},xe),n),f=1;f<u.length;f++){var g=r[f-1],p=g[0],d=g[2],v=d?d(u[f],c):+u[f];if(null==v)return null;i[p]=v}if(1===i.isPm&&null!=i.hour&&12!=+i.hour?i.hour=+i.hour+12:0===i.isPm&&12==+i.hour&&(i.hour=0),null==i.timezoneOffset){l=new Date(i.year,i.month,i.day,i.hour,i.minute,i.second,i.millisecond);for(var y=[["month","getMonth"],["day","getDate"],["hour","getHours"],["minute","getMinutes"],["second","getSeconds"]],m=(f=0,y.length);f<m;f++)if(o[y[f][0]]&&i[y[f][0]]!==l[y[f][1]]())return null}else if(l=new Date(Date.UTC(i.year,i.month,i.day,i.hour,i.minute-i.timezoneOffset,i.second,i.millisecond)),i.month>11||i.month<0||i.day>31||i.day<1||i.hour>23||i.hour<0||i.minute>59||i.minute<0||i.second>59||i.second<0)return null;return l}var Ee={format:Ye,parse:Oe,defaultI18n:me,setGlobalDateI18n:_e,setGlobalDateMasks:Ie},Ne=Object.freeze({__proto__:null,default:Ee,assign:pe,format:Ye,parse:Oe,defaultI18n:me,setGlobalDateI18n:_e,setGlobalDateMasks:Ie});function Fe(t,e){return(Ne.format||Ee.format)(t,e)}function ze(t){return p(t)&&(t=t.indexOf("T")>0?new Date(t).getTime():new Date(t.replace(/-/gi,"/")).getTime()),k(t)&&(t=t.getTime()),t}var Be=36e5,Le=24*Be,Xe=31*Le,je=[["HH:mm:ss",1e3],["HH:mm:ss",1e4],["HH:mm:ss",3e4],["HH:mm",6e4],["HH:mm",6e5],["HH:mm",18e5],["HH",Be],["HH",6*Be],["HH",12*Be],["YYYY-MM-DD",Le],["YYYY-MM-DD",4*Le],["YYYY-WW",7*Le],["YYYY-MM",Xe],["YYYY-MM",4*Xe],["YYYY-MM",6*Xe],["YYYY",380*Le]];function Ge(t,e,n){var i,r=(i=function(t){return t[1]},function(t,e,n,r){for(var a=s(n)?0:n,o=s(r)?t.length:r;a<o;){var h=a+o>>>1;i(t[h])>e?o=h:a=h+1}return a})(je,(e-t)/n)-1,a=je[r];return r<0?a=je[0]:r>=je.length&&(a=y(je)),a}var Re=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="timeCat",e}return L(e,t),e.prototype.translate=function(t){t=ze(t);var e=this.values.indexOf(t);return-1===e&&(e=x(t)&&t<this.values.length?t:NaN),e},e.prototype.getText=function(t,e){var n=this.translate(t);if(n>-1){var i=this.values[n],r=this.formatter;return i=r?r(i,e):Fe(i,this.mask)}return t},e.prototype.initCfg=function(){this.tickMethod="time-cat",this.mask="YYYY-MM-DD",this.tickCount=7},e.prototype.setDomain=function(){var e=this.values;u(e,(function(t,n){e[n]=ze(t)})),e.sort((function(t,e){return t-e})),t.prototype.setDomain.call(this)},e}(he),He=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.isContinuous=!0,e}return L(e,t),e.prototype.scale=function(t){if(s(t))return NaN;var e=this.rangeMin(),n=this.rangeMax();return this.max===this.min?e:e+this.getScalePercent(t)*(n-e)},e.prototype.init=function(){t.prototype.init.call(this);var e=this.ticks,n=v(e),i=y(e);n<this.min&&(this.min=n),i>this.max&&(this.max=i),s(this.minLimit)||(this.min=n),s(this.maxLimit)||(this.max=i)},e.prototype.setDomain=function(){var t=g(this.values),e=t.min,n=t.max;s(this.min)&&(this.min=e),s(this.max)&&(this.max=n),this.min>this.max&&(this.min=e,this.max=n)},e.prototype.calculateTicks=function(){var e=this,i=t.prototype.calculateTicks.call(this);return this.nice||(i=n(i,(function(t){return t>=e.min&&t<=e.max}))),i},e.prototype.getScalePercent=function(t){var e=this.max,n=this.min;return(t-n)/(e-n)},e.prototype.getInvertPercent=function(t){return(t-this.rangeMin())/(this.rangeMax()-this.rangeMin())},e}(oe),We=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="linear",e.isLinear=!0,e}return L(e,t),e.prototype.invert=function(t){var e=this.getInvertPercent(t);return this.min+e*(this.max-this.min)},e.prototype.initCfg=function(){this.tickMethod="wilkinson-extended",this.nice=!1},e}(He);function Ve(t,e){var n=Math.E;return e>=0?Math.pow(n,Math.log(e)/t):-1*Math.pow(n,Math.log(-e)/t)}function qe(t,e){return 1===t?1:Math.log(e)/Math.log(t)}function Ze(t,e,n){s(n)&&(n=Math.max.apply(null,t));var i=n;return u(t,(function(t){t>0&&t<i&&(i=t)})),i===n&&(i=n/e),i>1&&(i=1),i}var Ue=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="log",e}return L(e,t),e.prototype.invert=function(t){var e,n=this.base,i=qe(n,this.max),r=this.rangeMin(),a=this.rangeMax()-r,s=this.positiveMin;if(s){if(0===t)return 0;var o=1/(i-(e=qe(n,s/n)))*a;if(t<o)return t/o*s}else e=qe(n,this.min);var h=(t-r)/a*(i-e)+e;return Math.pow(n,h)},e.prototype.initCfg=function(){this.tickMethod="log",this.base=10,this.tickCount=6,this.nice=!0},e.prototype.setDomain=function(){t.prototype.setDomain.call(this);var e=this.min;if(e<0)throw new Error("When you use log scale, the minimum value must be greater than zero!");0===e&&(this.positiveMin=Ze(this.values,this.base,this.max))},e.prototype.getScalePercent=function(t){var e=this.max,n=this.min;if(e===n)return 0;if(t<=0)return 0;var i=this.base,r=this.positiveMin;return r&&(n=1*r/i),t<r?t/r/(qe(i,e)-qe(i,n)):(qe(i,t)-qe(i,n))/(qe(i,e)-qe(i,n))},e}(He),Je=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="pow",e}return L(e,t),e.prototype.invert=function(t){var e=this.getInvertPercent(t),n=this.exponent,i=Ve(n,this.max),r=Ve(n,this.min),a=e*(i-r)+r,s=a>=0?1:-1;return Math.pow(a,n)*s},e.prototype.initCfg=function(){this.tickMethod="pow",this.exponent=2,this.tickCount=5,this.nice=!0},e.prototype.getScalePercent=function(t){var e=this.max,n=this.min;if(e===n)return 0;var i=this.exponent;return(Ve(i,t)-Ve(i,n))/(Ve(i,e)-Ve(i,n))},e}(He),$e=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="time",e}return L(e,t),e.prototype.getText=function(t,e){var n=this.translate(t),i=this.formatter;return i?i(n,e):Fe(n,this.mask)},e.prototype.scale=function(e){var n=e;return(p(n)||k(n))&&(n=this.translate(n)),t.prototype.scale.call(this,n)},e.prototype.translate=function(t){return ze(t)},e.prototype.initCfg=function(){this.tickMethod="time-pretty",this.mask="YYYY-MM-DD",this.tickCount=7,this.nice=!1},e.prototype.setDomain=function(){var t=this.values,e=this.getConfig("min"),n=this.getConfig("max");if(s(e)&&x(e)||(this.min=this.translate(this.min)),s(n)&&x(n)||(this.max=this.translate(this.max)),t&&t.length){var i=[],r=1/0,a=r,o=0;u(t,(function(t){var e=ze(t);if(isNaN(e))throw new TypeError("Invalid Time: "+t+" in time scale!");r>e?(a=r,r=e):a>e&&(a=e),o<e&&(o=e),i.push(e)})),t.length>1&&(this.minTickInterval=a-r),s(e)&&(this.min=r),s(n)&&(this.max=o)}},e}(We),Ke=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="quantize",e}return L(e,t),e.prototype.invert=function(t){var e=this.ticks,n=e.length,i=this.getInvertPercent(t),r=Math.floor(i*(n-1));if(r>=n-1)return y(e);if(r<0)return v(e);var a=e[r],s=r/(n-1);return a+(i-s)/((r+1)/(n-1)-s)*(e[r+1]-a)},e.prototype.initCfg=function(){this.tickMethod="r-pretty",this.tickCount=5,this.nice=!0},e.prototype.calculateTicks=function(){var e=t.prototype.calculateTicks.call(this);return this.nice||(y(e)!==this.max&&e.push(this.max),v(e)!==this.min&&e.unshift(this.min)),e},e.prototype.getScalePercent=function(t){var e=this.ticks;if(t<v(e))return 0;if(t>y(e))return 1;var n=0;return u(e,(function(e,i){if(!(t>=e))return!1;n=i})),n/(e.length-1)},e}(He),Qe=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="quantile",e}return L(e,t),e.prototype.initCfg=function(){this.tickMethod="quantile",this.tickCount=5,this.nice=!0},e}(Ke),tn={};function en(t){return tn[t]}function nn(t,e){if(en(t))throw new Error("type '"+t+"' existed.");tn[t]=e}var rn=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="identity",e.isIdentity=!0,e}return L(e,t),e.prototype.calculateTicks=function(){return this.values},e.prototype.scale=function(t){return this.values[0]!==t&&x(t)?t:this.range[0]},e.prototype.invert=function(t){var e=this.range;return t<e[0]||t>e[1]?NaN:this.values[0]},e}(oe);function an(t){var e=t.values,i=t.tickInterval,r=t.tickCount,a=t.showLast;if(x(i)){var o=n(e,(function(t,e){return e%i==0})),h=y(e);return a&&y(o)!==h&&o.push(h),o}var u=e.length,l=t.min,c=t.max;if(s(l)&&(l=0),s(c)&&(c=e.length-1),!x(r)||r>=u)return e.slice(l,c+1);if(r<=0||c<=0)return[];for(var f=1===r?u:Math.floor(u/(r-1)),g=[],p=l,d=0;d<r&&!(p>=c);d++)p=Math.min(l+d*f,c),d===r-1&&a?g.push(e[c]):g.push(e[p]);return g}var sn=Math.sqrt(50),on=Math.sqrt(10),hn=Math.sqrt(2),un=function(){function t(){this._domain=[0,1]}return t.prototype.domain=function(t){return t?(this._domain=Array.from(t,Number),this):this._domain.slice()},t.prototype.nice=function(t){var e,n;void 0===t&&(t=5);var i,r=this._domain.slice(),a=0,s=this._domain.length-1,o=this._domain[a],h=this._domain[s];return h<o&&(o=(e=[h,o])[0],h=e[1],a=(n=[s,a])[0],s=n[1]),(i=ln(o,h,t))>0?i=ln(o=Math.floor(o/i)*i,h=Math.ceil(h/i)*i,t):i<0&&(i=ln(o=Math.ceil(o*i)/i,h=Math.floor(h*i)/i,t)),i>0?(r[a]=Math.floor(o/i)*i,r[s]=Math.ceil(h/i)*i,this.domain(r)):i<0&&(r[a]=Math.ceil(o*i)/i,r[s]=Math.floor(h*i)/i,this.domain(r)),this},t.prototype.ticks=function(t){return void 0===t&&(t=5),function(t,e,n){var i,r,a,s,o=-1;if(n=+n,(t=+t)===(e=+e)&&n>0)return[t];(i=e<t)&&(r=t,t=e,e=r);if(0===(s=ln(t,e,n))||!isFinite(s))return[];if(s>0)for(t=Math.ceil(t/s),e=Math.floor(e/s),a=new Array(r=Math.ceil(e-t+1));++o<r;)a[o]=(t+o)*s;else for(t=Math.floor(t*s),e=Math.ceil(e*s),a=new Array(r=Math.ceil(t-e+1));++o<r;)a[o]=(t-o)/s;i&&a.reverse();return a}(this._domain[0],this._domain[this._domain.length-1],t||5)},t}();function ln(t,e,n){var i=(e-t)/Math.max(0,n),r=Math.floor(Math.log(i)/Math.LN10),a=i/Math.pow(10,r);return r>=0?(a>=sn?10:a>=on?5:a>=hn?2:1)*Math.pow(10,r):-Math.pow(10,-r)/(a>=sn?10:a>=on?5:a>=hn?2:1)}function cn(t,e,n){return("ceil"===n?Math.ceil(t/e):"floor"===n?Math.floor(t/e):Math.round(t/e))*e}function fn(t,e,n){var i=cn(t,n,"floor"),r=cn(e,n,"ceil");i=m(i,n),r=m(r,n);for(var a=[],s=Math.max((r-i)/(Math.pow(2,12)-1),n),o=i;o<=r;o+=s){var h=m(o,s);a.push(h)}return{min:i,max:r,ticks:a}}function gn(t,e,n){var i,r=t.minLimit,a=t.maxLimit,o=t.min,h=t.max,u=t.tickCount,l=void 0===u?5:u,c=s(r)?s(e)?o:e:r,f=s(a)?s(n)?h:n:a;if(c>f&&(f=(i=[c,f])[0],c=i[1]),l<=2)return[c,f];for(var g=(f-c)/(l-1),p=[],d=0;d<l;d++)p.push(c+g*d);return p}function pn(t){return Math.abs(t)<1e-15?t:parseFloat(t.toFixed(15))}var dn=[1,5,2,2.5,4,3],vn=100*Number.EPSILON;function yn(t,e,n,i,r,a){var s=z(e),o=O(e,t),h=0,u=function(t,e){return(t%e+e)%e}(i,a);return(u<vn||a-u<vn)&&i<=0&&r>=0&&(h=1),1-o/(s-1)-n+h}function mn(t,e,n){var i=z(e);return 1-O(e,t)/(i-1)-n+1}function xn(t,e,n,i,r,a){var s=(t-1)/(a-r),o=(e-1)/(Math.max(a,i)-Math.min(n,r));return 2-Math.max(s/o,o/s)}function _n(t,e){return t>=e?2-(t-1)/(e-1):1}function Sn(t,e,n,i){var r=e-t;return 1-.5*(Math.pow(e-i,2)+Math.pow(t-n,2))/Math.pow(.1*r,2)}function Mn(t,e,n){var i=e-t;if(n>i){var r=(n-i)/2;return 1-Math.pow(r,2)/Math.pow(.1*i,2)}return 1}function wn(t,e,n,i,r,a){void 0===n&&(n=5),void 0===i&&(i=!0),void 0===r&&(r=dn),void 0===a&&(a=[.25,.2,.5,.05]);var s=n<0?0:Math.round(n);if(Number.isNaN(t)||Number.isNaN(e)||"number"!=typeof t||"number"!=typeof e||!s)return{min:0,max:0,ticks:[]};if(e-t<1e-15||1===s)return{min:t,max:e,ticks:[t]};if(e-t>1e148){var o=(e-t)/(w=n||5);return{min:t,max:e,ticks:Array(w).fill(null).map((function(e,n){return pn(t+o*n)}))}}for(var h={score:-2,lmin:0,lmax:0,lstep:0},u=1;u<1/0;){for(var l=0;l<r.length;l+=1){var c=r[l],f=mn(c,r,u);if(a[0]*f+a[1]+a[2]+a[3]<h.score){u=1/0;break}for(var g=2;g<1/0;){var p=_n(g,s);if(a[0]*f+a[1]+a[2]*p+a[3]<h.score)break;for(var d=(e-t)/(g+1)/u/c,m=Math.ceil(Math.log10(d));m<1/0;){var x=u*c*Math.pow(10,m),_=Mn(t,e,x*(g-1));if(a[0]*f+a[1]*_+a[2]*p+a[3]<h.score)break;var S=Math.floor(e/x)*u-(g-1)*u,M=Math.ceil(t/x)*u;if(S<=M)for(var w=M-S,b=0;b<=w;b+=1){var C=(S+b)*(x/u),P=C+x*(g-1),k=x,T=yn(c,r,u,C,P,k),A=Sn(t,e,C,P),D=xn(g,s,t,e,C,P),I=a[0]*T+a[1]*A+a[2]*D+1*a[3];I>h.score&&(!i||C<=t&&P>=e)&&(h.lmin=C,h.lmax=P,h.lstep=k,h.score=I)}m+=1}g+=1}}u+=1}var Y=pn(h.lmax),O=pn(h.lmin),E=pn(h.lstep),N=Math.floor(function(t){return Math.round(1e12*t)/1e12}((Y-O)/E))+1,F=new Array(N);F[0]=pn(O);for(l=1;l<N;l++)F[l]=pn(F[l-1]+E);return{min:Math.min(t,v(F)),max:Math.max(e,y(F)),ticks:F}}function bn(t,e,n){if(void 0===n&&(n=5),t===e)return{max:e,min:t,ticks:[t]};var i=n<0?0:Math.round(n);if(0===i)return{max:e,min:t,ticks:[]};var r=(e-t)/i,a=Math.pow(10,Math.floor(Math.log10(r))),s=a;2*a-r<1.5*(r-s)&&5*a-r<2.75*(r-(s=2*a))&&10*a-r<1.5*(r-(s=5*a))&&(s=10*a);for(var o=Math.ceil(e/s),h=Math.floor(t/s),u=Math.max(o*s,e),l=Math.min(h*s,t),c=Math.floor((u-l)/s)+1,f=new Array(c),g=0;g<c;g++)f[g]=pn(l+g*s);return{min:l,max:u,ticks:f}}function Cn(t,e){var n=t.length*e;return 1===e?t[t.length-1]:0===e?t[0]:n%1!=0?t[Math.ceil(n)-1]:t.length%2==0?(t[n-1]+t[n])/2:t[n]}function Pn(t){return new Date(t).getFullYear()}function kn(t){return new Date(t,0,1).getTime()}function Tn(t){return new Date(t).getMonth()}function An(t,e){return new Date(t,e,1).getTime()}se("cat",an),se("time-cat",(function(t){return an(j({showLast:!0},t))})),se("wilkinson-extended",(function(t){var e=t.min,n=t.max,i=t.tickCount,r=t.nice,a=t.tickInterval,o=t.minLimit,h=t.maxLimit,u=wn(e,n,i,r).ticks;return s(o)&&s(h)?a?fn(e,n,a).ticks:u:gn(t,v(u),y(u))})),se("r-pretty",(function(t){var e=t.min,n=t.max,i=t.tickCount,r=t.tickInterval,a=t.minLimit,o=t.maxLimit,h=bn(e,n,i).ticks;return s(a)&&s(o)?r?fn(e,n,r).ticks:h:gn(t,v(h),y(h))})),se("time",(function(t){var e=t.min,n=t.max,i=t.minTickInterval,r=t.tickInterval,a=t.tickCount;if(r)a=Math.ceil((n-e)/r);else{var s=(n-e)/(r=Ge(e,n,a)[1])/a;s>1&&(r*=Math.ceil(s)),i&&r<i&&(r=i)}r=Math.max(Math.floor((n-e)/(Math.pow(2,12)-1)),r);for(var o=[],h=e;h<n+r;h+=r)o.push(h);return o})),se("time-pretty",(function(t){var e=t.min,n=t.max,i=t.minTickInterval,r=t.tickCount,a=t.tickInterval,s=[];a||(a=(n-e)/r,i&&a<i&&(a=i)),a=Math.max(Math.floor((n-e)/(Math.pow(2,12)-1)),a);var o=Pn(e);if(a>31536e6)for(var h=Pn(n),u=Math.ceil(a/31536e6),l=o;l<=h+u;l+=u)s.push(kn(l));else if(a>Xe){var c=Math.ceil(a/Xe),f=Tn(e),g=function(t,e){var n=Pn(t),i=Pn(e),r=Tn(t);return 12*(i-n)+(Tn(e)-r)%12}(e,n);for(l=0;l<=g+c;l+=c)s.push(An(o,l+f))}else if(a>Le){var p=(x=new Date(e)).getFullYear(),d=x.getMonth(),v=x.getDate(),y=Math.ceil(a/Le),m=function(t,e){return Math.ceil((e-t)/Le)}(e,n);for(l=0;l<m+y;l+=y)s.push(new Date(p,d,v+l).getTime())}else if(a>Be){p=(x=new Date(e)).getFullYear(),d=x.getMonth(),y=x.getDate();var x,_=x.getHours(),S=Math.ceil(a/Be),M=function(t,e){return Math.ceil((e-t)/Be)}(e,n);for(l=0;l<=M+S;l+=S)s.push(new Date(p,d,y,_+l).getTime())}else if(a>6e4){var w=function(t,e){return Math.ceil((e-t)/6e4)}(e,n),b=Math.ceil(a/6e4);for(l=0;l<=w+b;l+=b)s.push(e+6e4*l)}else{var C=a;C<1e3&&(C=1e3);var P=1e3*Math.floor(e/1e3),k=Math.ceil((n-e)/1e3),T=Math.ceil(C/1e3);for(l=0;l<k+T;l+=T)s.push(P+1e3*l)}return s.length>=512&&console.warn("Notice: current ticks length("+s.length+') >= 512, may cause performance issues, even out of memory. Because of the configure "tickInterval"(in milliseconds, current is '+a+") is too small, increase the value to solve the problem!"),s})),se("log",(function(t){var e,n=t.base,i=t.tickCount,r=t.min,a=t.max,s=t.values,o=qe(n,a);if(r>0)e=Math.floor(qe(n,r));else{var h=Ze(s,n,a);e=Math.floor(qe(n,h))}for(var u=o-e,l=Math.ceil(u/i),c=[],f=e;f<o+l;f+=l)c.push(Math.pow(n,f));return r<=0&&c.unshift(0),c})),se("pow",(function(t){var e=t.exponent,n=t.tickCount,i=Math.ceil(Ve(e,t.max));return bn(Math.floor(Ve(e,t.min)),i,n).ticks.map((function(t){var n=t>=0?1:-1;return Math.pow(t,e)*n}))})),se("quantile",(function(t){var e=t.tickCount,n=t.values;if(!n||!n.length)return[];for(var i=n.slice().sort((function(t,e){return t-e})),r=[],a=0;a<e;a++){var s=a/(e-1);r.push(Cn(i,s))}return r})),se("d3-linear",(function(t){var e=t.min,n=t.max,i=t.tickInterval,r=t.minLimit,a=t.maxLimit,o=function(t){var e=t.min,n=t.max,i=t.nice,r=t.tickCount,a=new un;return a.domain([e,n]),i&&a.nice(r),a.ticks(r)}(t);return s(r)&&s(a)?i?fn(e,n,i).ticks:o:gn(t,v(o),y(o))})),nn("cat",he),nn("category",he),nn("identity",rn),nn("linear",We),nn("log",Ue),nn("pow",Je),nn("time",$e),nn("timeCat",Re),nn("quantize",Ke),nn("quantile",Qe);var Dn=function(t){var e=t.values,n=t.tickCount;if(!n)return e;if(e.length<=1)return e;for(var i=parseInt(e.length/(n-1))||1,r=[],a=0;a<e.length;a+=i)r.push(e[a]);var s=e[e.length-1];return r[r.length-1]!==s&&(r.length>=n?r[r.length-1]=s:r.push(s)),r},In=[1,1.2,1.5,2,2.2,2.4,2.5,3,4,5,6,7.5,8,10];function Yn(t){var e=1;if(0===(t=Math.abs(t)))return e;if(t<1){for(var n=0;t<1;)e/=10,t*=10,n++;return e.toString().length>12&&(e=parseFloat(e.toFixed(n))),e}for(;t>10;)e*=10,t/=10;return e}function On(t){var e=t.interval,n=t.tickCount,i=t.max,r=t.min;return Math.floor(r/e)*e+(n-1)*e>=i}function En(t){var e=t.toString(),n=e.indexOf("."),i=e.indexOf("e-"),r=i>=0?parseInt(e.substr(i+2),10):e.substr(n+1).length;return r>20&&(r=20),r}function Nn(t,e){return parseFloat(t.toFixed(e))}var Fn=en("linear"),zn=en("identity"),Bn=en("category"),Ln=en("timeCat");function Xn(t){Object.keys(t).forEach((function(e){delete t[e]}))}se("cat",Dn),se("time-cat",Dn),se("wilkinson-extended",(function(t){var e=t||{},n=e.tickCount,i=e.tickInterval,r=t||{},a=r.min,s=r.max;a=isNaN(a)?0:a,s=isNaN(s)?0:s;var o=n&&n>=2?n:5,h=i||function(t){var e=t.tickCount,n=t.min,i=t.max;if(n===i)return 1*Yn(i);for(var r=(i-n)/(e-1),a=Yn(r),s=r/a,o=i/a,h=n/a,u=0,l=0;l<In.length;l++){if(s<=In[l]){u=l;break}}var c=n<0&&i>0&&2===e?In[u]:function t(e,n,i,r){for(var a=!1,s=In[e],o=e;o<In.length;o++)if(On({interval:In[o],tickCount:n,max:r,min:i})){s=In[o],a=!0;break}if(!a)return 10*t(0,n,i/10,r/10);return s}(u,e,h,o),f=En(c)+En(a);return Nn(c*a,f)}({tickCount:o,max:s,min:a}),u=Math.floor(a/h)*h;if(i){var l=Math.abs(Math.ceil((s-u)/i))+1;o=Math.max(o,l)}var c=[],f=0,g=En(h);if(a<0&&s>0&&2===o)return[Nn(u,g),Nn(Math.ceil(s/h)*h,g)];for(;f<o;)c.push(Nn(u+f*h,g)),f++;return c})),oe.Linear=Fn,oe.Identity=zn,oe.Category=Bn,oe.Cat=Bn,oe.TimeCat=Ln;var jn=function(){function t(t){this.defs={},this.scales={},D(this,t)}var e=t.prototype;return e.setFieldDef=function(t,e){var n=this.defs;h(t)?D(n,t):n[t]=e,this.updateScales()},e._getDef=function(t){var e=this.defs,n=null;return(Mt.scales[t]||e[t])&&(n=D({},Mt.scales[t]),u(e[t],(function(t,e){s(t)?delete n[e]:n[e]=t}))),n},e._getDefaultType=function(t,e,n){if(n&&n.type)return n.type;var i="linear",r=W(e,t);return o(r)&&(r=r[0]),p(r)&&(i="cat"),i},e._getScaleDef=function(t,e,n,i){var r,a={field:e,values:r=i&&i.values?i.values:H(n,e)};if("cat"!==t&&"timeCat"!==t){if(!i||!i.min||!i.max){var s=U(r),o=s.min,h=s.max;a.min=o,a.max=h,a.nice=!0}}else a.isRounding=!1;return a},e._adjustRange=function(t,e){var n=e.range,i=e.values;if("linear"===t||n||!i)return e;var r=i.length;if(1===r)e.range=[.5,1];else{var a=this.chart.get("coord"),o=Mt.widthRatio.multiplePie,h=0;!function(t){if(!t.isPolar)return!1;var e=t.startAngle,n=t.endAngle;return!(!s(e)&&!s(n)&&n-e<2*Math.PI)}(a)?(h=1/r*.5,e.range=[h,1-h]):a.transposed?(h=1/r*o,e.range=[h/2,1-h/2]):e.range=[0,1-1/r]}return e},e._getScaleCfg=function(t,e){var n=this._getDef(t);if(!e||!e.length)return n&&n.type?(n.field=t,{type:n.type,cfg:n}):{type:"identity",cfg:{value:t,field:t.toString(),values:[t]}};var i=e[0][t];if(null===i&&(i=W(e,t)),x(t)||s(i)&&!n)return{type:"identity",cfg:{value:t,field:t.toString(),values:[t]}};var r=this._getDefaultType(t,e,n),a=this._getScaleDef(r,t,e,n);return n&&D(a,n),{type:r,cfg:a=this._adjustRange(r,a)}},e.createScale=function(t,e){var n=this.scales,i=this._getScaleCfg(t,e),r=i.type,a=i.cfg,s=n[t];if(s&&s.type===r)return s.change(a),s;var o=new(en(r))(a);return n[t]=o,o},e._updateScale=function(t){var e=t.field,n=this.chart._getScaleData(e),i=this._getScaleCfg(e,n).cfg;t.change(i)},e.updateScales=function(){var t=this;u(this.scales,(function(e){t._updateScale(e)}))},e.adjustStartZero=function(t){var e=this.defs,n=t.field,i=t.min,r=t.max;e[n]&&e[n].min||(i>0?t.change({min:0}):r<0&&t.change({max:0}))},e.clear=function(){Xn(this.defs),Xn(this.scales),this.data=null},t}(),Gn=function(){var t=e.prototype;function e(t){this._initDefaultCfg(),D(this,t),this.draw()}return t._initDefaultCfg=function(){this.ticks=[],this.tickLine={},this.offsetFactor=1,this.frontContainer=null,this.backContainer=null,this.gridPoints=[]},t.draw=function(){var t=this.line,e=this.tickLine,n=this.label,i=this.grid;i&&this.drawGrid(i),e&&this.drawTicks(e),t&&this.drawLine(t),n&&this.drawLabels()},t.drawTicks=function(t){var e=this,n=e.ticks,i=t.length,r=e.getContainer(t.top);u(n,(function(n){var a=e.getOffsetPoint(n.value),s=e.getSidePoint(a,i);r.addShape("line",{className:"axis-tick",attrs:D({x1:a.x,y1:a.y,x2:s.x,y2:s.y},t)})._id=e._id+"-ticks"}))},t.drawLabels=function(){var t=this,e=t.labelOffset;u(t.labels,(function(n){var i=t.getContainer(n.get("top")),r=t.getOffsetPoint(n.get("value")),a=t.getSidePoint(r,e),s=a.x,o=a.y;n.attr(D({x:s,y:o},t.getTextAlignInfo(r,e),n.get("textStyle"))),n._id=t._id+"-"+n.attr("text"),i.add(n)}))},t.drawLine=function(){},t.drawGrid=function(t){var e=this,n=e.gridPoints,i=e.ticks,r=t,s=n.length;u(n,(function(n,o){if(a(t)){var h=i[o]||{},u=t(h.text,o,s);r=u?D({},Mt._defaultAxis.grid,u):null}if(r){var l,c=r.type,f=n.points,g=e.getContainer(r.top);if("arc"===c){var p=e.center,d=e.startAngle,v=e.endAngle,y=Yt.length([f[0].x-p.x,f[0].y-p.y]);l=g.addShape("Arc",{className:"axis-grid",attrs:D({x:p.x,y:p.y,startAngle:d,endAngle:v,r:y},r)})}else l=g.addShape("Polyline",{className:"axis-grid",attrs:D({points:f},r)});l._id=n._id}}))},t.getOffsetPoint=function(){},t.getAxisVector=function(){},t.getOffsetVector=function(t,e){var n=this.getAxisVector(t),i=Yt.normalize([],n),r=this.offsetFactor,a=[-1*i[1]*r,i[0]*r];return Yt.scale([],a,e)},t.getSidePoint=function(t,e){var n=this.getOffsetVector(t,e);return{x:t.x+n[0],y:t.y+n[1]}},t.getTextAlignInfo=function(t,e){var n=this.getOffsetVector(t,e);return{textAlign:n[0]>0?"left":n[0]<0?"right":"center",textBaseline:n[1]>0?"top":n[1]<0?"bottom":"middle"}},t.getContainer=function(t){var e=this.frontContainer,n=this.backContainer;return t?e:n},e}(),Rn=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initDefaultCfg=function(){t.prototype._initDefaultCfg.call(this),this.start=null,this.end=null},n.getOffsetPoint=function(t){var e=this.start,n=this.end;return{x:e.x+(n.x-e.x)*t,y:e.y+(n.y-e.y)*t}},n.getAxisVector=function(){var t=this.start,e=this.end;return[e.x-t.x,e.y-t.y]},n.drawLine=function(t){var e=this.getContainer(t.top),n=this.start,i=this.end;e.addShape("line",{className:"axis-line",attrs:D({x1:n.x,y1:n.y,x2:i.x,y2:i.y},t)})},e}(Gn);Gn.Line=Rn;var Hn=function(){function t(t){this.axisCfg={},this.frontPlot=null,this.backPlot=null,this.axes={},D(this,t)}var e=t.prototype;return e._isHide=function(t){var e=this.axisCfg;return!e||!1===e[t]},e._getLinePosition=function(t,e,n,i){var r="",a=t.field,s=this.axisCfg;return s[a]&&s[a].position?r=s[a].position:"x"===e?r=i?"left":"bottom":"y"===e&&(r=n?"right":"left",i&&(r="bottom")),r},e._getLineCfg=function(t,e,n){var i,r,a=1;return"x"===e?(i={x:0,y:0},r={x:1,y:0}):"right"===n?(i={x:1,y:0},r={x:1,y:1}):(i={x:0,y:0},r={x:0,y:1},a=-1),t.transposed&&(a*=-1),{offsetFactor:a,start:t.convertPoint(i),end:t.convertPoint(r)}},e._getCircleCfg=function(t){return{startAngle:t.startAngle,endAngle:t.endAngle,center:t.center,radius:t.circleRadius}},e._getRadiusCfg=function(t){var e,n;return t.transposed?(e={x:0,y:0},n={x:1,y:0}):(e={x:0,y:0},n={x:0,y:1}),{offsetFactor:-1,start:t.convertPoint(e),end:t.convertPoint(n)}},e._getAxisCfg=function(t,e,n,i,r){var s=this,o=this,h=this.axisCfg,l=e.getTicks(),c=Y({ticks:l,frontContainer:this.frontPlot,backContainer:this.backPlot},r,h[e.field]),f=[],g=c.label,p=l.length,d=0,v=0,y=g;return u(l,(function(t,e){if(a(g)){var n=g(t.text,e,p);y=n?D({},Mt._defaultAxis.label,n):null}if(y){var i={};y.textAlign&&(i.textAlign=y.textAlign),y.textBaseline&&(i.textBaseline=y.textBaseline);var r=(y.top?s.frontPlot:s.backPlot).addShape("text",{className:"axis-label",aria:!1,attrs:D({x:0,y:0,text:t.text,fontFamily:o.chart.get("canvas").get("fontFamily")},y),value:t.value,textStyle:i,top:y.top,context:o.chart.get("canvas").get("context")});f.push(r);var h=r.getBBox(),u=h.width,l=h.height;d=Math.max(d,u),v=Math.max(v,l)}})),c.labels=f,c.maxWidth=d,c.maxHeight=v,c},e._createAxis=function(t,e,n,i,r){void 0===r&&(r="");var a,s,o,h=t.type,u=t.transposed;if("cartesian"===h||"rect"===h){var l=this._getLinePosition(e,i,r,u);(o=Mt.axis[l]).position=l,a="Line",s=l}else"x"===i&&!u||"y"===i&&u?(o=Mt.axis.circle,a="Circle",s="circle"):(o=Mt.axis.radius,a="Line",s="radius");var c=this._getAxisCfg(t,e,n,i,o);c.type=a,c.dimType=i,c.verticalScale=n,c.index=r,this.axes[s]=c},e.createAxis=function(t,e,n){var i=this;e&&!i._isHide(e.field)&&i._createAxis(t,e,n[0],"x"),u(n,(function(n,r){i._isHide(n.field)||i._createAxis(t,n,e,"y",r)}));var r=this.axes,a=i.chart;if(a._isAutoPadding()){var o=dt(a.get("padding")),h=dt(a.get("appendPadding")),l=a.get("legendRange")||{top:0,right:0,bottom:0,left:0},c=["auto"===o[0]?l.top+2*h[0]:o[0],"auto"===o[1]?l.right+h[1]:o[1],"auto"===o[2]?l.bottom+h[2]:o[2],"auto"===o[3]?l.left+h[3]:o[3]];if(t.isPolar){var f=r.circle;if(f){var g=f.maxHeight,p=f.maxWidth,d=f.labelOffset;c[0]+=g+d,c[1]+=p+d,c[2]+=g+d,c[3]+=p+d}}else{if(r.right&&"auto"===o[1]){var v=r.right,y=v.maxWidth,m=v.labelOffset;c[1]+=y+m}if(r.left&&"auto"===o[3]){var x=r.left,_=x.maxWidth,S=x.labelOffset;c[3]+=_+S}if(r.bottom&&"auto"===o[2]){var M=r.bottom,w=M.maxHeight,b=M.labelOffset;c[2]+=w+b}}a.set("_padding",c),a._updateLayout(c)}u(r,(function(e){var n,r=e.type,a=e.grid,o=e.verticalScale,h=e.ticks,l=e.dimType,c=e.position,f=e.index;if(t.isPolar?"Line"===r?n=i._getRadiusCfg(t):"Circle"===r&&(n=i._getCircleCfg(t)):n=i._getLineCfg(t,l,c),a&&o){var g=[],p=function(t){var e=t.slice(0);if(e.length>0){var n=e[0],i=e[e.length-1];0!==n.value&&e.unshift({value:0}),1!==i.value&&e.push({value:1})}return e}(o.getTicks());u(h,(function(e){var n=[];u(p,(function(i){var r="x"===l?e.value:i.value,a="x"===l?i.value:e.value;if(r>=0&&r<=1&&a>=0&&a<=1){var s=t.convertPoint({x:r,y:a});n.push(s)}})),g.push({points:n,_id:"axis-"+l+f+"-grid-"+e.tickValue})})),e.gridPoints=g,t.isPolar&&(e.center=t.center,e.startAngle=t.startAngle,e.endAngle=t.endAngle)}n._id="axis-"+l,s(f)||(n._id="axis-"+l+f),new Gn[r](D(e,n))}))},e.clear=function(){this.axes={},this.frontPlot.clear(),this.backPlot.clear()},t}(),Wn=function(t,e){var n=e.x-t.x,i=e.y-t.y;return Math.abs(n)>Math.abs(i)?n>0?"right":"left":i>0?"down":"up"},Vn=function(t,e){var n=Math.abs(e.x-t.x),i=Math.abs(e.y-t.y);return Math.sqrt(n*n+i*i)},qn=function(){function t(t){var e=this,n=t.canvas,i=t.el;wt(this,"_click",(function(t){var n=ft(t,e.canvas);t.points=n,e.emitEvent("click",t)})),wt(this,"_start",(function(t){var n,i,r=ft(t,e.canvas);r&&(t.points=r,e.emitEvent("touchstart",t),e.reset(),e.startTime=Date.now(),e.startPoints=r,r.length>1?(e.startDistance=Vn(r[0],r[1]),e.center=(n=r[0],i=r[1],{x:n.x+(i.x-n.x)/2,y:n.y+(i.y-n.y)/2})):e.pressTimeout=setTimeout((function(){t.direction="none",e.emitStart("press",t),e.emitEvent("press",t),e.eventType="press",e.direction="none"}),250))})),wt(this,"_move",(function(t){var n=ft(t,e.canvas);if(n){e.clearPressTimeout(),t.points=n,e.emitEvent("touchmove",t);var i=e.startPoints;if(i)if(n.length>1){var r=e.startDistance,a=Vn(n[0],n[1]);t.zoom=a/r,t.center=e.center,e.emitStart("pinch",t),e.emitEvent("pinch",t)}else{var s=n[0].x-i[0].x,o=n[0].y-i[0].y,h=e.direction||Wn(i[0],n[0]);e.direction=h;var u=e.getEventType(n);t.direction=h,t.deltaX=s,t.deltaY=o,e.emitStart(u,t),e.emitEvent(u,t);var l=e.lastMoveTime,c=Date.now();c-l>0&&(e.prevMoveTime=l,e.prevMovePoints=e.lastMovePoints,e.lastMoveTime=c,e.lastMovePoints=n)}}})),wt(this,"_end",(function(t){var n=ft(t,e.canvas);t.points=n,e.emitEnd(t),e.emitEvent("touchend",t);var i=e.lastMoveTime;if(Date.now()-i<100){var r=i-(e.prevMoveTime||e.startTime);if(r>0){var a=e.prevMovePoints||e.startPoints,s=e.lastMovePoints,o=Vn(a[0],s[0])/r;o>.3&&(t.velocity=o,t.direction=Wn(a[0],s[0]),e.emitEvent("swipe",t))}}e.reset();var h=t.touches;h&&h.length>0&&e._start(t)})),wt(this,"_cancel",(function(t){e.emitEvent("touchcancel",t),e.reset()})),this.canvas=n,this.delegateEvent(i),this.processEvent={}}var e=t.prototype;return e.delegateEvent=function(t){t.addEventListener("click",this._click),t.addEventListener("touchstart",this._start),t.addEventListener("touchmove",this._move),t.addEventListener("touchend",this._end),t.addEventListener("touchcancel",this._cancel)},e.emitEvent=function(t,e){this.canvas.emit(t,e)},e.getEventType=function(t){var e,n=this.eventType,i=this.canvas,r=this.startTime,a=this.startPoints;if(n)return n;var s=i.__events.pan;s&&s.length?e=Date.now()-r>250&&Vn(a[0],t[0])<10?"press":"pan":e="press";return this.eventType=e,e},e.enable=function(t){this.processEvent[t]=!0},e.isProcess=function(t){return this.processEvent[t]},e.emitStart=function(t,e){this.isProcess(t)||(this.enable(t),this.emitEvent(t+"start",e))},e.emitEnd=function(t){var e=this,n=this.processEvent;Object.keys(n).forEach((function(i){e.emitEvent(i+"end",t),delete n[i]}))},e.clearPressTimeout=function(){this.pressTimeout&&(clearTimeout(this.pressTimeout),this.pressTimeout=0)},e.reset=function(){this.clearPressTimeout(),this.startTime=0,this.startPoints=null,this.startDistance=0,this.direction=null,this.eventType=null,this.pinch=!1,this.prevMoveTime=0,this.prevMovePoints=null,this.lastMoveTime=0,this.lastMovePoints=null},t}(),Zn=function(t){function e(e){var n;return(n=t.call(this)||this).context=e,n.width=0,n.height=0,n.style={},n.currentStyle={},n.attrs={},n.isCanvasElement=!0,n}Ct(e,t);var n=e.prototype;return n.getContext=function(){return this.context},n.getBoundingClientRect=function(){return{top:0,right:this.width,bottom:this.height,left:0}},n.setAttribute=function(t,e){this.attrs[t]=e},n.addEventListener=function(t,e){this.on(t,e)},n.removeEventListener=function(t,e){this.off(t,e)},n.dispatchEvent=function(t,e){this.emit(t,e)},e}(Tt);var Un=function(t){return t?function(t){if(!t)return!1;if(1!==t.nodeType||!t.nodeName||"canvas"!==t.nodeName.toLowerCase())return!1;var e=!1;try{t.addEventListener("eventTest",(function(){e=!0})),t.dispatchEvent(new Event("eventTest"))}catch(t){e=!1}return e}(t.canvas)?t.canvas:new Zn(t):null};function Jn(t,e){u(t,(function(t){t=t.split(":"),e.addColorStop(Number(t[0]),t[1])}))}function $n(t,e,n){if("("===t[1])try{var i=t[0];if("l"===i)return function(t,e,n){var i,r,a=t.split(" "),s=a[0].slice(2,a[0].length-1);i=parseFloat(s)*Math.PI/180,r=2*Math.PI,s=(i%r+r)%r;var o,h,u=a.slice(1),l=e.getBBox(),c=l.minX,f=l.minY,g=l.maxX,p=l.maxY;s>=0&&s<.5*Math.PI?(o={x:c,y:f},h={x:g,y:p}):.5*Math.PI<=s&&s<Math.PI?(o={x:g,y:f},h={x:c,y:p}):Math.PI<=s&&s<1.5*Math.PI?(o={x:g,y:p},h={x:c,y:f}):(o={x:c,y:p},h={x:g,y:f});var d=Math.tan(s),v=d*d,y=(h.x-o.x+d*(h.y-o.y))/(v+1)+o.x,m=d*(h.x-o.x+d*(h.y-o.y))/(v+1)+o.y,x=n.createLinearGradient(o.x,o.y,y,m);return Jn(u,x),x}(t,e,n);if("r"===i)return function(t,e,n){var i=t.split(" "),r=i[0].slice(2,i[0].length-1);r=r.split(",");var a=parseFloat(r[0]),s=parseFloat(r[1]),o=parseFloat(r[2]),h=i.slice(1);if(0===o)return h[h.length-1].split(":")[1];var u=e.getBBox(),l=u.width,c=u.height,f=u.minX,g=u.minY,p=Math.sqrt(l*l+c*c)/2,d=n.createRadialGradient(f+l*a,g+c*s,o*p,f+l/2,g+c/2,p);return Jn(h,d),d}(t,e,n)}catch(t){console.error("error in parsing gradient string, please check if there are any extra whitespaces."),console.error(t)}return t}var Kn={stroke:"strokeStyle",fill:"fillStyle",opacity:"globalAlpha"},Qn=["fillStyle","font","globalAlpha","lineCap","lineWidth","lineJoin","miterLimit","shadowBlur","shadowColor","shadowOffsetX","shadowOffsetY","strokeStyle","textAlign","textBaseline","lineDash","shadow"],ti=["circle","sector","polygon","rect","polyline"],ei=function(){var t=e.prototype;function e(t){this._initProperties(),D(this._attrs,t);var e=this._attrs.attrs;e&&this.initAttrs(e),this.initTransform()}return t._initProperties=function(){this._attrs={zIndex:0,visible:!0,destroyed:!1}},t.get=function(t){return this._attrs[t]},t.set=function(t,e){this._attrs[t]=e},t.isGroup=function(){return this.get("isGroup")},t.isShape=function(){return this.get("isShape")},t.initAttrs=function(t){this.attr(D(this.getDefaultAttrs(),t))},t.getDefaultAttrs=function(){return{}},t._setAttr=function(t,e){var n=this._attrs.attrs;if("clip"===t)e=this._setAttrClip(e);else{var i=Kn[t];i&&(n[i]=e)}n[t]=e},t._getAttr=function(t){return this._attrs.attrs[t]},t._setAttrClip=function(t){return t&&ti.indexOf(t._attrs.type)>-1?(null===t.get("canvas")&&(t=Object.assign({},t)),t.set("parent",this.get("parent")),t.set("context",this.get("context")),t):null},t.attr=function(t,e){var n=this;if(n.get("destroyed"))return null;var i=arguments.length;if(0===i)return n._attrs.attrs;if(h(t)){for(var r in this._attrs.bbox=null,t)n._setAttr(r,t[r]);return n._afterAttrsSet&&n._afterAttrsSet(),n}return 2===i?(this._attrs.bbox=null,n._setAttr(t,e),n._afterAttrsSet&&n._afterAttrsSet(),n):n._getAttr(t)},t.getParent=function(){return this.get("parent")},t.draw=function(t){this.get("destroyed")||this.get("visible")&&(this.setContext(t),this.drawInner(t),this.restoreContext(t))},t.setContext=function(t){var e=this._attrs.attrs.clip;t.save(),e&&(e.resetTransform(t),e.createPath(t),t.clip()),this.resetContext(t),this.resetTransform(t)},t.restoreContext=function(t){t.restore()},t.resetContext=function(t){var e=this._attrs.attrs;for(var n in e)if(Qn.indexOf(n)>-1){var i=e[n];"fillStyle"!==n&&"strokeStyle"!==n||!i||(i=$n(i,this,t)),"lineDash"===n&&t.setLineDash&&o(i)?t.setLineDash(i):t[n]=i}},t.hasFill=function(){return this.get("canFill")&&this._attrs.attrs.fillStyle},t.hasStroke=function(){return this.get("canStroke")&&this._attrs.attrs.strokeStyle},t.drawInner=function(){},t.show=function(){return this.set("visible",!0),this},t.hide=function(){return this.set("visible",!1),this},t.isVisible=function(){return this.get("visible")},t.getAriaLabel=function(){var t=this._attrs,e=t.destroyed,n=t.visible,i=t.isShape,r=t.aria;if(!e&&n&&(!i||r))return this._getAriaLabel()},t._getAriaLabel=function(){return this._attrs.ariaLabel},t._removeFromParent=function(){var t=this.get("parent");t&&Z(t.get("children"),this);return this},t.remove=function(t){t?this.destroy():this._removeFromParent()},t.destroy=function(){if(this.get("destroyed"))return null;this._removeFromParent(),this._attrs={},this.set("destroyed",!0)},t.getBBox=function(){return{minX:0,maxX:0,minY:0,maxY:0,width:0,height:0}},t.initTransform=function(){var t=this._attrs.attrs||{};t.matrix||(t.matrix=[1,0,0,1,0,0]),this._attrs.attrs=t},t.getMatrix=function(){return this._attrs.attrs.matrix},t.setMatrix=function(t){this._attrs.attrs.matrix=[t[0],t[1],t[2],t[3],t[4],t[5]]},t.transform=function(t){var e=this._attrs.attrs.matrix;return this._attrs.attrs.matrix=It.transform(e,t),this},t.setTransform=function(t){return this._attrs.attrs.matrix=[1,0,0,1,0,0],this.transform(t)},t.translate=function(t,e){var n=this._attrs.attrs.matrix;It.translate(n,n,[t,e])},t.rotate=function(t){var e=this._attrs.attrs.matrix;It.rotate(e,e,t)},t.scale=function(t,e){var n=this._attrs.attrs.matrix;It.scale(n,n,[t,e])},t.moveTo=function(t,e){var n=this._attrs.x||0,i=this._attrs.y||0;this.translate(t-n,e-i),this.set("x",t),this.set("y",e)},t.apply=function(t){var e=this._attrs.attrs.matrix;return Yt.transformMat2d(t,t,e),this},t.resetTransform=function(t){var e=this._attrs.attrs.matrix;It.isChanged(e)&&t.transform(e[0],e[1],e[2],e[3],e[4],e[5])},t.isDestroyed=function(){return this.get("destroyed")},e}(),ni=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initProperties=function(){this._attrs={zIndex:0,visible:!0,destroyed:!1,isShape:!0,attrs:{}}},n.getType=function(){return this._attrs.type},n.drawInner=function(t){var e=this.get("attrs");this.createPath(t);var n=t.globalAlpha;if(this.hasFill()){var i=e.fillOpacity;s(i)||1===i?t.fill():(t.globalAlpha=i,t.fill(),t.globalAlpha=n)}if(this.hasStroke()&&e.lineWidth>0){var r=e.strokeOpacity;s(r)||1===r||(t.globalAlpha=r),t.stroke()}},n.getBBox=function(){var t=this._attrs.bbox;return t||((t=this.calculateBox())&&(t.x=t.minX,t.y=t.minY,t.width=t.maxX-t.minX,t.height=t.maxY-t.minY),this._attrs.bbox=t),t},n.calculateBox=function(){return null},n.createPath=function(){},e}(ei);var ii=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initProperties=function(){t.prototype._initProperties.call(this),this._attrs.canFill=!0,this._attrs.canStroke=!0,this._attrs.type="rect"},n.getDefaultAttrs=function(){return{x:0,y:0,width:0,height:0,radius:0,lineWidth:0}},n.createRadiusPath=function(t,e,n,i,r,a){a=function(t,e,n){if(!((t=dt(t))[0]||t[1]||t[2]||t[3]))return t;var i=Math.max(t[0]+t[1],t[2]+t[3]),r=Math.max(t[0]+t[3],t[1]+t[2]),a=Math.min(e/i,n/r);return a<1?t.map((function(t){return t*a})):t}(a,i,r),t.moveTo(e+a[0],n),t.lineTo(e+i-a[1],n),t.arc(e+i-a[1],n+a[1],a[1],-Math.PI/2,0,!1),t.lineTo(e+i,n+r-a[2]),t.arc(e+i-a[2],n+r-a[2],a[2],0,Math.PI/2,!1),t.lineTo(e+a[3],n+r),t.arc(e+a[3],n+r-a[3],a[3],Math.PI/2,Math.PI,!1),t.lineTo(e,n+a[0]),t.arc(e+a[0],n+a[0],a[0],Math.PI,3*Math.PI/2,!1),t.closePath()},n.createPath=function(t){var e=this.get("attrs"),n=e.x,i=e.y,r=e.width,a=e.height,s=e.radius;t.beginPath(),s&&r*a?this.createRadiusPath(t,n,i,r,a,s):t.rect(n,i,r,a)},n.calculateBox=function(){var t=this.get("attrs"),e=t.x,n=t.y;return{minX:e,minY:n,maxX:e+t.width,maxY:n+t.height}},e}(ni),ri={},ai=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initProperties=function(){t.prototype._initProperties.call(this),this._attrs.canFill=!1,this._attrs.canStroke=!1,this._attrs.loading=!1,this._attrs.image=null,this._attrs.type="image"},n.draw=function(e){var n=this;if(!this.get("loading"))if(this.get("image"))t.prototype.draw.call(this,e);else{var i=this.get("attrs").src;if(i&&window.Image){var r=this.get("cacheImage");if(r&&ri[i])return this.set("image",ri[i]),void this.draw(e);this.set("loading",!0);var a=new Image;a.crossOrigin="",a.onload=function(){n.set("loading",!1),n.set("image",a),n.draw(e)},a.src=i,r&&(ri[i]=a)}}},n.createPath=function(t){var e=this.get("image");this.drawImage(t,e)},n.drawImage=function(t,e){var n=this._attrs,i=n.attrs;if(!n.destroyed){var r=i.x,a=i.y,o=i.width,h=i.height,u=i.sx,l=i.sy,c=i.swidth,f=i.sheight,g=i.radius,p=i.fillOpacity;g&&(t.save(),this.createRadiusPath(t,r,a,o,h,g),t.clip());var d=t.globalAlpha;s(p)||(t.globalAlpha=p),s(u)||s(l)||s(c)||s(f)?t.drawImage(e,r,a,o,h):t.drawImage(e,u,l,c,f,r,a,o,h),t.globalAlpha=d,g&&t.restore()}},e}(ii),si=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initProperties=function(){t.prototype._initProperties.call(this),this._attrs.canFill=!0,this._attrs.canStroke=!0,this._attrs.type="circle"},n.getDefaultAttrs=function(){return{x:0,y:0,r:0,lineWidth:0}},n.createPath=function(t){var e=this.get("attrs"),n=e.x,i=e.y,r=e.r;t.beginPath(),t.arc(n,i,r,0,2*Math.PI,!1),t.closePath()},n.calculateBox=function(){var t=this.get("attrs"),e=t.x,n=t.y,i=t.r;return{minX:e-i,maxX:e+i,minY:n-i,maxY:n+i}},e}(ni),oi=Yt.create(),hi=Yt.create(),ui=Yt.create();function li(t,e,n,i,r){var a=t*t;return e+(3*-e+t*(3*e-e*t))*t+(3*n+t*(-6*n+3*n*t))*t+(3*i-3*i*t)*a+r*(a*t)}function ci(t){for(var e,n,i,r,a,s=1/0,o=-1/0,h=1/0,u=-1/0,l={x:t[0],y:t[1]},c={x:t[2],y:t[3]},f={x:t[4],y:t[5]},g={x:t[6],y:t[7]},p=0;p<100;p++){var d={x:li(a=p/100,(e=l).x,(n=c).x,(i=f).x,(r=g).x),y:li(a,e.y,n.y,i.y,r.y)};d.x<s&&(s=d.x),d.x>o&&(o=d.x),d.y<h&&(h=d.y),d.y>u&&(u=d.y)}return{minX:s,minY:h,maxX:o,maxY:u}}function fi(t,e){if(0!==t.length){for(var n=t[0],i=n.x,r=n.x,a=n.y,s=n.y,o=t.length,h=1;h<o;h++)n=t[h],i=Math.min(i,n.x),r=Math.max(r,n.x),a=Math.min(a,n.y),s=Math.max(s,n.y);return{minX:i-(e=e/2||0),minY:a-e,maxX:r+e,maxY:s+e}}}function gi(t,e,n,i,r,a){var s=Math.abs(i-r);if(s%(2*Math.PI)<1e-4&&s>1e-4)return{minX:t-n,minY:e-n,maxX:t+n,maxY:e+n};oi[0]=Math.cos(i)*n+t,oi[1]=Math.sin(i)*n+e,hi[0]=Math.cos(r)*n+t,hi[1]=Math.sin(r)*n+e;var o=[0,0],h=[0,0];if(Yt.min(o,oi,hi),Yt.max(h,oi,hi),(i%=2*Math.PI)<0&&(i+=2*Math.PI),(r%=2*Math.PI)<0&&(r+=2*Math.PI),i>r&&!a?r+=2*Math.PI:i<r&&a&&(i+=2*Math.PI),a){var u=r;r=i,i=u}for(var l=0;l<r;l+=Math.PI/2)l>i&&(ui[0]=Math.cos(l)*n+t,ui[1]=Math.sin(l)*n+e,Yt.min(o,ui,o),Yt.max(h,ui,h));return{minX:o[0],minY:o[1],maxX:h[0],maxY:h[1]}}var pi=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initProperties=function(){t.prototype._initProperties.call(this),this._attrs.canStroke=!0,this._attrs.type="line"},n.getDefaultAttrs=function(){return{x1:0,y1:0,x2:0,y2:0,lineWidth:1}},n.createPath=function(t){var e=this.get("attrs"),n=e.x1,i=e.y1,r=e.x2,a=e.y2;t.beginPath(),t.moveTo(n,i),t.lineTo(r,a)},n.calculateBox=function(){var t=this.get("attrs");return function(t,e,n,i,r){return r=r/2||0,{minX:Math.min(t,n)-r,minY:Math.min(e,i)-r,maxX:Math.max(t,n)+r,maxY:Math.max(e,i)+r}}(t.x1,t.y1,t.x2,t.y2,t.lineWidth)},e}(ni),di=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initProperties=function(){t.prototype._initProperties.call(this),this._attrs.canFill=!0,this._attrs.canStroke=!0,this._attrs.type="polygon"},n.getDefaultAttrs=function(){return{points:null,lineWidth:0}},n.createPath=function(t){var e=this.get("attrs").points;t.beginPath();for(var n=0,i=e.length;n<i;n++){var r=e[n];0===n?t.moveTo(r.x,r.y):t.lineTo(r.x,r.y)}t.closePath()},n.calculateBox=function(){return fi(this.get("attrs").points)},e}(ni);function vi(t){return[t.x,t.y]}function yi(t,e,n){for(var i,r,a,s=!!e,o=function(t,e,n,i){var r,a,s,o,h,u,l,c,f=[],g=!!i;if(g){for(s=[1/0,1/0],o=[-1/0,-1/0],c=0,l=t.length;c<l;c++)h=vi(t[c]),Yt.min(s,s,h),Yt.max(o,o,h);Yt.min(s,s,i[0]),Yt.max(o,o,i[1])}for(c=0,u=t.length;c<u;c++){if(h=vi(t[c]),n)r=vi(t[c?c-1:u-1]),a=vi(t[(c+1)%u]);else{if(0===c||c===u-1){f.push([h[0],h[1]]);continue}r=vi(t[c-1]),a=vi(t[c+1])}var p=Yt.sub([],a,r);Yt.scale(p,p,e);var d=Yt.distance(h,r),v=Yt.distance(h,a),y=d+v;0!==y&&(d/=y,v/=y);var m=Yt.scale([],p,-d),x=Yt.scale([],p,v),_=Yt.add([],h,m),S=Yt.add([],h,x);g&&(Yt.max(_,_,s),Yt.min(_,_,o),Yt.max(S,S,s),Yt.min(S,S,o)),f.push([_[0],_[1]]),f.push([S[0],S[1]])}return n&&f.push(f.shift()),f}(t,.4,s,n),h=t.length,u=[],l=0;l<h-1;l++)i=o[2*l],r=o[2*l+1],a=t[l+1],u.push(["C",i[0],i[1],r[0],r[1],a.x,a.y]);return s&&(i=o[h],r=o[h+1],a=t[0],u.push(["C",i[0],i[1],r[0],r[1],a.x,a.y])),u}function mi(t){for(var e=[],n=0,i=t.length;n<i;n++){var r=t[n];isNaN(r.x)||isNaN(r.y)||e.push(r)}return e}var xi=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initProperties=function(){t.prototype._initProperties.call(this),this._attrs.canFill=!0,this._attrs.canStroke=!0,this._attrs.type="polyline"},n.getDefaultAttrs=function(){return{points:null,lineWidth:1,smooth:!1}},n.createPath=function(t){var e=this.get("attrs"),n=e.points,i=e.smooth,r=mi(n);if(t.beginPath(),r.length)if(t.moveTo(r[0].x,r[0].y),i)for(var a=yi(r,!1,[[0,0],[1,1]]),s=0,o=a.length;s<o;s++){var h=a[s];t.bezierCurveTo(h[1],h[2],h[3],h[4],h[5],h[6])}else{var u,l;for(u=1,l=r.length-1;u<l;u++)t.lineTo(r[u].x,r[u].y);t.lineTo(r[l].x,r[l].y)}},n.calculateBox=function(){var t=this.get("attrs"),e=t.points,n=t.smooth,i=t.lineWidth,r=mi(e);if(r.length<=1)return fi(r,i);if(n){for(var a=[],s=yi(r,!1,[[0,0],[1,1]]),o=0,h=s.length;o<h;o++){var u=s[o];if(0===o)a.push([r[0].x,r[0].y,u[1],u[2],u[3],u[4],u[5],u[6]]);else{var l=s[o-1];a.push([l[5],l[6],u[1],u[2],u[3],u[4],u[5],u[6]])}}return function(t,e){for(var n=1/0,i=-1/0,r=1/0,a=-1/0,s=0,o=t.length;s<o;s++){var h=ci(t[s]);h.minX<n&&(n=h.minX),h.maxX>i&&(i=h.maxX),h.minY<r&&(r=h.minY),h.maxY>a&&(a=h.maxY)}return{minX:n-(e=e/2||0),minY:r-e,maxX:i+e,maxY:a+e}}(a,i)}return fi(r,i)},e}(ni),_i=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initProperties=function(){t.prototype._initProperties.call(this),this._attrs.canStroke=!0,this._attrs.canFill=!0,this._attrs.type="arc"},n.getDefaultAttrs=function(){return{x:0,y:0,r:0,startAngle:0,endAngle:2*Math.PI,anticlockwise:!1,lineWidth:1}},n.createPath=function(t){var e=this.get("attrs"),n=e.x,i=e.y,r=e.r,a=e.startAngle,s=e.endAngle,o=e.anticlockwise;t.beginPath(),a!==s&&t.arc(n,i,r,a,s,o)},n.calculateBox=function(){var t=this.get("attrs");return gi(t.x,t.y,t.r,t.startAngle,t.endAngle,t.anticlockwise)},e}(ni),Si=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initProperties=function(){t.prototype._initProperties.call(this),this._attrs.canFill=!0,this._attrs.canStroke=!0,this._attrs.type="sector"},n.getDefaultAttrs=function(){return{x:0,y:0,lineWidth:0,r:0,r0:0,startAngle:0,endAngle:2*Math.PI,anticlockwise:!1}},n.createPath=function(t){var e=this.get("attrs"),n=e.x,i=e.y,r=e.startAngle,a=e.endAngle,s=e.r,o=e.r0,h=e.anticlockwise;t.beginPath();var u=Math.cos(r),l=Math.sin(r);t.moveTo(u*o+n,l*o+i),t.lineTo(u*s+n,l*s+i),(Math.abs(a-r)>1e-4||0===r&&a<0)&&(t.arc(n,i,s,r,a,h),t.lineTo(Math.cos(a)*o+n,Math.sin(a)*o+i),0!==o&&t.arc(n,i,o,a,r,!h)),t.closePath()},n.calculateBox=function(){var t=this.get("attrs"),e=t.x,n=t.y,i=t.r,r=t.r0,a=t.startAngle,s=t.endAngle,o=t.anticlockwise,h=gi(e,n,i,a,s,o),u=gi(e,n,r,a,s,o);return{minX:Math.min(h.minX,u.minX),minY:Math.min(h.minY,u.minY),maxX:Math.max(h.maxX,u.maxX),maxY:Math.max(h.maxY,u.maxY)}},e}(ni),Mi=function(t){var e=t.width,n=t.height,i=t.rotate,r=Math.abs(i);return{width:Math.abs(e*Math.cos(r)+n*Math.sin(r)),height:Math.abs(n*Math.cos(r)+e*Math.sin(r))}},wi=0,bi={},Ci=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initProperties=function(){t.prototype._initProperties.call(this),this._attrs.canFill=!0,this._attrs.canStroke=!0,this._attrs.type="text"},n.getDefaultAttrs=function(){return{lineWidth:0,lineCount:1,fontSize:12,fontFamily:"sans-serif",fontStyle:"normal",fontWeight:"normal",fontVariant:"normal",textAlign:"start",textBaseline:"bottom",lineHeight:null,textArr:null}},n._getFontStyle=function(){var t=this._attrs.attrs,e=t.fontSize,n=t.fontFamily,i=t.fontWeight;return t.fontStyle+" "+t.fontVariant+" "+i+" "+e+"px "+n},n._afterAttrsSet=function(){var t=this._attrs.attrs;if(t.font=this._getFontStyle(),t.text){var e=t.text,n=null,i=1;p(e)&&-1!==e.indexOf("\n")&&(i=(n=e.split("\n")).length),t.lineCount=i,t.textArr=n}this.set("attrs",t)},n._getTextHeight=function(){var t=this._attrs.attrs;if(t.height)return t.height;var e=t.lineCount,n=1*t.fontSize;return e>1?n*e+this._getSpaceingY()*(e-1):n},n._getSpaceingY=function(){var t=this._attrs.attrs,e=t.lineHeight,n=1*t.fontSize;return e?e-n:.14*n},n.drawInner=function(t){var e=this._attrs.attrs,n=e.text,i=e.x,r=e.y;if(!(s(n)||isNaN(i)||isNaN(r))){var a=e.textArr,o=1*e.fontSize,h=this._getSpaceingY();e.rotate&&(t.translate(i,r),t.rotate(e.rotate),i=0,r=0);var u,l,c=e.textBaseline;if(a&&(u=this._getTextHeight()),this.hasFill()){var f=e.fillOpacity;if(s(f)||1===f||(t.globalAlpha=f),a)for(var g=0,p=a.length;g<p;g++){var d=a[g];l=r+g*(h+o)-u+o,"middle"===c&&(l+=u-o-(u-o)/2),"top"===c&&(l+=u-o),t.fillText(d,i,l)}else t.fillText(n,i,r)}if(this.hasStroke())if(a)for(var v=0,y=a.length;v<y;v++){var m=a[v];l=r+v*(h+o)-u+o,"middle"===c&&(l+=u-o-(u-o)/2),"top"===c&&(l+=u-o),t.strokeText(m,i,l)}else t.strokeText(n,i,r)}},n._getAriaLabel=function(){return this._attrs.attrs.text},n.calculateBox=function(){var t=this._attrs.attrs,e=t.x,n=t.y,i=t.textAlign,r=t.textBaseline,a=this._getTextWidth();if(!a)return{minX:e,minY:n,maxX:e,maxY:n};var s=this._getTextHeight();if(t.rotate){var o=Mi({width:a,height:s,rotate:t.rotate});a=o.width,s=o.height}var h={x:e,y:n-s};return i&&("end"===i||"right"===i?h.x-=a:"center"===i&&(h.x-=a/2)),r&&("top"===r?h.y+=s:"middle"===r&&(h.y+=s/2)),{minX:h.x,minY:h.y,maxX:h.x+a,maxY:h.y+s}},n._getTextWidth=function(){var t=this._attrs.attrs;if(t.width)return t.width;var e=t.text,n=this.get("context");if(!s(e)){var i=t.font,r=t.textArr,a=e+""+i;if(bi[a])return bi[a];var o=0;if(r)for(var h=0,u=r.length;h<u;h++){var l=r[h];o=Math.max(o,pt(l,i,n).width)}else o=pt(e,i,n).width;return wi>5e3&&(wi=0,bi={}),wi++,bi[a]=o,o}},e}(ni),Pi=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initProperties=function(){t.prototype._initProperties.call(this),this._attrs.canFill=!0,this._attrs.canStroke=!0,this._attrs.createPath=null,this._attrs.type="custom"},n.createPath=function(t){var e=this.get("createPath");e&&e.call(this,t)},n.calculateBox=function(){var t=this.get("calculateBox");return t&&t.call(this)},e}(ni),ki={circle:function(t,e,n,i){i.arc(t,e,n,0,2*Math.PI,!1)},square:function(t,e,n,i){i.moveTo(t-n,e-n),i.lineTo(t+n,e-n),i.lineTo(t+n,e+n),i.lineTo(t-n,e+n),i.closePath()}},Ti=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initProperties=function(){t.prototype._initProperties.call(this),this._attrs.canFill=!0,this._attrs.canStroke=!0,this._attrs.type="marker"},n.getDefaultAttrs=function(){return{x:0,y:0,lineWidth:0}},n.createPath=function(t){var e,n=this.get("attrs"),i=n.x,r=n.y,s=n.radius,o=n.symbol||"circle";e=a(o)?o:ki[o],t.beginPath(),e(i,r,s,t,this)},n.calculateBox=function(){var t=this.get("attrs"),e=t.x,n=t.y,i=t.radius;return{minX:e-i,minY:n-i,maxX:e+i,maxY:n+i}},e}(ni);ni.Rect=ii,ni.Image=ai,ni.Circle=si,ni.Line=pi,ni.Polygon=di,ni.Polyline=xi,ni.Arc=_i,ni.Sector=Si,ni.Text=Ci,ni.Custom=Pi,ni.Marker=Ti;var Ai={};var Di={getGroupClass:function(){},getChildren:function(){return this.get("children")},addShape:function(t,e){void 0===e&&(e={});var n=Ai[t];n||(n=b(t),Ai[t]=n);var i=new ni[n](e);return this.add(i),i},addGroup:function(t){var e=new(this.getGroupClass())(t);return this.add(e),e},contain:function(t){return this.get("children").indexOf(t)>-1},sort:function(){for(var t=this.get("children"),e=0,n=t.length;e<n;e++){t[e]._INDEX=e}return t.sort(function(t){return function(e,n){var i=t(e,n);return 0===i?e._INDEX-n._INDEX:i}}((function(t,e){return t.get("zIndex")-e.get("zIndex")}))),this},drawChildren:function(t){for(var e=this.get("children"),n=0,i=e.length;n<i;n++){e[n].draw(t)}return this},clear:function(){for(var t=this.get("children");0!==t.length;)t[t.length-1].remove(!0);return this},add:function(t){var e=this.get("children");o(t)||(t=[t]);for(var n=0,i=t.length;n<i;n++){var r=t[n],a=r.get("parent");if(a)Z(a.get("children"),r);this._setEvn(r),e.push(r)}return this},_setEvn:function(t){var e=this._attrs,n=e.context,i=e.canvas,r=e.aria,a=t._attrs,s=a.isGroup,o=a.type;t._attrs.parent=this,t._attrs.context=n,t._attrs.canvas=i,r&&!1!==t._attrs.aria&&(t._attrs.aria=r),"text"===o&&i&&i.get("fontFamily")&&(t._attrs.attrs.fontFamily=t._attrs.attrs.fontFamily||i.get("fontFamily"));var h=t._attrs.attrs.clip;if(h&&(h._attrs.parent=this,h._attrs.context=n,h._attrs.canvas=i),s)for(var u=t._attrs.children,l=0,c=u.length;l<c;l++)t._setEvn(u[l])},_getAriaLabel:function(){var t=this._attrs,e=t.aria,n=t.ariaLabel,i=t.children;if(e){var r=[];if(i&&i.length)for(var a=0,s=i.length;a<s;a++){var o=i[a].getAriaLabel();o&&r.push(o)}var h=r.join(" ");return n&&h?n+" "+h+" ":n||h}}},Ii=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initProperties=function(){this._attrs={type:"group",zIndex:0,visible:!0,destroyed:!1,isGroup:!0,canFill:!0,canStroke:!0,attrs:{},children:[]}},n.getBBox=function(){for(var t=1/0,e=-1/0,n=1/0,i=-1/0,r=this.get("children"),a=0,s=r.length;a<s;a++){var o=r[a];if(o.get("visible")){var h=o.getBBox();if(!h)continue;var u=[h.minX,h.minY],l=[h.minX,h.maxY],c=[h.maxX,h.minY],f=[h.maxX,h.maxY],g=o.attr("matrix");Yt.transformMat2d(u,u,g),Yt.transformMat2d(l,l,g),Yt.transformMat2d(c,c,g),Yt.transformMat2d(f,f,g),t=Math.min(u[0],l[0],c[0],f[0],t),e=Math.max(u[0],l[0],c[0],f[0],e),n=Math.min(u[1],l[1],c[1],f[1],n),i=Math.max(u[1],l[1],c[1],f[1],i)}}return{minX:t,minY:n,maxX:e,maxY:i,x:t,y:n,width:e-t,height:i-n}},n.createPath=function(e){var n=this.get("attrs");(n.fillStyle||n.strokeStyle)&&t.prototype.createPath.call(this,e)},n.drawInner=function(e){t.prototype.drawInner.call(this,e),this.drawChildren(e)},n.destroy=function(){this.get("destroyed")||(this.clear(),t.prototype.destroy.call(this))},e}(ii);D(Ii.prototype,Di,{getGroupClass:function(){return Ii}});var Yi="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:function(t){return setTimeout(t,16)},Oi=function(t){Ct(n,t);var e=n.prototype;function n(e){var n;n=t.call(this)||this;var i=e.title,r=i?w(St.general.withTitle,{title:i}):St.general.title;return n._attrs=D({type:"canvas",children:[],ariaLabel:r},e),n._initPixelRatio(),n._initCanvas(),n}return e.get=function(t){return this._attrs[t]},e.set=function(t,e){this._attrs[t]=e},e._initPixelRatio=function(){this.get("pixelRatio")||this.set("pixelRatio",it())},e.beforeDraw=function(){var t=this._attrs.context,e=this._attrs.el;t&&t.clearRect&&t.clearRect(0,0,e.width,e.height)},e._initCanvas=function(){var t,e=this.get("el"),n=this.get("context");if(!e&&!n)throw new Error("Please specify the id, el or context of the chart!");t=e?p(e)?ot(e):e:Un(n),n&&t&&!t.getContext&&(t.getContext=function(){return n});var i=this.get("width");i||(i=at(t));var r=this.get("height");r||(r=st(t)),this.set("canvas",this),this.set("el",t),this.set("context",n||t.getContext("2d")),this.changeSize(i,r);var a=new qn({canvas:this,el:t});this.set("eventController",a)},e.changeSize=function(t,e){var n=this.get("pixelRatio"),i=this.get("el");(i.style&&(i.style.width=t+"px",i.style.height=e+"px"),nt(i))&&(i.width=t*n,i.height=e*n,1!==n&&this.get("context").scale(n,n));this.set("width",t),this.set("height",e)},e.getWidth=function(){var t=this.get("pixelRatio");return this.get("width")*t},e.getHeight=function(){var t=this.get("pixelRatio");return this.get("height")*t},e.getPointByClient=function(t,e){var n=this.get("el"),i=n.getBoundingClientRect(),r=i.right-i.left,a=i.bottom-i.top;return{x:(t-i.left)*(n.width/r),y:(e-i.top)*(n.height/a)}},e._beginDraw=function(){this._attrs.toDraw=!0},e._endDraw=function(){this._attrs.toDraw=!1},e.draw=function(){var t=this;t.get("destroyed")||(t.get("animateHandler")?this._beginDraw():function e(){t.set("animateHandler",Yi((function(){t.set("animateHandler",void 0),t.get("toDraw")&&e()}))),t.beforeDraw();try{var n=t._attrs.context;t.drawChildren(n),n.draw&&n.draw(),t.setAriaLabel()}catch(e){console.warn("error in draw canvas, detail as:"),console.warn(e),t._endDraw()}t._endDraw()}())},e.setAriaLabel=function(){var t=this._attrs.el,e=this._getAriaLabel();e&&t.setAttribute&&t.setAttribute("aria-label",e)},e.destroy=function(){if(!this.get("destroyed")){var t=this.get("el");t.width=0,t.height=0,this.clear(),this._attrs={},this.set("destroyed",!0)}},e.isDestroyed=function(){return this.get("destroyed")},n}(Tt);D(Oi.prototype,Di,{getGroupClass:function(){return Ii}});var Ei={};function Ni(t){var e=Ei[t];return e||{Canvas:Oi,Group:Ii,Shape:ni}}function Fi(t){return new(Ni(t.renderer).Canvas)(t)}var zi=Object.freeze({__proto__:null,registerEngine:function(t,e){Ei[t]=e},getEngine:Ni,createCanvas:Fi,Canvas:Oi,Group:Ii,Shape:ni,Matrix:It,Vector2:Yt});function Bi(t){var e,n=t.start,i=t.end,r=i.x-n.x,a=Math.abs(i.y-n.y);if(t.isPolar){var s=t.circleRadius,o=t.center,h=t.startAngle,u=t.endAngle;e=new ni.Sector({attrs:{x:o.x,y:o.y,r:s,r0:0,startAngle:h,endAngle:u}})}else e=new ni.Rect({attrs:{x:n.x,y:i.y-10,width:r,height:a+20}});return e.isClip=!0,e}function Li(t,e){var n=t.x,i=t.y,r=e.tl,a=e.tr,s=e.br;return n>=r.x&&n<=a.x&&i>=r.y&&i<=s.y}var Xi=Object.freeze({__proto__:null,getClip:Bi,isPointInPlot:Li});function ji(t,e){return t-e}var Gi=function(t){Ct(n,t),n.initPlugins=function(){return{_plugins:[],_cacheId:0,register:function(t){var e=this._plugins;[].concat(t).forEach((function(t){-1===e.indexOf(t)&&e.push(t)})),this._cacheId++},unregister:function(t){var e=this._plugins;[].concat(t).forEach((function(t){var n=e.indexOf(t);-1!==n&&e.splice(n,1)})),this._cacheId++},clear:function(){this._plugins=[],this._cacheId++},count:function(){return this._plugins.length},getAll:function(){return this._plugins},notify:function(t,e,n){var i,r,a,s,o=this.descriptors(t),h=o.length;for(i=0;i<h;++i)if("function"==typeof(s=(r=o[i].plugin)[e])&&(a=[t].concat(n||[]),!1===s.apply(r,a)))return!1;return!0},descriptors:function(t){var e=t._plugins||(t._plugins={});if(e.id===this._cacheId)return e.descriptors;var n=[],i=[];return this._plugins.concat(t&&t.get("plugins")||[]).forEach((function(t){-1===n.indexOf(t)&&(n.push(t),i.push({plugin:t}))})),e.descriptors=i,e.id=this._cacheId,i}}};var e=n.prototype;function n(e){var n,i=kt(n=t.call(this,e)||this);return u(ie,(function(t,e){var n=M(e);i[n]=function(e){var n=new t(e);return i.addGeom(n),n}})),i._init(),n}return e.getDefaultCfg=function(){return{id:null,renderer:"canvas",rendered:!1,padding:Mt.padding,data:null,scales:{},geoms:[],colDefs:null,pixelRatio:Mt.pixelRatio,filters:null,appendPadding:Mt.appendPadding}},e._syncYScales=function(){if(this.get("syncY")){var t=this.get("geoms"),e=[],n=[],i=[];u(t,(function(t){var r=t.getYScale();r.isLinear&&(e.push(r),n.push(r.min),i.push(r.max))})),n=Math.min.apply(null,n),i=Math.max.apply(null,i),u(e,(function(t){t.change({min:n}),t.change({max:i})}))}},e._getFieldsForLegend=function(){var t=[];return u(this.get("geoms"),(function(e){var n=e.get("attrOptions").color;n&&n.field&&p(n.field)&&u(n.field.split("*"),(function(e){-1===t.indexOf(e)&&t.push(e)}))})),t},e._getScaleData=function(t){var e=this.get("data"),n=this.get("filteredData");n.length&&(-1===this._getFieldsForLegend().indexOf(t)&&(e=n));return e},e._adjustScale=function(){for(var t=this.get("scaleController"),e=this.get("geoms"),n=0;n<e.length;n++){var i=e[n];if("interval"===i.get("type")){var r=i.getYScale();t.adjustStartZero(r)}}},e._removeGeoms=function(){for(var t=this.get("geoms");t.length>0;){t.shift().destroy()}},e._clearGeoms=function(){for(var t=this.get("geoms"),e=0,n=t.length;e<n;e++){t[e].clear()}},e._clearInner=function(){this._clearGeoms(),n.plugins.notify(this,"clearInner"),this.emit("clearinner"),this.get("axisController")&&this.get("axisController").clear()},e._initFilteredData=function(){var t=this.get("filters"),e=this.get("data")||[];t&&(e=e.filter((function(e){var n=!0;return u(t,(function(t,i){if(t&&!(n=t(e[i],e)))return!1})),n}))),this.set("filteredData",e)},e._changeGeomsData=function(){for(var t=this.get("geoms"),e=this.get("filteredData"),n=0,i=t.length;n<i;n++){t[n].changeData(e)}},e._initGeom=function(t){if(!t.get("isInit")){var e=this.get("coord"),n=this.get("filteredData"),i=this.get("colDefs"),r=this.get("middlePlot");t.set("chart",this),t.set("container",r.addGroup()),t.set("data",n),t.set("coord",e),t.set("colDefs",i),t.init(),this.emit("_aftergeominit",t)}},e._initGeoms=function(){for(var t=this.get("geoms"),e=0,n=t.length;e<n;e++)this._initGeom(t[e])},e._initCoord=function(){var t=this.get("plotRange"),e=D({type:"cartesian"},this.get("coordCfg"),{plot:t}),n=e.type,i=new(0,Et[b(n)])(e);this.set("coord",i)},e._initLayout=function(){var t=this.get("_padding");t||(t=dt(t=this.get("margin")||this.get("padding")));var e="auto"===t[0]?0:t[0],n="auto"===t[1]?0:t[1],i="auto"===t[2]?0:t[2],r={x:"auto"===t[3]?0:t[3],y:e},a={x:this.get("width")-n,y:this.get("height")-i},s=this.get("plot");if(s)s.reset(r,a);else{var o=new Dt({start:r,end:a});this.set("plotRange",o),this.set("plot",o)}},e._initCanvas=function(){try{var t=Fi({renderer:this.get("renderer"),el:this.get("el")||this.get("id"),context:this.get("context"),pixelRatio:this.get("pixelRatio"),width:this.get("width"),height:this.get("height"),fontFamily:Mt.fontFamily,aria:this.get("aria"),title:this.get("title"),landscape:this.get("landscape")});this.set("canvas",t),this.set("el",t.get("el")),this.set("width",t.get("width")),this.set("height",t.get("height"))}catch(t){throw t}n.plugins.notify(this,"afterCanvasInit")},e._initLayers=function(){var t=this.get("canvas");this.set("backPlot",t.addGroup()),this.set("middlePlot",t.addGroup({zIndex:10})),this.set("frontPlot",t.addGroup({zIndex:20}))},e._initEvents=function(){var t=this;this.on("afterdatachange",(function(){t._initFilteredData(),t._changeGeomsData()})),this.on("_aftersizechange",(function(){t._initLayout();var e=t.get("coord");e&&e.reset(t.get("plot"))}))},e._initScaleController=function(){var t=new jn({chart:this});this.set("colDefs",t.defs),this.set("scales",t.scales),this.set("scaleController",t)},e._clearScaleController=function(){this.get("scaleController").clear()},e._init=function(){this._initCanvas(),this._initLayout(),this._initLayers(),this._initEvents(),this._initScaleController(),this.set("axisController",new Hn({frontPlot:this.get("frontPlot").addGroup({className:"axisContainer"}),backPlot:this.get("backPlot").addGroup({className:"axisContainer"}),chart:this})),n.plugins.notify(this,"init")},e.init=function(){this._initFilteredData(),this._initCoord(),n.plugins.notify(this,"beforeGeomInit"),this._initGeoms(),this._syncYScales(),this._adjustScale(),this.emit("afterinit")},e.source=function(t,e){return this.set("data",t),e&&this.scale(e),this},e.scale=function(t,e){return this.get("scaleController").setFieldDef(t,e),this},e.axis=function(t,e){var n=this.get("axisController");return t?(n.axisCfg=n.axisCfg||{},n.axisCfg[t]=e):n.axisCfg=null,this},e.coord=function(t,e){var n;return h(t)?n=t:(n=e||{}).type=t||"cartesian",this.set("coordCfg",n),this},e.filter=function(t,e){var n=this.get("filters")||{};n[t]=e,this.set("filters",n),this.get("rendered")&&this.emit("afterdatachange",this.get("data"))},e.render=function(){var t=this.get("rendered"),e=this.get("canvas"),i=this.get("geoms");t?(this._initGeoms(),this._adjustScale()):(this.init(),this.set("rendered",!0)),this.emit("beforerender"),n.plugins.notify(this,"beforeGeomDraw"),this._renderAxis();var r=this.get("middlePlot");if(this.get("limitInPlot")&&!r.attr("clip")){var a=Bi(this.get("coord"));a.set("canvas",r.get("canvas")),r.attr("clip",a)}this.emit("beforegeomdraw");for(var s=0,o=i.length;s<o;s++){i[s].paint()}return this.emit("aftergeomdraw"),n.plugins.notify(this,"afterGeomDraw"),e.sort(),this.get("frontPlot").sort(),n.plugins.notify(this,"beforeCanvasDraw"),e.draw(),this.emit("afterrender"),this},e.clear=function(){return n.plugins.notify(this,"clear"),this.emit("clear"),this._clearInner(),this._removeGeoms(),this._clearScaleController(),this.set("legendItems",null),this.set("filters",null),this.set("isUpdate",!1),this.set("_padding",null),this.set("rendered",!1),this.get("canvas").draw(),this},e.repaint=function(){this.get("rendered")&&(this.set("isUpdate",!0),this.set("legendItems",null),n.plugins.notify(this,"repaint"),this._clearInner(),this.emit("repaint"),this.render())},e.changeData=function(t){this.emit("beforedatachange",t),this.set("data",t),n.plugins.notify(this,"changeData"),this.emit("afterdatachange",t),this.set("_padding",null),this.repaint()},e.changeSize=function(t,e){return t?this.set("width",t):t=this.get("width"),e?this.set("height",e):e=this.get("height"),this.get("canvas").changeSize(t,e),this.emit("_aftersizechange",{width:t,height:e}),this.repaint(),this},e.destroy=function(){this.clear(),this.get("canvas").destroy(),n.plugins.notify(this,"afterCanvasDestroyed"),this._interactions&&u(this._interactions,(function(t){t.destroy()})),t.prototype.destroy.call(this)},e.getPosition=function(t){for(var e=this.get("coord"),n=this.getXScale(),i=n.field,r=this.getYScales(),a=r[0],s=a.field,o=0,h=r.length;o<h;o++){var u=r[o],l=u.field;if(t[l]){a=u,s=l;break}}var c=n.scale(t[i]),f=a.scale(t[s]);return e.convertPoint({x:c,y:f})},e.getRecord=function(t){var e=this.get("coord"),n=this.getXScale(),i=this.getYScales()[0],r=e.invertPoint(t),a={};return a[n.field]=n.invert(r.x),a[i.field]=i.invert(r.y),a},e.getSnapRecords=function(t){var e=this.get("geoms")[0],n=[];return e&&(n=e.getSnapRecords(t)),n},e.createScale=function(t){var e=this._getScaleData(t);return this.get("scaleController").createScale(t,e)},e.addGeom=function(t){this.get("geoms").push(t)},e.getXScale=function(){return this.get("geoms")[0].getXScale()},e.getYScales=function(){var t=this.get("geoms"),e=[];return u(t,(function(t){var n=t.getYScale();-1===e.indexOf(n)&&e.push(n)})),e},e.getLegendItems=function(){if(this.get("legendItems"))return this.get("legendItems");var t={},e=[];return u(this.get("geoms"),(function(n){var i=n.getAttr("color");if(i){var r=i.getScale("color");if(r.isCategory&&!function(t,e){var n=!1;return u(t,(function(t){var i=[].concat(t.values),r=[].concat(e.values);t.type!==e.type||t.field!==e.field||i.sort(ji).toString()!==r.sort(ji).toString()||(n=!0)})),n}(e,r)){e.push(r);var a=r.field,s=r.getTicks(),o=[];u(s,(function(t){var e=t.text,n=t.value,a=r.invert(n),s={fill:i.mapping(a).join("")||Mt.defaultColor,radius:3,symbol:"circle",stroke:"#fff"};o.push({name:e,dataValue:a,checked:!0,marker:s})})),t[a]=o}}})),this.set("legendItems",t),t},e.registerPlugins=function(t){var e=this,i=e.get("plugins")||[];o(i)||(i=[i]),[].concat(t).forEach((function(t){-1===i.indexOf(t)&&(t.init&&t.init(e),i.push(t))})),n.plugins._cacheId++,e.set("plugins",i)},e._renderAxis=function(){var t=this.get("axisController"),e=this.getXScale(),i=this.getYScales(),r=this.get("coord");n.plugins.notify(this,"beforeRenderAxis"),t.createAxis(r,e,i)},e._isAutoPadding=function(){if(this.get("_padding"))return!1;var t=this.get("padding");return o(t)?-1!==t.indexOf("auto"):"auto"===t},e._updateLayout=function(t){var e=this.get("width"),n=this.get("height"),i={x:t[3],y:t[0]},r={x:e-t[1],y:n-t[2]},a=this.get("plot"),s=this.get("coord");a.reset(i,r),s.reset(a)},e.landscape=function(t){this.get("canvas").set("landscape",t)},n}(At);Gi.plugins=Gi.initPlugins();var Ri=function(){return null};function Hi(t){var e=[],n=t.x,i=t.y;return(i=o(i)?i:[i]).forEach((function(t,i){var r={x:o(n)?n[i]:n,y:t};e.push(r)})),e}function Wi(t,e,n){if(!t.length)return[];var i,r=[],a=[];return u(t,(function(t){i=t._origin?t._origin[e]:t[e],n?s(i)||a.push(t):o(i)&&s(i[0])||s(i)?a.length&&(r.push(a),a=[]):a.push(t)})),a.length&&r.push(a),r}Mt.version;function Vi(t,e,n){if(0!==t.size){var i=function(t){var e={lineWidth:0,stroke:t.color,fill:t.color};return t.size&&(e.size=t.size),D(e,t.style),D({},Mt.shape.point,e)}(t),r=i.r||i.size,a=t.x,s=o(t.y)?t.y:[t.y];"hollowCircle"===n&&(i.lineWidth=1,i.fill=null);for(var h=0,u=s.length;h<u;h++)return"rect"===n?e.addShape("Rect",{className:"point",attrs:D({x:a-r,y:s[h]-r,width:2*r,height:2*r},i)}):e.addShape("Circle",{className:"point",attrs:D({x:a,y:s[h],r:r},i)})}}Zt.registerFactory("point",{defaultShapeType:"circle",getDefaultPoints:function(t){return Hi(t)}}),u(["circle","hollowCircle","rect"],(function(t){Zt.registerShape("point",t,{draw:function(e,n){return Vi(e,n,t)}})}));var qi=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n.getDefaultCfg=function(){var e=t.prototype.getDefaultCfg.call(this);return e.type="point",e.shapeType="point",e.generatePoints=!1,e},n.draw=function(t,e){var n=this,i=n.get("container");u(t,(function(t){var r=t.shape,a=n.getDrawCfg(t);if(o(t.y)){var h=n.hasAdjust("stack");u(t.y,(function(s,o){a.y=s,h&&0===o||n.drawShape(r,t,a,i,e)}))}else s(t.y)||n.drawShape(r,t,a,i,e)}))},e}(ie);ie.Point=qi,Zt.registerFactory("line",{defaultShapeType:"line"});u(["line","smooth","dash"],(function(t){Zt.registerShape("line",t,{draw:function(e,n){var i="smooth"===t,r=function(t){var e={strokeStyle:t.color};return t.size>=0&&(e.lineWidth=t.size),D(e,t.style),D({},Mt.shape.line,e)}(e);return"dash"===t&&(r.lineDash=Mt.lineDash),function(t,e,n,i){var r=t.points;if(r.length&&o(r[0].y)){for(var a=[],s=[],h=0,u=r.length;h<u;h++){var l=Hi(r[h]);s.push(l[0]),a.push(l[1])}return t.isInCircle&&(a.push(a[0]),s.push(s[0])),t.isStack?e.addShape("Polyline",{className:"line",attrs:D({points:a,smooth:i},n)}):[e.addShape("Polyline",{className:"line",attrs:D({points:a,smooth:i},n)}),e.addShape("Polyline",{className:"line",attrs:D({points:s,smooth:i},n)})]}return t.isInCircle&&r.push(r[0]),e.addShape("Polyline",{className:"line",attrs:D({points:r,smooth:i},n)})}(e,n,r,i)}})}));var Zi=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n.getDefaultCfg=function(){var e=t.prototype.getDefaultCfg.call(this);return e.type="path",e.shapeType="line",e},n.getDrawCfg=function(e){var n=t.prototype.getDrawCfg.call(this,e);return n.isStack=this.hasAdjust("stack"),n},n.draw=function(t,e){var n=this,i=n.get("container"),r=n.getYScale(),a=n.get("connectNulls"),s=Wi(t,r.field,a),o=this.getDrawCfg(t[0]);o.origin=t,u(s,(function(r,a){o.splitedIndex=a,o.points=r,n.drawShape(o.shape,t[0],o,i,e)}))},e}(ie);ie.Path=Zi;var Ui=function(t){function e(){return t.apply(this,arguments)||this}return Ct(e,t),e.prototype.getDefaultCfg=function(){var e=t.prototype.getDefaultCfg.call(this);return e.type="line",e.sortable=!0,e},e}(Zi);function Ji(t,e){return Math.abs(t-e)<1e-5}function $i(t){return!isNaN(t)&&!s(t)}function Ki(t){for(var e=[],n=0,i=t.length;n<i;n++){var r=t[n];$i(r.x)&&$i(r.y)&&e.push(r)}return e}function Qi(t,e,n){var i=t.points,r=[],a=[];u(i,(function(t){a.push(t[0]),r.push(t[1])}));var s=D({fillStyle:t.color},Mt.shape.area,t.style);return a.reverse(),r=this.parsePoints(r),a=this.parsePoints(a),t.isInCircle&&(r.push(r[0]),a.unshift(a[a.length-1]),function(t,e){var n=!0;return u(t,(function(t){if(!Ji(t.x,e.x)||!Ji(t.y,e.y))return n=!1,!1})),n}(a,t.center)&&(a=[])),function(t,e,n,i,r){var a=t.concat(e);return r?n.addShape("Custom",{className:"area",attrs:D({points:a},i),createPath:function(t){var e=[[0,0],[1,1]],n=Ki(this._attrs.attrs.points),i=n.length,r=n.slice(0,i/2),a=n.slice(i/2,i),s=yi(r,!1,e);t.beginPath(),t.moveTo(r[0].x,r[0].y);for(var o=0,h=s.length;o<h;o++){var u=s[o];t.bezierCurveTo(u[1],u[2],u[3],u[4],u[5],u[6])}if(a.length){var l=yi(a,!1,e);t.lineTo(a[0].x,a[0].y);for(var c=0,f=l.length;c<f;c++){var g=l[c];t.bezierCurveTo(g[1],g[2],g[3],g[4],g[5],g[6])}}t.closePath()},calculateBox:function(){return fi(Ki(this._attrs.attrs.points))}}):n.addShape("Polyline",{className:"area",attrs:D({points:a},i)})}(r,a,e,s,n)}ie.Line=Ui,Zt.registerFactory("area",{defaultShapeType:"area",getDefaultPoints:function(t){var e=t.x,n=t.y,i=t.y0;n=o(n)?n:[i,n];var r=[];return r.push({x:e,y:n[0]},{x:e,y:n[1]}),r}});u(["area","smooth"],(function(t){Zt.registerShape("area",t,{draw:function(e,n){var i="smooth"===t;return Qi.call(this,e,n,i)}})}));var tr=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n.getDefaultCfg=function(){var e=t.prototype.getDefaultCfg.call(this);return e.type="area",e.shapeType="area",e.generatePoints=!0,e.sortable=!0,e},n.draw=function(t,e){var n=this,i=n.get("container"),r=this.getDrawCfg(t[0]),a=n.getYScale(),s=n.get("connectNulls"),o=Wi(t,a.field,s);r.origin=t,u(o,(function(a,s){r.splitedIndex=s;var o=a.map((function(t){return t.points}));r.points=o,n.drawShape(r.shape,t[0],r,i,e)}))},e}(ie);ie.Area=tr;var er={initEvent:function(){var t=this,e=this.get("chart");e&&e.on("_aftersizechange",(function(){t.set("_width",null)}))},getDefaultSize:function(){var t=this.get("defaultSize");if(!t){var e=this.get("coord"),n=this.getXScale(),i=this.get("dataArray"),r=d(n.values).length,a=n.range,s=1/r,o=1;e&&e.isPolar?o=e.transposed&&r>1?Mt.widthRatio.multiplePie:Mt.widthRatio.rose:(n.isLinear&&(s*=a[1]-a[0]),o=Mt.widthRatio.column),s*=o,this.hasAdjust("dodge")&&(s/=i.length),t=s,this.set("defaultSize",t)}return t},getDimWidth:function(t){var e=this.get("coord"),n=e.convertPoint({x:0,y:0}),i=e.convertPoint({x:"x"===t?1:0,y:"x"===t?0:1}),r=0;return n&&i&&(r=Math.sqrt(Math.pow(i.x-n.x,2)+Math.pow(i.y-n.y,2))),r},_getWidth:function(){var t=this.get("_width");if(!t){var e=this.get("coord");t=e&&e.isPolar&&!e.transposed?(e.endAngle-e.startAngle)*e.circleRadius:this.getDimWidth("x"),this.set("_width",t)}return t},_toNormalizedSize:function(t){return t/this._getWidth()},_toCoordSize:function(t){return this._getWidth()*t},getNormalizedSize:function(t){var e=this.getAttrValue("size",t);return e=s(e)?this.getDefaultSize():this._toNormalizedSize(e)},getSize:function(t){var e=this.getAttrValue("size",t);if(s(e)){var n=this.getDefaultSize();e=this._toCoordSize(n)}return e}};function nr(t){var e,n,i=t.x,r=t.y,a=t.y0,s=t.size,h=a,u=r;return o(r)&&(u=r[1],h=r[0]),o(i)?(e=i[0],n=i[1]):(e=i-s/2,n=i+s/2),[{x:e,y:h},{x:e,y:u},{x:n,y:u},{x:n,y:h}]}Zt.registerFactory("interval",{defaultShapeType:"rect",getDefaultPoints:function(t){return nr(t)}}),Zt.registerShape("interval","rect",{draw:function(t,e){var n=this.parsePoints(t.points),i=D({fill:t.color},Mt.shape.interval,t.style);if(t.isInCircle){var r=n.slice(0);this._coord.transposed&&(r=[n[0],n[3],n[2],n[1]]);var a=t.center,s=a.x,o=a.y,h=[1,0],u=[r[0].x-s,r[0].y-o],l=[r[1].x-s,r[1].y-o],c=[r[2].x-s,r[2].y-o],f=Yt.angleTo(h,l),g=Yt.angleTo(h,c),p=Yt.length(u),d=Yt.length(l);return f>=1.5*Math.PI&&(f-=2*Math.PI),g>=1.5*Math.PI&&(g-=2*Math.PI),e.addShape("Sector",{className:"interval",attrs:D({x:s,y:o,r:d,r0:p,startAngle:f,endAngle:g},i)})}var v=function(t){for(var e=[],n=[],i=0,r=t.length;i<r;i++){var a=t[i];e.push(a.x),n.push(a.y)}var s=Math.min.apply(null,e),o=Math.min.apply(null,n);return{x:s,y:o,width:Math.max.apply(null,e)-s,height:Math.max.apply(null,n)-o}}(n);return e.addShape("rect",{className:"interval",attrs:D(v,i)})}}),["pyramid","funnel"].forEach((function(t){Zt.registerShape("interval",t,{getPoints:function(t){return t.size=2*t.size,nr(t)},draw:function(e,n){var i,r,a=this.parsePoints(e.points),s=this.parsePoints(e.nextPoints),o=null;s?o=[a[0],a[1],s[1],s[0]]:(o=[a[0],a[1]],"pyramid"===t?o.push((i=a[2],r=a[3],{x:(i.x-r.x)/2+r.x,y:(i.y-r.y)/2+r.y})):o.push(a[2],a[3]));var h=D({fill:e.color,points:o},Mt.shape.interval,e.style);return n.addShape("polygon",{className:"interval",attrs:h})}})}));var ir=function(t){Ct(n,t);var e=n.prototype;function n(e){var n;return D(kt(n=t.call(this,e)||this),er),n}return e.getDefaultCfg=function(){var e=t.prototype.getDefaultCfg.call(this);return e.type="interval",e.shapeType="interval",e.generatePoints=!0,e},e.init=function(){t.prototype.init.call(this),this.initEvent()},e.createShapePointsCfg=function(e){var n=t.prototype.createShapePointsCfg.call(this,e);return n.size=this.getNormalizedSize(e),n},e.clearInner=function(){t.prototype.clearInner.call(this),this.set("defaultSize",null)},n}(ie);ie.Interval=ir,Zt.registerFactory("polygon",{defaultShapeType:"polygon",getDefaultPoints:function(t){for(var e=[],n=t.x,i=t.y,r=0,a=n.length;r<a;r++)e.push({x:n[r],y:i[r]});return e}}),Zt.registerShape("polygon","polygon",{draw:function(t,e){var n=this.parsePoints(t.points),i=D({fill:t.color,points:n},t.style);return e.addShape("Polygon",{className:"polygon",attrs:i})}});var rr=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n.getDefaultCfg=function(){var e=t.prototype.getDefaultCfg.call(this);return e.type="polygon",e.shapeType="polygon",e.generatePoints=!0,e},n.createShapePointsCfg=function(e){var n,i=t.prototype.createShapePointsCfg.call(this,e),r=i.x,a=i.y;if(!o(r)||!o(a)){var s=this.getXScale(),h=this.getYScale(),u=.5/(s.values?s.values.length:s.ticks.length),l=.5/(h.values?h.values.length:h.ticks.length);s.isCategory&&h.isCategory?(r=[r-u,r-u,r+u,r+u],a=[a-l,a+l,a+l,a-l]):o(r)?(r=[(n=r)[0],n[0],n[1],n[1]],a=[a-l/2,a+l/2,a+l/2,a-l/2]):o(a)&&(a=[(n=a)[0],n[1],n[1],n[0]],r=[r-u/2,r-u/2,r+u/2,r+u/2]),i.x=r,i.y=a}return i},e}(ie);ie.Polygon=rr,Zt.registerFactory("schema",{}),Zt.registerShape("schema","candle",{getPoints:function(t){return e=t.x,n=t.y,i=t.size,r=function(t){var e=t.sort((function(t,e){return t<e?1:-1})),n=e.length;if(n<4)for(var i=e[n-1],r=0;r<4-n;r++)e.push(i);return e}(n),[{x:e,y:r[0]},{x:e,y:r[1]},{x:e-i/2,y:r[2]},{x:e-i/2,y:r[1]},{x:e+i/2,y:r[1]},{x:e+i/2,y:r[2]},{x:e,y:r[2]},{x:e,y:r[3]}];var e,n,i,r},draw:function(t,e){var n=this.parsePoints(t.points),i=D({stroke:t.color,fill:t.color,lineWidth:1},t.style);return e.addShape("Custom",{className:"schema",attrs:i,createPath:function(t){t.beginPath(),t.moveTo(n[0].x,n[0].y),t.lineTo(n[1].x,n[1].y),t.moveTo(n[2].x,n[2].y);for(var e=3;e<6;e++)t.lineTo(n[e].x,n[e].y);t.closePath(),t.moveTo(n[6].x,n[6].y),t.lineTo(n[7].x,n[7].y)}})}});var ar=function(t){Ct(n,t);var e=n.prototype;function n(e){var n;return D(kt(n=t.call(this,e)||this),er),n}return e.getDefaultCfg=function(){var e=t.prototype.getDefaultCfg.call(this);return e.type="schema",e.shapeType="schema",e.generatePoints=!0,e},e.init=function(){t.prototype.init.call(this),this.initEvent()},e.createShapePointsCfg=function(e){var n=t.prototype.createShapePointsCfg.call(this,e);return n.size=this.getNormalizedSize(e),n},e.clearInner=function(){t.prototype.clearInner.call(this),this.set("defaultSize",null)},n}(ie);ie.Schema=ar;var sr={}.toString,or=function(t,e){return sr.call(t)==="[object "+e+"]"},hr=Array.isArray?Array.isArray:function(t){return or(t,"Array")},ur=function(t){return null==t};var lr=function(t){var e,n;function i(){return t.apply(this,arguments)||this}n=t,(e=i).prototype=Object.create(n.prototype),e.prototype.constructor=e,e.__proto__=n;var r=i.prototype;return r._initDefaultCfg=function(){this.xField=null,this.yField=null},r.processAdjust=function(t){this.processStack(t)},r.processStack=function(t){var e=this.xField,n=this.yField,i=t.length,r={positive:{},negative:{}};this.reverseOrder&&(t=t.slice(0).reverse());for(var a=0;a<i;a++)for(var s=t[a],o=0,h=s.length;o<h;o++){var u=s[o],l=u[e]||0,c=u[n],f=l.toString();if(c=hr(c)?c[1]:c,!ur(c)){var g=c>=0?"positive":"negative";r[g][f]||(r[g][f]=0),u[n]=[r[g][f],c+r[g][f]],r[g][f]+=c}}},i}(Qt);Qt.Stack=lr;var cr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fr=function(t){var e=void 0===t?"undefined":cr(t);return null!==t&&"object"===e||"function"===e},gr=function(t,e){if(t){if(hr(t))for(var n=0,i=t.length;n<i&&!1!==e(t[n],n);n++);else if(fr(t))for(var r in t)if(t.hasOwnProperty(r)&&!1===e(t[r],r))break}};var pr=function(t){var e,n;function i(){return t.apply(this,arguments)||this}n=t,(e=i).prototype=Object.create(n.prototype),e.prototype.constructor=e,e.__proto__=n;var r=i.prototype;return r._initDefaultCfg=function(){this.marginRatio=.5,this.dodgeRatio=.5,this.adjustNames=["x","y"]},r.getDodgeOffset=function(t,e,n){var i=t.pre,r=t.next,a=r-i,s=a*this.dodgeRatio/n,o=this.marginRatio*s;return(i+r)/2+(.5*(a-n*s-(n-1)*o)+((e+1)*s+e*o)-.5*s-.5*a)},r.processAdjust=function(t){var e=this,n=t.length,i=e.xField;gr(t,(function(t,r){for(var a=0,s=t.length;a<s;a++){var o=t[a],h=o[i],u={pre:1===s?h-1:h-.5,next:1===s?h+1:h+.5},l=e.getDodgeOffset(u,r,n);o[i]=l}}))},i}(Qt);Qt.Dodge=pr;var dr=function(t){return or(t,"Function")},vr=function(t,e){if(hr(t)){var n=t[0],i=void 0;i=dr(e)?e(t[0]):t[0][e];var r=void 0;return gr(t,(function(t){(r=dr(e)?e(t):t[e])>i&&(n=t,i=r)})),n}};var yr={merge:function(t){for(var e=[],n=0;n<t.length;n++)e=e.concat(t[n]);return e}},mr=function(t){var e,n;function i(){return t.apply(this,arguments)||this}n=t,(e=i).prototype=Object.create(n.prototype),e.prototype.constructor=e,e.__proto__=n;var r=i.prototype;return r._initDefaultCfg=function(){this.xField=null,this.yField=null,this.cacheMax=null,this.adjustNames=["y"],this.groupFields=null},r._getMax=function(t){var e=this.mergeData,n=vr(e,(function(e){var n=e[t];return hr(n)?Math.max.apply(null,n):n}))[t];return hr(n)?Math.max.apply(null,n):n},r._getXValuesMax=function(){var t=this.yField,e=this.xField,n={},i=this.mergeData;return gr(i,(function(i){var r=i[e],a=i[t],s=hr(a)?Math.max.apply(null,a):a;n[r]=n[r]||0,n[r]<s&&(n[r]=s)})),n},r.processAdjust=function(t){var e=yr.merge(t);this.mergeData=e,this._processSymmetric(t),this.mergeData=null},r._processSymmetric=function(t){var e,n=this.xField,i=this.yField,r=this._getMax(i),a=t[0][0];a&&hr(a[i])&&(e=this._getXValuesMax()),gr(t,(function(t){gr(t,(function(t){var a,s=t[i];if(hr(s)){var o=t[n],h=e[o];a=(r-h)/2;var u=[];gr(s,(function(t){u.push(a+t)})),t[i]=u}else a=(r-s)/2,t[i]=[a,s+a]}))}))},i}(Qt);Qt.Symmetric=mr;var xr=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initDefaultCfg=function(){this.type="polar",this.startAngle=-Math.PI/2,this.endAngle=3*Math.PI/2,this.inner=0,this.innerRadius=0,this.isPolar=!0,this.transposed=!1,this.center=null,this.radius=null},n.init=function(e,n){t.prototype.init.call(this,e,n);var i,r,a=this.inner||this.innerRadius,s=Math.abs(n.x-e.x),o=Math.abs(n.y-e.y);this.startAngle===-Math.PI&&0===this.endAngle?(i=Math.min(s/2,o),r={x:(e.x+n.x)/2,y:e.y}):(i=Math.min(s,o)/2,r={x:(e.x+n.x)/2,y:(e.y+n.y)/2});var h=this.radius;h>0&&h<=1&&(i*=h),this.x={start:this.startAngle,end:this.endAngle},this.y={start:i*a,end:i},this.center=r,this.circleRadius=i},n._convertPoint=function(t){var e=this.center,n=this.transposed,i=n?"y":"x",r=n?"x":"y",a=this.x,s=this.y,o=a.start+(a.end-a.start)*t[i],h=s.start+(s.end-s.start)*t[r];return{x:e.x+Math.cos(o)*h,y:e.y+Math.sin(o)*h}},n._invertPoint=function(t){var e=this.center,n=this.transposed,i=this.x,r=this.y,a=n?"y":"x",s=n?"x":"y",o=[1,0,0,1,0,0];It.rotate(o,o,i.start);var h=[1,0];Yt.transformMat2d(h,h,o),h=[h[0],h[1]];var u=[t.x-e.x,t.y-e.y];if(Yt.zero(u))return{x:0,y:0};var l=Yt.angleTo(h,u,i.end<i.start);Math.abs(l-2*Math.PI)<.001&&(l=0);var c=Yt.length(u),f=l/(i.end-i.start);f=i.end-i.start>0?f:-f;var g=(c-r.start)/(r.end-r.start),p={};return p[a]=f,p[s]=g,p},e}(Et);Et.Polar=xr;var _r=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initDefaultCfg=function(){t.prototype._initDefaultCfg.call(this),this.startAngle=-Math.PI/2,this.endAngle=3*Math.PI/2,this.radius=null,this.center=null},n.getOffsetPoint=function(t){var e=this.startAngle,n=e+(this.endAngle-e)*t;return this._getCirclePoint(n)},n._getCirclePoint=function(t,e){var n=this.center;return e=e||this.radius,{x:n.x+Math.cos(t)*e,y:n.y+Math.sin(t)*e}},n.getTextAlignInfo=function(t,e){var n,i=this.getOffsetVector(t,e),r="middle";return i[0]>0?n="left":i[0]<0?n="right":(n="center",i[1]>0?r="top":i[1]<0&&(r="bottom")),{textAlign:n,textBaseline:r}},n.getAxisVector=function(t){var e=this.center,n=this.offsetFactor;return[(t.y-e.y)*n,-1*(t.x-e.x)*n]},n.drawLine=function(t){var e=this.center,n=this.radius,i=this.startAngle,r=this.endAngle;this.getContainer(t.top).addShape("arc",{className:"axis-line",attrs:D({x:e.x,y:e.y,r:n,startAngle:i,endAngle:r},t)})},e}(Gn);Gn.Circle=_r;var Sr={min:0,median:.5,max:1},Mr=function(){var t=e.prototype;function e(t){this._initDefaultCfg(),Y(this,t)}return t._initDefaultCfg=function(){},t._getNormalizedValue=function(t,e){return s(Sr[t])?e.scale(t):Sr[t]},t.parsePercentPoint=function(t,e){var n=parseFloat(e[0])/100,i=parseFloat(e[1])/100,r=t.start,a=t.end,s=Math.abs(r.x-a.x),o=Math.abs(r.y-a.y);return{x:s*n+Math.min(r.x,a.x),y:o*i+Math.min(r.y,a.y)}},t.parsePoint=function(t,e){var n=this.xScale,i=this.yScales;if(a(e)&&(e=e(n,i)),p(e[0])&&-1!==e[0].indexOf("%")&&!isNaN(e[0].slice(0,-1)))return this.parsePercentPoint(t,e);var r=this._getNormalizedValue(e[0],n),s=this._getNormalizedValue(e[1],i[0]),o=t.convertPoint({x:r,y:s});return this.limitInPlot?r>=0&&r<=1&&s>=0&&s<=1?o:null:o},t.render=function(){},t.repaint=function(){this.remove();var t=this.coord,e=this.container,n=this.canvas;e&&!e.isDestroyed()&&(this.render(t,e),n.draw())},t.remove=function(){var t=this.element;t&&t.remove(!0)},t.changeVisible=function(t){this.visible=t;var e=this.element;e&&(e.set?e.set("visible",t):e.style.display=t?"":"none")},e}(),wr=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initDefaultCfg=function(){this.type="arc",this.start=[],this.end=[],this.style={stroke:"#999",lineWidth:1}},n.render=function(t,e){var n=this.parsePoint(t,this.start),i=this.parsePoint(t,this.end);if(n&&i){var r=t.center,a=Math.sqrt((n.x-r.x)*(n.x-r.x)+(n.y-r.y)*(n.y-r.y)),s=Math.atan2(n.y-r.y,n.x-r.x),o=Math.atan2(i.y-r.y,i.x-r.x),h=e.addShape("arc",{className:"guide-arc",attrs:D({x:r.x,y:r.y,r:a,startAngle:s,endAngle:o},this.style)});return this.element=h,h}},e}(Mr);function br(t,e){for(var n in e)e.hasOwnProperty(n)&&(t.style[n]=e[n]);return t}function Cr(t){var e=document.createElement("div");return t=t.replace(/(^\s*)|(\s*$)/g,""),e.innerHTML=""+t,e.childNodes[0]}Mr.Arc=wr;var Pr=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initDefaultCfg=function(){this.type="html",this.position=null,this.alignX="center",this.alignY="middle",this.offsetX=null,this.offsetY=null,this.html=null},n.render=function(t,e){var n=this.parsePoint(t,this.position);if(n){var i=Cr(this.html);i=br(i,{position:"absolute",top:Math.floor(n.y)+"px",left:Math.floor(n.x)+"px",visibility:"hidden"});var r=e.get("canvas").get("el"),a=r.parentNode;a=br(a,{position:"relative"});var s=Cr('<div class="guideWapper" style="position: absolute;top: 0; left: 0;"></div>');a.appendChild(s),s.appendChild(i);var o=r.offsetTop,h=r.offsetLeft,u=this.alignX,l=this.alignY,c=this.offsetX,f=this.offsetY,g=function(t,e,n,i){var r=[];return"left"===t&&"top"===e?(r[0]=0,r[1]=0):"right"===t&&"top"===e?(r[0]=-n,r[1]=0):"left"===t&&"bottom"===e?(r[0]=0,r[1]=Math.floor(-i)):"right"===t&&"bottom"===e?(r[0]=Math.floor(-n),r[1]=Math.floor(-i)):"right"===t&&"middle"===e?(r[0]=Math.floor(-n),r[1]=Math.floor(-i/2)):"left"===t&&"middle"===e?(r[0]=0,r[1]=Math.floor(-i/2)):"center"===t&&"bottom"===e?(r[0]=Math.floor(-n/2),r[1]=Math.floor(-i)):"center"===t&&"top"===e?(r[0]=Math.floor(-n/2),r[1]=0):(r[0]=Math.floor(-n/2),r[1]=Math.floor(-i/2)),r}(u,l,at(i),st(i));n.x=n.x+g[0]+h,n.y=n.y+g[1]+o,c&&(n.x+=c),f&&(n.y+=f),br(i,{top:Math.floor(n.y)+"px",left:Math.floor(n.x)+"px",visibility:"visible"}),this.element=s}},n.remove=function(){var t=this.element;t&&t.parentNode&&t.parentNode.removeChild(t)},e}(Mr);Mr.Html=Pr;var kr=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initDefaultCfg=function(){this.type="line",this.start=[],this.end=[],this.style={stroke:"#000",lineWidth:1}},n.render=function(t,e){var n=[];if(n[0]=this.parsePoint(t,this.start),n[1]=this.parsePoint(t,this.end),n[0]&&n[1]){var i=e.addShape("Line",{className:"guide-line",attrs:D({x1:n[0].x,y1:n[0].y,x2:n[1].x,y2:n[1].y},this.style)});return this.element=i,i}},e}(Mr);Mr.Line=kr;var Tr=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initDefaultCfg=function(){this.type="rect",this.start=[],this.end=[],this.style={fill:"#CCD7EB",opacity:.4}},n.render=function(t,e){var n=this.parsePoint(t,this.start),i=this.parsePoint(t,this.end);if(n&&i){var r=e.addShape("rect",{className:"guide-rect",attrs:D({x:Math.min(n.x,i.x),y:Math.min(n.y,i.y),width:Math.abs(i.x-n.x),height:Math.abs(n.y-i.y)},this.style)});return this.element=r,r}},e}(Mr);Mr.Rect=Tr;var Ar=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initDefaultCfg=function(){this.type="text",this.position=null,this.content=null,this.style={fill:"#000"},this.offsetX=0,this.offsetY=0},n.render=function(t,e){var n=this.position,i=this.parsePoint(t,n);if(i){var r=this.content,a=this.style,s=this.offsetX,o=this.offsetY;s&&(i.x+=s),o&&(i.y+=o);var h=e.addShape("text",{className:"guide-text",attrs:D({x:i.x,y:i.y,text:r},a)});return this.element=h,h}},e}(Mr);Mr.Text=Ar;var Dr=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initDefaultCfg=function(){this.type="tag",this.position=null,this.content=null,this.direct="tl",this.autoAdjust=!0,this.offsetX=0,this.offsetY=0,this.side=4,this.background={padding:5,radius:2,fill:"#1890FF"},this.textStyle={fontSize:12,fill:"#fff",textAlign:"center",textBaseline:"middle"},this.withPoint=!0,this.pointStyle={fill:"#1890FF",r:3,lineWidth:1,stroke:"#fff"}},n._getDirect=function(t,e,n,i){var r=this.direct,a=this.side,s=t.get("canvas"),o=s.get("width"),h=s.get("height"),u=e.x,l=e.y,c=r[0],f=r[1];"t"===c&&l-a-i<0?c="b":"b"===c&&l+a+i>h&&(c="t");var g="c"===c?a:0;return"l"===f&&u-g-n<0?f="r":"r"===f&&u+g+n>o?f="l":"c"===f&&(n/2+u+g>o?f="l":u-n/2-g<0&&(f="r")),r=c+f},n.render=function(t,e){var n=this.parsePoint(t,this.position);if(n&&!isNaN(n.x)&&!isNaN(n.y)){var i=this.content,r=this.background,a=this.textStyle,s=[],o=e.addGroup({className:"guide-tag"});if(this.withPoint){var h=o.addShape("Circle",{className:"guide-tag-point",attrs:D({x:n.x,y:n.y},this.pointStyle)});s.push(h)}var u=o.addGroup(),l=u.addShape("text",{className:"guide-tag-text",zIndex:1,attrs:D({x:0,y:0,text:i},a)});s.push(l);var c=l.getBBox(),f=dt(r.padding),g=c.width+f[1]+f[3],p=c.height+f[0]+f[2],d=c.minY-f[0],v=c.minX-f[3],y=u.addShape("rect",{className:"guide-tag-bg",zIndex:-1,attrs:D({x:v,y:d,width:g,height:p},r)});s.push(y);var m,x=this.autoAdjust?this._getDirect(e,n,g,p):this.direct,_=this.side,S=n.x+this.offsetX,M=n.y+this.offsetY,w=dt(r.radius);"tl"===x?(m=[{x:g+v-_-1,y:p+d-1},{x:g+v,y:p+d-1},{x:g+v,y:p+_+d}],w[2]=0,S-=g,M=M-_-p):"cl"===x?(m=[{x:g+v-1,y:(p-_)/2+d-1},{x:g+v-1,y:(p+_)/2+d+1},{x:g+_+v,y:p/2+d}],S=S-g-_,M-=p/2):"bl"===x?(m=[{x:g+v,y:-_+d},{x:g+v-_-1,y:d+1},{x:g+v,y:d+1}],w[1]=0,S-=g,M+=_):"bc"===x?(m=[{x:g/2+v,y:-_+d},{x:(g-_)/2+v-1,y:d+1},{x:(g+_)/2+v+1,y:d+1}],S-=g/2,M+=_):"br"===x?(m=[{x:v,y:d-_},{x:v,y:d+1},{x:v+_+1,y:d+1}],w[0]=0,M+=_):"cr"===x?(m=[{x:v-_,y:p/2+d},{x:v+1,y:(p-_)/2+d-1},{x:v+1,y:(p+_)/2+d+1}],S+=_,M-=p/2):"tr"===x?(m=[{x:v,y:p+_+d},{x:v,y:p+d-1},{x:_+v+1,y:p+d-1}],w[3]=0,M=M-p-_):"tc"===x&&(m=[{x:(g-_)/2+v-1,y:p+d-1},{x:(g+_)/2+v+1,y:p+d-1},{x:g/2+v,y:p+_+d}],S-=g/2,M=M-p-_);var b=u.addShape("Polygon",{className:"guide-tag-side",zIndex:0,attrs:{points:m,fill:r.fill}});return s.push(b),y.attr("radius",w),u.moveTo(S-v,M-d),u.sort(),this.element=o,s}},e}(Mr);Mr.Tag=Dr;var Ir=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n._initDefaultCfg=function(){this.type="point",this.position=null,this.offsetX=0,this.offsetY=0,this.style={fill:"#1890FF",r:3,lineWidth:1,stroke:"#fff"}},n.render=function(t,e){var n=this.parsePoint(t,this.position);if(!n)return null;var i=e.addShape("Circle",{className:"guide-point",attrs:D({x:n.x+this.offsetX,y:n.y+this.offsetY},this.style)});return this.element=i,i},e}(Mr);Mr.Point=Ir;var Yr=function(){var t=e.prototype;function e(t){Y(this,this.getDefaultCfg(),t),this._init(),this._renderTitle(),this._renderItems()}return t.getDefaultCfg=function(){return{showTitle:!1,title:null,items:null,titleGap:12,itemGap:10,itemMarginBottom:12,itemFormatter:null,itemWidth:null,wordSpace:6,x:0,y:0,layout:"horizontal",joinString:": "}},t._init=function(){var t=this.parent;if(t){var e=t.addGroup({zIndex:this.zIndex||0});this.container=e;var n=e.addGroup();this.wrapper=n;var i=n.addGroup({className:"itemsGroup"});this.itemsGroup=i}},t._renderTitle=function(t){t=t||this.title;var e=this.titleShape,n=0;if(this.showTitle&&t){if(e&&!e.get("destroyed"))e.attr("text",t);else{var i=this.wrapper,r=this.titleStyle;e=i.addShape("text",{className:"title",attrs:D({x:0,y:0,text:t},r)}),this.titleShape=e}n=e.getBBox().height+this.titleGap}this._titleHeight=n},t._renderItems=function(t){var e=this;(t=t||e.items)&&(e.reversed&&t.reverse(),u(t,(function(t,n){e._addItem(t,n)})),t.length>1&&this._adjustItems(),this._renderBackground())},t._renderBackground=function(){var t=this.background;if(t){var e=this.container,n=this.wrapper.getBBox(),i=n.minX,r=n.minY,a=n.width,s=n.height,o=t.padding||[0,0,0,0],h=D({x:i-(o=dt(o))[3],y:r-o[0],width:a+o[1]+o[3],height:s+o[0]+o[2]},t),u=this.backShape;u?u.attr(h):u=e.addShape("Rect",{zIndex:-1,attrs:h}),this.backShape=u,e.sort()}},t._addItem=function(t){var e,n=this.itemsGroup.addGroup({name:t.name,value:t.value,dataValue:t.dataValue,checked:t.checked}),i=this.unCheckStyle,r=this.unCheckColor,a=this.nameStyle,s=this.valueStyle,o=this.wordSpace,h=t.marker,u=t.value,l=0;if(r&&(i.fill=r),h){var c=D({x:h.radius||3,y:this._titleHeight},h);!1===t.checked&&D(c,i),l+=n.addShape("marker",{className:"item-marker",attrs:c}).getBBox().width+o}var f=t.name;if(f){var g=this.joinString||"";f=u?f+g:f,e=n.addShape("text",{className:"name",attrs:D({x:l,y:this._titleHeight,text:this._formatItemValue(f)},a,!1===t.checked?i:null)})}if(u){var p=l;e&&(p+=e.getBBox().width),n.addShape("text",{className:"value",attrs:D({x:p,y:this._titleHeight,text:u},s,!1===t.checked?i:null)})}return n},t._formatItemValue=function(t){var e=this.itemFormatter;return e&&(t=e.call(this,t)),t},t._getMaxItemWidth=function(){var t=this.itemWidth;if(x(t)||s(t))return t;if("auto"===t){for(var e=this.itemsGroup.get("children"),n=e.length,i=0,r=0;r<n;r++){var a=e[r].getBBox().width;i=Math.max(i,a)}var o=this.maxLength,h=this.itemGap,u=(o-h)/2,l=(o-2*h)/3;return 2===n?Math.max(i,u):i<=l?l:i<=u?u:i}},t._adjustHorizontal=function(){for(var t,e,n=this.maxLength,i=this.itemsGroup.get("children"),r=this.itemGap,a=this.itemMarginBottom,s=this._titleHeight,o=0,h=0,u=this._getMaxItemWidth(),l=[],c=0,f=i.length;c<f;c++){var g=i[c],p=g.getBBox(),d=p.height,v=p.width;e=d+a,(t=u||v)-(n-h)>1e-4&&(o++,h=0),g.moveTo(h,o*e),l.push({x:h,y:o*e+s-d/2,width:1.375*v,height:1.375*d}),h+=t+r}this.legendHitBoxes=l},t._adjustVertical=function(){for(var t,e,n=this.maxLength,i=this.itemsGroup,r=this.itemGap,a=this.itemMarginBottom,s=this.itemWidth,o=this._titleHeight,h=i.get("children"),u=0,l=0,c=0,f=[],g=0,p=h.length;g<p;g++){var d=h[g],v=d.getBBox();t=v.width,e=v.height,x(s)?l=s+r:t>l&&(l=t+r),n-u<e?(u=0,c+=l,d.moveTo(c,0),f.push({x:c,y:o-e/2,width:1.375*t,height:1.375*e})):(d.moveTo(c,u),f.push({x:c,y:u-e/2+o,width:1.375*t,height:1.375*e})),u+=e+a}this.legendHitBoxes=f},t._adjustItems=function(){"horizontal"===this.layout?this._adjustHorizontal():this._adjustVertical()},t.moveTo=function(t,e){this.x=t,this.y=e;var n=this.container;return n&&n.moveTo(t,e),this},t.setItems=function(t){this.clearItems(),this._renderItems(t)},t.setTitle=function(t){this._renderTitle(t)},t.clearItems=function(){this.itemsGroup.clear()},t.getWidth=function(){return this.container.getBBox().width},t.getHeight=function(){return this.container.getBBox().height},t.show=function(){this.container.show()},t.hide=function(){this.container.hide()},t.clear=function(){var t=this.container;t.clear(),t.remove(!0)},e}(),Or=function(){var t=e.prototype;function e(t){Y(this,this.getDefaultCfg(),t),this._init();var e=this.content,n=this.x,i=this.y;s(e)||this.updateContent(e),this.updatePosition(n,i)}return t.getDefaultCfg=function(){return{x:0,y:0,content:"",textStyle:{fontSize:12,fill:"#fff",textAlign:"center",textBaseline:"middle",fontFamily:"Arial"},background:{radius:1,fill:"rgba(0, 0, 0, 0.65)",padding:[3,5]},width:0,height:0,className:""}},t._init=function(){var t=this.content,e=this.textStyle,n=this.background,i=this.className,r=this.visible,a=this.context,s=new Ii({context:a,className:i,zIndex:0,visible:r}),o=s.addShape("Text",{className:i+"-text",zIndex:1,attrs:D({text:t,x:0,y:0},e)}),h=s.addShape("Rect",{className:i+"-bg",zIndex:-1,attrs:D({x:0,y:0,width:0,height:0},n)});s.sort(),this.container=s,this.textShape=o,this.backgroundShape=h},t._getBBox=function(){var t=this.textShape,e=this.background,n=t.getBBox(),i=dt(e.padding),r=n.width+i[1]+i[3],a=n.height+i[0]+i[2];return{x:n.minX-i[3],y:n.minY-i[0],width:r,height:a}},t.updateContent=function(t){var e=this.textShape,n=this.backgroundShape;if(!s(t)){h(t)||(t={text:t}),e.attr(t);var i=this._getBBox(),r=i.x,a=i.y,o=i.width,u=i.height,l=this.width||o,c=this.height||u;n.attr({x:r,y:a,width:l,height:c}),this._width=l,this._height=c,this.content=t.text}},t.updatePosition=function(t,e){var n=this.container,i=this._getBBox(),r=i.x,a=i.y;n.moveTo(t-r,e-a),this.x=t-r,this.y=e-a},t.getWidth=function(){return this._width},t.getHeight=function(){return this._height},t.show=function(){this.container.show()},t.hide=function(){this.container.hide()},t.clear=function(){var t=this.container;t.clear(),t.remove(!0),this.container=null,this.textShape=null,this.backgroundShape=null},e}(),Er=function(){var t=e.prototype;function e(t){Y(this,this.getDefaultCfg(),t);var e=this.frontPlot;if(!this.custom){var n=new Yr(D({parent:e,zIndex:3},t));this.container=n;var i=this.fixed,r=this.background;i||(this.tooltipArrow=e.addShape("Polygon",{className:"tooltip-arrow",visible:!1,zIndex:2,attrs:D({points:[]},r)}))}if(this.showXTip){var a=this.xTipBackground,s=this.xTipTextStyle,o=new Or({context:e.get("context"),className:"xTip",background:a,textStyle:s,visible:!1});e.add(o.container),this.xTipBox=o}if(this.showYTip){var h=this.yTipBackground,u=this.yTipTextStyle,l=new Or({context:e.get("context"),className:"yTip",background:h,textStyle:u,visible:!1});e.add(l.container),this.yTipBox=l}this.showCrosshairs&&this._renderCrosshairs(),e.sort()}return t.getDefaultCfg=function(){return{showCrosshairs:!1,crosshairsStyle:{stroke:"rgba(0, 0, 0, 0.25)",lineWidth:1},crosshairsType:"y",showXTip:!1,showYTip:!1,xTip:null,xTipBackground:{radius:1,fill:"rgba(0, 0, 0, 0.65)",padding:[3,5]},xTipTextStyle:{fontSize:12,fill:"#fff",textAlign:"center",textBaseline:"middle"},yTip:null,yTipBackground:{radius:1,fill:"rgba(0, 0, 0, 0.65)",padding:[3,5]},yTipTextStyle:{fontSize:12,fill:"#fff",textAlign:"center",textBaseline:"middle"},background:null,layout:"horizontal",offsetX:0,offsetY:0}},t.setContent=function(t,e){if(this.title=t,this.items=e,!this.custom){var n=this.container;n.setTitle(t),n.setItems(e)}},t.setYTipContent=function(t){var e=this.yTip;t=a(e)?e(t):D({text:t},e),this.yTipBox&&this.yTipBox.updateContent(t)},t.setYTipPosition=function(t){var e=this.plotRange,n=this.crosshairsShapeX;if(this.showYTip){var i=this.yTipBox,r=i.getHeight(),a=i.getWidth(),s=e.tl.x-a,o=t-r/2;o<=e.tl.y&&(o=e.tl.y),o+r>=e.br.y&&(o=e.br.y-r),s<0&&(s=e.tl.x,n&&n.attr("x1",e.tl.x+a)),i.updatePosition(s,o)}},t.setXTipContent=function(t){var e=this.xTip;t=a(e)?e(t):D({text:t},e),this.xTipBox&&this.xTipBox.updateContent(t)},t.setXTipPosition=function(t){var e=this.showXTip,n=this.canvas,i=this.plotRange,r=this.xTipBox,a=this.crosshairsShapeY;if(e){var s=n.get("height"),o=r.getWidth(),h=r.getHeight(),u=t-o/2,l=i.br.y;u<=i.tl.x&&(u=i.tl.x),u+o>=i.tr.x&&(u=i.tr.x-o),s-l<h&&(l-=h),r.updatePosition(u,l),a&&a.attr("y1",l)}},t.setXCrosshairPosition=function(t){this.crosshairsShapeX&&this.crosshairsShapeX.moveTo(0,t)},t.setYCrosshairPosition=function(t){this.crosshairsShapeY&&this.crosshairsShapeY.moveTo(t,0)},t.setPosition=function(t){var e=this.container,n=this.plotRange,i=this.offsetX,r=this.offsetY,a=this.fixed,s=this.tooltipArrow;if(e){var o,h=e.container.getBBox(),u=h.minX,l=h.minY,c=h.width,f=h.height,g=n.tl,p=n.tr,d=0,v=g.y-f-4+r;if(v<0&&(v=0),a)d=(g.x+p.x)/2-c/2+i;else if((d=(o=t.length>1?(t[0].x+t[t.length-1].x)/2:t[0].x)-c/2+i)<g.x&&(d=g.x),d+c>p.x&&(d=p.x-c),s){var y=v+f;s.attr("points",[{x:o-3,y:y},{x:o+3,y:y},{x:o,y:y+4}]);var m=e.backShape,x=dt(m.attr("radius"));o===g.x?(x[3]=0,s.attr("points",[{x:g.x,y:y},{x:g.x+4,y:y},{x:g.x,y:y+4}])):o===p.x&&(x[2]=0,s.attr("points",[{x:p.x-4,y:y},{x:p.x,y:y},{x:p.x,y:y+4}])),m.attr("radius",x)}e.moveTo(d-u,v-l)}},t.setMarkers=function(t){void 0===t&&(t={});var e=t,n=e.items,i=e.style,r=e.type,a=this._getMarkerGroup(r);if("circle"===r)for(var s=0,o=n.length;s<o;s++){var h=n[s];a.addShape("marker",{className:"tooltip-circle-marker",attrs:D({x:h.x,y:h.y,stroke:h.color},i)})}else a.addShape("rect",{className:"tooltip-rect-marker",attrs:i})},t.clearMarkers=function(){var t=this.markerGroup;t&&t.clear()},t.show=function(){var t=this.crosshairsShapeX,e=this.crosshairsShapeY,n=this.markerGroup,i=this.container,r=this.tooltipArrow,a=this.xTipBox,s=this.yTipBox,o=this.canvas;t&&t.show(),e&&e.show(),n&&n.show(),i&&i.show(),r&&r.show(),a&&a.show(),s&&s.show(),o.draw()},t.hide=function(){var t=this.crosshairsShapeX,e=this.crosshairsShapeY,n=this.markerGroup,i=this.container,r=this.tooltipArrow,a=this.xTipBox,s=this.yTipBox;t&&t.hide(),e&&e.hide(),n&&n.hide(),i&&i.hide(),r&&r.hide(),a&&a.hide(),s&&s.hide()},t.destroy=function(){var t=this.crosshairsShapeX,e=this.crosshairsShapeY,n=this.markerGroup,i=this.container,r=this.tooltipArrow,a=this.xTipBox,s=this.yTipBox;t&&t.remove(!0),e&&e.remove(!0),n&&n.remove(!0),r&&r.remove(!0),i&&i.clear(),a&&a.clear(),s&&s.clear(),this.destroyed=!0},t._getMarkerGroup=function(t){var e=this.markerGroup;return e?e.clear():("circle"===t?(e=this.frontPlot.addGroup({zIndex:1}),this.frontPlot.sort()):e=this.backPlot.addGroup(),this.markerGroup=e),e},t._renderCrosshairs=function(){var t=this.crosshairsType,e=this.crosshairsStyle,n=this.frontPlot,i=this.plotRange,r=i.tl,a=i.br;vt(t,"x")&&(this.crosshairsShapeX=n.addShape("Line",{className:"tooltip-crosshairs-x",zIndex:0,visible:!1,attrs:D({x1:r.x,y1:0,x2:a.x,y2:0},e)})),vt(t,"y")&&(this.crosshairsShapeY=n.addShape("Line",{className:"tooltip-crosshairs-y",zIndex:0,visible:!1,attrs:D({x1:0,y1:a.y,x2:0,y2:r.y},e)}))},e}();function Nr(t){var e=t.getAttr("color");if(e){var n=e.getScale(e.type);if(n.isLinear)return n}var i=t.getXScale(),r=t.getYScale();return r||i}function Fr(t,e){var n,i,r=t._getGroupScales();if(r.length&&u(r,(function(t){return i=t,!1})),i){var a=i.field;n=i.getText(e[a])}else{var s=Nr(t);n=s.alias||s.field}return n}function zr(t,e){var n=Nr(t);return n.getText(e[n.field])}function Br(t,e){var n=t.getAttr("position").getFields()[0],i=t.get("scales")[n];return i.getText(e[i.field])}function Lr(t){var e=[];return u(t,(function(t){var n=function(t,e){var n=-1;return u(t,(function(t,i){if(t.title===e.title&&t.name===e.name&&t.value===e.value&&t.color===e.color)return n=i,!1})),n}(e,t);-1===n?e.push(t):e[n]=t})),e}Mt.tooltip=Y({triggerOn:"press",triggerOff:"pressend",alwaysShow:!1,showTitle:!1,showCrosshairs:!1,crosshairsStyle:{stroke:"rgba(0, 0, 0, 0.25)",lineWidth:1},showTooltipMarker:!0,background:{radius:1,fill:"rgba(0, 0, 0, 0.65)",padding:[3,5]},titleStyle:{fontSize:12,fill:"#fff",textAlign:"start",textBaseline:"top"},nameStyle:{fontSize:12,fill:"rgba(255, 255, 255, 0.65)",textAlign:"start",textBaseline:"middle"},valueStyle:{fontSize:12,fill:"#fff",textAlign:"start",textBaseline:"middle"},showItemMarker:!0,itemMarkerStyle:{radius:3,symbol:"circle",lineWidth:1,stroke:"#fff"},layout:"horizontal",snap:!1},Mt.tooltip||{});var Xr=function(){function t(t){var e=this;wt(this,"handleShowEvent",(function(t){var n=e.chart;if(e.enable){var i=n.get("plotRange"),r=gt(t,n);if(Li(r,i)||e._tooltipCfg.alwaysShow){var a=e.timeStamp,s=+new Date;s-a>16&&(e.showTooltip(r),e.timeStamp=s)}else e.hideTooltip()}})),wt(this,"handleHideEvent",(function(){e.enable&&e.hideTooltip()})),this.enable=!0,this.cfg={},this.tooltip=null,this.chart=null,this.timeStamp=0,D(this,t);var n=this.chart.get("canvas");this.canvas=n,this.canvasDom=n.get("el")}var e=t.prototype;return e._setCrosshairsCfg=function(){var t=this.chart,e=D({},Mt.tooltip),n=t.get("geoms"),i=[];u(n,(function(t){var e=t.get("type");-1===i.indexOf(e)&&i.push(e)}));var r=t.get("coord").type;return!n.length||"cartesian"!==r&&"rect"!==r||1===i.length&&-1!==["line","area","path","point"].indexOf(i[0])&&D(e,{showCrosshairs:!0}),e},e._getMaxLength=function(t){void 0===t&&(t={});var e=t,n=e.layout,i=e.plotRange;return"horizontal"===n?i.br.x-i.bl.x:i.bl.y-i.tr.y},e.render=function(){if(!this.tooltip){var t=this.chart,e=t.get("canvas"),n=t.get("frontPlot").addGroup({className:"tooltipContainer",zIndex:10}),i=t.get("backPlot").addGroup({className:"tooltipContainer"}),r=t.get("plotRange"),a=t.get("coord"),s=this._setCrosshairsCfg(),o=this.cfg,h=Y({plotRange:r,frontPlot:n,backPlot:i,canvas:e,fixed:a.transposed||a.isPolar},s,o);h.maxLength=this._getMaxLength(h),this._tooltipCfg=h;var u=new Er(h);this.tooltip=u,h.alwaysShow&&this.prePoint&&this.showTooltip(this.prePoint),this.bindEvents()}},e.clear=function(){var t=this.tooltip;t&&(t.destroy(),this.unBindEvents()),this.tooltip=null,this._lastActive=null},e._getTooltipMarkerStyle=function(t){void 0===t&&(t={});var e=t,n=e.type,i=e.items,r=this._tooltipCfg;if("rect"===n){var a,s,o,h,u=this.chart,l=u.get("plotRange"),c=l.tl,f=l.br,g=u.get("coord"),p=i[0],d=i[i.length-1],v=p.width;g.transposed?(a=c.x,s=d.y-.75*v,o=f.x-c.x,h=p.y-d.y+1.5*v):(a=p.x-.75*v,s=c.y,o=d.x-p.x+1.5*v,h=f.y-c.y),t.style=D({x:a,y:s,width:o,height:h,fill:"#CCD6EC",opacity:.3},r.tooltipMarkerStyle)}else t.style=D({radius:4,fill:"#fff",lineWidth:2},r.tooltipMarkerStyle);return t},e._setTooltip=function(t,e,n){void 0===n&&(n={}),this.prePoint=t;var i=this._lastActive,r=this.tooltip,a=this._tooltipCfg;e=Lr(e);var s=this.chart,o=s.get("coord"),h=s.getYScales()[0],u=a.snap;if(!1===u&&h.isLinear){var l,c,f=o.invertPoint(t);Li(t,s.get("plotRange"))&&(o.transposed?(l=h.invert(f.x),c=t.x,r.setXTipContent(l),r.setXTipPosition(c),r.setYCrosshairPosition(c)):(l=h.invert(f.y),c=t.y,r.setYTipContent(l),r.setYTipPosition(c),r.setXCrosshairPosition(c)))}if(a.onShow&&a.onShow({x:t.x,y:t.y,tooltip:r,items:e,tooltipMarkerCfg:n}),g=i,p=e,JSON.stringify(g)!==JSON.stringify(p)){var g,p;this._lastActive=e;var d=a.onChange;d&&d({x:t.x,y:t.y,tooltip:r,items:e,tooltipMarkerCfg:n});var v=e[0],y=v.title||v.name,m=v.x;if(e.length>1&&(m=(e[0].x+e[e.length-1].x)/2),r.setContent(y,e,o.transposed),r.setPosition(e,t),o.transposed){var x=v.y;e.length>1&&(x=(e[0].y+e[e.length-1].y)/2),r.setYTipContent(y),r.setYTipPosition(x),r.setXCrosshairPosition(x),u&&(r.setXTipContent(v.value),r.setXTipPosition(m),r.setYCrosshairPosition(m))}else r.setXTipContent(y),r.setXTipPosition(m),r.setYCrosshairPosition(m),u&&(r.setYTipContent(v.value),r.setYTipPosition(v.y),r.setXCrosshairPosition(v.y));var _=n.items;a.showTooltipMarker&&_.length?(n=this._getTooltipMarkerStyle(n),r.setMarkers(n)):r.clearMarkers(),r.show()}else{!1===u&&(vt(a.crosshairsType,"y")||a.showYTip)&&this.chart.get("canvas").draw()}},e.showTooltip=function(t){var e,n,i=this.chart,r=[],a=[],s=this._tooltipCfg,h=s.showItemMarker,l=s.itemMarkerStyle,c=s.alwaysShow;h&&(n=l);var f=i.get("geoms"),g=i.get("coord");if(u(f,(function(i){if(i.get("visible")){var s=i.get("type"),h=i.getSnapRecords(t),l=i.get("adjust");if("interval"===s&&l&&"symmetric"===l.type)return;u(h,(function(t){var h=t.x,u=t.y,l=t._origin,c=t.color;if((h||!isNaN(h))&&(u||!isNaN(u))){var f={x:h,y:o(u)?u[1]:u,color:c||Mt.defaultColor,origin:l,name:Fr(i,l),value:zr(i,l),title:Br(i,l)};n&&(f.marker=D({fill:c||Mt.defaultColor},n)),a.push(f),-1!==["line","area","path"].indexOf(s)?(e="circle",r.push(f)):"interval"!==s||"cartesian"!==g.type&&"rect"!==g.type||(e="rect",f.width=i.getSize(t._origin),r.push(f))}}))}})),a.length){var p={items:r,type:e};this._setTooltip(t,a,p)}else c||this.hideTooltip()},e.hideTooltip=function(){var t=this._tooltipCfg;this._lastActive=null;var e=this.tooltip;e&&(e.hide(),t.onHide&&t.onHide({tooltip:e}),this.chart.get("canvas").draw())},e._handleEvent=function(t,e,n){var i=this.canvas;u([].concat(t),(function(t){"bind"===n?i.on(t,e):i.off(t,e)}))},e.bindEvents=function(){var t=this._tooltipCfg,e=t.triggerOn,n=t.triggerOff,i=t.alwaysShow;e&&this._handleEvent(e,this.handleShowEvent,"bind"),i||this._handleEvent(n,this.handleHideEvent,"bind")},e.unBindEvents=function(){var t=this._tooltipCfg,e=t.triggerOn,n=t.triggerOff,i=t.alwaysShow;e&&this._handleEvent(e,this.handleShowEvent,"unBind"),i||this._handleEvent(n,this.handleHideEvent,"unBind")},t}();function jr(t){var e=new Xr({chart:t});t.set("tooltipController",e),t.tooltip=function(t,n){return h(t)&&(n=t,t=!0),e.enable=t,n&&(e.cfg=n),this}}function Gr(t){var e=t.get("tooltipController");e.render(),t.showTooltip=function(t){return e.showTooltip(t),this},t.hideTooltip=function(){return e.hideTooltip(),this}}function Rr(t){t.get("tooltipController").clear()}var Hr={init:jr,afterGeomDraw:Gr,clearInner:Rr},Wr=Object.freeze({__proto__:null,init:jr,afterGeomDraw:Gr,clearInner:Rr,default:Hr});Mt.guide=Y({line:{style:{stroke:"#a3a3a3",lineWidth:1},top:!0},text:{style:{fill:"#787878",textAlign:"center",textBaseline:"middle"},offsetX:0,offsetY:0,top:!0},rect:{style:{fill:"#fafafa"},top:!1},arc:{style:{stroke:"#a3a3a3"},top:!0},html:{offsetX:0,offsetY:0,alignX:"center",alignY:"middle"},tag:{top:!0,offsetX:0,offsetY:0,side:4,background:{padding:5,radius:2,fill:"#1890FF"},textStyle:{fontSize:12,fill:"#fff",textAlign:"center",textBaseline:"middle"}},point:{top:!0,offsetX:0,offsetY:0,style:{fill:"#fff",r:3,lineWidth:2,stroke:"#1890ff"}}},Mt.guide||{});var Vr=function(){function t(t){this.guides=[],this.xScale=null,this.yScales=null,this.guideShapes=[],D(this,t)}var e=t.prototype;return e._toString=function(t){return a(t)&&(t=t(this.xScale,this.yScales)),t=t.toString()},e._getId=function(t,e){var n=e.id;if(!n){var i=e.type;n="arc"===i||"line"===i||"rect"===i?this._toString(e.start)+"-"+this._toString(e.end):this._toString(e.position)}return n},e.paint=function(t){var e=this,n=e.chart,i=e.guides,r=e.xScale,a=e.yScales,s=[];u(i,(function(i,o){var h;i.xScale=r,i.yScales=a,"regionFilter"===i.type?i.chart=n:h=i.top?e.frontPlot:e.backPlot,i.coord=t,i.container=h,i.canvas=n.get("canvas");var u=i.render(t,h);if(u){var l=e._getId(u,i);[].concat(u).forEach((function(t){t._id=t.get("className")+"-"+l,t.set("index",o),s.push(t)}))}})),e.guideShapes=s},e.clear=function(){return this.reset(),this.guides=[],this},e.reset=function(){u(this.guides,(function(t){t.remove()}))},e._createGuide=function(t,e){var n=b(t),i=new Mr[n](Y({},Mt.guide[t],e));return this.guides.push(i),i},e.line=function(t){return void 0===t&&(t={}),this._createGuide("line",t)},e.text=function(t){return void 0===t&&(t={}),this._createGuide("text",t)},e.arc=function(t){return void 0===t&&(t={}),this._createGuide("arc",t)},e.html=function(t){return void 0===t&&(t={}),this._createGuide("html",t)},e.rect=function(t){return void 0===t&&(t={}),this._createGuide("rect",t)},e.tag=function(t){return void 0===t&&(t={}),this._createGuide("tag",t)},e.point=function(t){return void 0===t&&(t={}),this._createGuide("point",t)},e.regionFilter=function(t){return void 0===t&&(t={}),this._createGuide("regionFilter",t)},t}();function qr(t){var e=new Vr({frontPlot:t.get("frontPlot").addGroup({zIndex:20,className:"guideContainer"}),backPlot:t.get("backPlot").addGroup({className:"guideContainer"})});t.set("guideController",e),t.guide=function(){return e}}function Zr(t){var e=t.get("guideController");if(e.guides.length){var n=t.getXScale(),i=t.getYScales(),r=t.get("coord");e.xScale=n,e.yScales=i,e.chart=t,e.paint(r)}}function Ur(t){t.get("guideController").clear()}function Jr(t){t.get("guideController").reset()}var $r={init:qr,afterGeomDraw:Zr,clear:Ur,repaint:Jr},Kr=Object.freeze({__proto__:null,init:qr,afterGeomDraw:Zr,clear:Ur,repaint:Jr,default:$r}),Qr={itemMarginBottom:12,itemGap:10,showTitle:!1,titleStyle:{fontSize:12,fill:"#808080",textAlign:"start",textBaseline:"top"},nameStyle:{fill:"#808080",fontSize:12,textAlign:"start",textBaseline:"middle"},valueStyle:{fill:"#000000",fontSize:12,textAlign:"start",textBaseline:"middle"},unCheckStyle:{fill:"#bfbfbf"},itemWidth:"auto",wordSpace:6,selectedMode:"multiple"};Mt.legend=Y({common:Qr,right:D({position:"right",layout:"vertical"},Qr),left:D({position:"left",layout:"vertical"},Qr),top:D({position:"top",layout:"horizontal"},Qr),bottom:D({position:"bottom",layout:"horizontal"},Qr)},Mt.legend||{});var ta=function(){function t(t){var e=this;wt(this,"handleEvent",(function(t){var n=e;var i=n.chart,r=gt(t,i),a=function(t,e){var i=null;return u(n.legends,(function(n){u(n,(function(n){var r=n.itemsGroup,a=n.legendHitBoxes,s=r.get("children");if(s.length){var o=n.x,h=n.y;u(a,(function(r,a){if(t>=r.x+o&&t<=r.x+r.width+o&&e>=r.y+h&&e<=r.height+r.y+h)return i={clickedItem:s[a],clickedLegend:n},!1}))}}))})),i}(r.x,r.y);if(a&&!1!==a.clickedLegend.clickable){var s=a.clickedItem,o=a.clickedLegend;if(o.onClick)t.clickedItem=s,o.onClick(t);else if(!o.custom){var h=s.get("checked"),l=s.get("dataValue"),c=o.filteredVals,f=o.field;"single"===o.selectedMode?i.filter(f,(function(t){return t===l})):(h?c.push(l):Z(c,l),i.filter(f,(function(t){return-1===c.indexOf(t)}))),i.repaint()}}})),this.legendCfg={},this.enable=!0,this.position="top",D(this,t);var n=this.chart;this.canvasDom=n.get("canvas").get("el"),this.clear()}var e=t.prototype;return e.addLegend=function(t,e,n){var i=this.legendCfg,r=t.field,a=i[r];if(!1===a)return null;if(a&&a.custom)this.addCustomLegend(r);else{var s=i.position||this.position;a&&a.position&&(s=a.position),t.isCategory&&this._addCategoryLegend(t,e,s,n)}},e.addCustomLegend=function(t){var e=this.legendCfg;t&&e[t]&&(e=e[t]);var n=e.position||this.position,i=this.legends;i[n]=i[n]||[];var r=e.items;if(!r)return null;var a=this.container;u(r,(function(t){f(t.marker)?t.marker.radius=t.marker.radius||3:t.marker={symbol:t.marker||"circle",fill:t.fill,radius:3},t.checked=!!s(t.checked)||t.checked,t.name=t.name||t.value}));var o=new Yr(Y({},Mt.legend[n],e,{maxLength:this._getMaxLength(n),items:r,parent:a}));i[n].push(o)},e.clear=function(){u(this.legends,(function(t){u(t,(function(t){t.clear()}))})),this.legends={},this.unBindEvents()},e._isFiltered=function(t,e,n){var i=!1;return u(e,(function(e){if(i=i||t.getText(e)===t.getText(n))return!1})),i},e._getMaxLength=function(t){var e=this.chart,n=dt(e.get("appendPadding"));return"right"===t||"left"===t?e.get("height")-(n[0]+n[2]):e.get("width")-(n[1]+n[3])},e._addCategoryLegend=function(t,e,n,i){var r=this,a=r.legendCfg,s=r.legends,o=r.container,h=r.chart,l=t.field;s[n]=s[n]||[];var c="circle";a[l]&&a[l].marker?c=a[l].marker:a.marker&&(c=a.marker),u(e,(function(e){f(c)?D(e.marker,c):e.marker.symbol=c,i&&(e.checked=!r._isFiltered(t,i,e.dataValue))})),h.get("legendItems")[l]=e;var g=Y({},Mt.legend[n],a[l]||a,{maxLength:r._getMaxLength(n),items:e,field:l,filteredVals:i,parent:o});g.showTitle&&Y(g,{title:t.alias||t.field});var p=new Yr(g);return s[n].push(p),p},e._alignLegend=function(t,e,n){var i=this.plotRange,r=i.tl,a=i.bl,s=this.chart,o=t.offsetX||0,h=t.offsetY||0,u=s.get("width"),l=s.get("height"),c=dt(s.get("appendPadding")),f=t.getHeight(),g=t.getWidth(),p=0,d=0;if("left"===n||"right"===n){var v=t.verticalAlign||"middle",y=Math.abs(r.y-a.y);p="left"===n?c[3]:u-g-c[1],d=(y-f)/2+r.y,"top"===v?d=r.y:"bottom"===v&&(d=a.y-f),e&&(d=e.get("y")-f-12)}else{var m=t.align||"left";if(p=c[3],"center"===m?p=u/2-g/2:"right"===m&&(p=u-(g+c[1])),d="top"===n?c[0]+Math.abs(t.container.getBBox().minY):l-f,e){var x=e.getWidth();p=e.x+x+12}}"bottom"===n&&h>0&&(h=0),"right"===n&&o>0&&(o=0),t.moveTo(p+o,d+h)},e.alignLegends=function(){var t=this;return u(t.legends,(function(e,n){u(e,(function(i,r){var a=e[r-1];t._alignLegend(i,a,n)}))})),t},e.bindEvents=function(){var t=this.legendCfg.triggerOn||"touchstart";ut(this.canvasDom,t,this.handleEvent)},e.unBindEvents=function(){var t=this.legendCfg.triggerOn||"touchstart";lt(this.canvasDom,t,this.handleEvent)},t}();function ea(t){var e=new ta({container:t.get("backPlot").addGroup(),plotRange:t.get("plotRange"),chart:t});t.set("legendController",e),t.legend=function(t,n){var i=e.legendCfg;return e.enable=!0,P(t)?(e.enable=t,i=n||{}):h(t)?i=t:i[t]=n,e.legendCfg=i,this}}function na(t){var e=t.get("legendController");if(!e.enable)return null;var n=e.legendCfg,i=e.container;if(n&&n.custom)e.addCustomLegend();else{var r=t.getLegendItems(),a=t.get("scales"),s=t.get("filters");u(r,(function(t,n){var i,r=a[n],o=r.values;i=s&&s[n]?o.filter((function(t){return!s[n](t)})):[],e.addLegend(r,t,i)}))}n&&!1!==n.clickable&&e.bindEvents();var o=e.legends,h={top:0,right:0,bottom:0,left:0};u(o,(function(e,n){var i=0;u(e,(function(t){var e=t.getWidth(),r=t.getHeight();"top"===n||"bottom"===n?(i=Math.max(i,r),t.offsetY>0&&(i+=t.offsetY)):(i=Math.max(i,e),t.offsetX>0&&(i+=t.offsetX))})),h[n]=i+function(t,e){var n=0;switch(e=dt(e),t){case"top":n=e[0];break;case"right":n=e[1];break;case"bottom":n=e[2];break;case"left":n=e[3]}return n}(n,t.get("appendPadding"))})),t.set("legendRange",h),Object.keys(o).length?i.set("ariaLabel",St.legend.prefix):i.set("ariaLabel",null)}function ia(t){t.get("legendController").alignLegends()}function ra(t){t.get("legendController").clear(),t.set("legendRange",null)}var aa={init:ea,beforeGeomDraw:na,afterGeomDraw:ia,clearInner:ra},sa=Object.freeze({__proto__:null,init:ea,beforeGeomDraw:na,afterGeomDraw:ia,clearInner:ra,default:aa}),oa="object"==typeof performance&&performance.now?performance:Date,ha=function(){function t(){this.anims=[],this.time=null,this.playing=!1,this.canvas=[]}var e=t.prototype;return e.play=function(){var t=this;t.time=oa.now(),t.playing=!0,Yi((function e(){t.playing&&(Yi(e),t.update())}))},e.stop=function(){this.playing=!1,this.time=null,this.canvas=[]},e.pushAnim=function(t){this.playing||this.play();var e=t.delay,n=t.duration,i=this.time+e,r=i+n;t.startTime=i,t.endTime=r,this.anims.push(t)},e.update=function(){var t=oa.now();if(this.canvas=[],this.anims.length){for(var e=0;e<this.anims.length;e++){var n=this.anims[e];if(!(t<n.startTime||n.hasEnded)){var i=n.shape;if(i.get("destroyed"))this.anims.splice(e,1),e--;else{var r=n.startState,a=n.endState,s=n.interpolate,o=n.duration;t>=n.startTime&&!n.hasStarted&&(n.hasStarted=!0,n.onStart&&n.onStart());var h=(t-n.startTime)/o;if(h=Math.max(0,Math.min(h,1)),h=n.easing(h),n.onFrame)n.onFrame(h);else for(var u in s){var l=(0,s[u])(h),c=void 0;if("points"===u){c=[];for(var f=Math.max(r.points.length,a.points.length),g=0;g<f;g+=2)c.push({x:l[g],y:l[g+1]})}else c=l;i._attrs.attrs[u]=c,i._attrs.bbox=null}var p=i.get("canvas");-1===this.canvas.indexOf(p)&&this.canvas.push(p),n.onUpdate&&n.onUpdate(h),t>=n.endTime&&!n.hasEnded&&(n.hasEnded=!0,n.onEnd&&n.onEnd()),1===h&&(this.anims.splice(e,1),e--)}}}this.canvas.map((function(t){return t.draw(),t})),this.time=oa.now()}else this.stop()},t}();function ua(t){return t}function la(t){return 1-ca(1-t)}function ca(t){return(t/=1)<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375}var fa=Object.freeze({__proto__:null,linear:ua,quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4/(2*Math.PI)*Math.asin(1/n),-n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4/(2*Math.PI)*Math.asin(1/n),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=i/(2*Math.PI)*Math.asin(1/n),(t*=2)<1?n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*-.5:n*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return(t-=1)*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:la,bounceOut:ca,bounceInOut:function(t){return t<.5?.5*la(2*t):.5*ca(2*t-1)+.5}});function ga(t){for(var e=[],n=0,i=t.length;n<i;n++)t[n]&&(e.push(t[n].x),e.push(t[n].y));return e}function pa(t,e){return e-=t=+t,function(n){return t+e*n}}function da(t,e){var n,i=e?e.length:0,r=t?Math.min(i,t.length):0,a=new Array(r),s=new Array(i);for(n=0;n<r;++n)a[n]=pa(t[n],e[n]);for(;n<i;++n)s[n]=e[n];return function(t){for(n=0;n<r;++n)s[n]=a[n](t);return s}}var va=function(){function t(t,e,n){this.hasStarted=!1,this.hasEnded=!1,this.shape=t,this.source=e,this.timeline=n,this.animate=null}var e=t.prototype;return e.to=function(t){void 0===t&&(t={});var e,n=t.delay||0,i=t.attrs||{},r=t.duration||1e3;e="function"==typeof t.easing?t.easing:fa[t.easing]||ua;var a={shape:this.shape,delay:n,duration:r,easing:e},s={};for(var o in i){var h=this.source[o],u=i[o];"points"===o?(h=ga(h),u=ga(u),s.points=da(h,u),this.source.points=h,i.points=u):"matrix"===o?s.matrix=da(h,u):s[o]=pa(h,u)}return a.interpolate=s,a.startState=this.source,a.endState=i,this.timeline.pushAnim(a),this.animate=a,this},e.onFrame=function(t){return this.animate&&(this.animate.onFrame=function(e){t(e)}),this},e.onStart=function(t){return this.animate&&(this.animate.onStart=function(){t()}),this},e.onUpdate=function(t){return this.animate&&(this.animate.onUpdate=function(e){t(e)}),this},e.onEnd=function(t){return this.animate&&(this.animate.onEnd=function(){t()}),this},t}(),ya={appear:{duration:450,easing:"quadraticOut"},update:{duration:300,easing:"quadraticOut"},enter:{duration:300,easing:"quadraticOut"},leave:{duration:350,easing:"quadraticIn"}},ma={defaultCfg:{},Action:{},getAnimation:function(t,e,n){var i=this.defaultCfg[t];if(i){var r=i[n];if(a(r))return r(e)}return!1},getAnimateCfg:function(t,e){var n=ya[e],i=this.defaultCfg[t];return i&&i.cfg&&i.cfg[e]?Y({},n,i.cfg[e]):n},registerAnimation:function(t,e){var n;this.Action||(this.Action={}),this.Action=bt({},this.Action,((n={})[t]=e,n))}};function xa(t,e,n){var i;t.apply(e);var r=e[0],a=e[1];if("x"===n){t.transform([["t",r,a],["s",.01,1],["t",-r,-a]]);var s=t.getMatrix();i=It.transform(s,[["t",r,a],["s",100,1],["t",-r,-a]])}else if("y"===n){t.transform([["t",r,a],["s",1,.01],["t",-r,-a]]);var o=t.getMatrix();i=It.transform(o,[["t",r,a],["s",1,100],["t",-r,-a]])}else if("xy"===n){t.transform([["t",r,a],["s",.01,.01],["t",-r,-a]]);var h=t.getMatrix();i=It.transform(h,[["t",r,a],["s",100,100],["t",-r,-a]])}return i}function _a(t,e,n,i){var r=t._id,s=function(t,e,n){var i={};return t.delay&&(i.delay=a(t.delay)?t.delay(e,n):t.delay),i.easing=t.easing,i.duration=t.duration,i.delay=t.delay,i}(n,t.get("index"),r),o=s.easing,h=s.delay,u=s.duration,l=t.animate().to({attrs:e,duration:u,delay:h,easing:o});i&&l.onEnd((function(){i()}))}function Sa(t,e){var n=s(t.attr("fillOpacity"))?1:t.attr("fillOpacity"),i=s(t.attr("strokeOpacity"))?1:t.attr("strokeOpacity");t.attr("fillOpacity",0),t.attr("strokeOpacity",0),_a(t,{fillOpacity:n,strokeOpacity:i},e)}var Ma=Object.freeze({__proto__:null,fadeIn:Sa});function wa(t,e,n,i,r){var a,s,o=function(t){var e=t.start,n=t.end;return{start:e,end:n,width:n.x-e.x,height:Math.abs(n.y-e.y)}}(n),h=o.start,u=o.end,l=o.width,c=o.height,f=new ni.Rect({attrs:{x:h.x,y:u.y,width:l,height:c}});"y"===r?(a=h.x+l/2,s=i.y<h.y?i.y:h.y):"x"===r?(a=i.x>h.x?i.x:h.x,s=h.y+c/2):"xy"===r&&(n.isPolar?(a=n.center.x,s=n.center.y):(a=(h.x+u.x)/2,s=(h.y+u.y)/2));var g=xa(f,[a,s],r);f.isClip=!0,f.endState={matrix:g},f.set("canvas",t.get("canvas")),t.attr("clip",f);_a(f,f.endState,e,(function(){t.attr("clip",null),f.remove(!0)}))}function ba(t,e,n){for(var i=t.get("children"),r=0,a=i.length;r<a;r++){var s=i[r],o=s.getBBox();_a(s,{matrix:xa(s,[(o.minX+o.maxX)/2,(o.minY+o.maxY)/2],n)},e)}}function Ca(t,e,n,i){wa(t,e,n,i,"x")}function Pa(t,e,n,i){wa(t,e,n,i,"y")}function ka(t,e,n,i){wa(t,e,n,i,"xy")}function Ta(t,e){ba(t,e,"xy")}function Aa(t,e,n){var i=Bi(n);i.set("canvas",t.get("canvas")),t.attr("clip",i);var r={};if(n.isPolar){var a=n.startAngle,s=n.endAngle;r.endAngle=s,i.attr("endAngle",a)}else{var o=n.start,h=n.end,u=Math.abs(o.x-h.x),l=Math.abs(o.y-h.y);n.isTransposed?(i.attr("height",0),r.height=l):(i.attr("width",0),r.width=u)}_a(i,r,e,(function(){t.attr("clip",null),i.remove(!0)}))}var Da,Ia=Object.freeze({__proto__:null,groupWaveIn:Aa,groupScaleInX:Ca,groupScaleInY:Pa,groupScaleInXY:ka,shapesScaleInX:function(t,e){ba(t,e,"x")},shapesScaleInY:function(t,e){ba(t,e,"y")},shapesScaleInXY:Ta});ei.prototype.animate=function(){var t=D({},this.get("attrs"));return new va(this,t,Da)},Gi.prototype.animate=function(t){return this.set("animate",t),this},ma.Action=Ma,ma.defaultCfg={interval:{enter:function(t){return t.isPolar&&t.transposed?function(t){t.set("zIndex",-1),t.get("parent").sort()}:Sa}},area:{enter:function(t){return t.isPolar?null:Sa}},line:{enter:function(t){return t.isPolar?null:Sa}},path:{enter:function(t){return t.isPolar?null:Sa}}};var Ya={line:function(t){return t.isPolar?ka:Aa},area:function(t){return t.isPolar?ka:Aa},path:function(t){return t.isPolar?ka:Aa},point:function(){return Ta},interval:function(t){var e;return t.isPolar?(e=ka,t.transposed&&(e=Aa)):e=t.transposed?Ca:Pa,e},schema:function(){return Aa}};function Oa(t,e,n){var i=[];return u(t,(function(t,r){var a=t.get("container").get("children"),o=t.get("type"),h=s(t.get("animateCfg"))?za(o,e):t.get("animateCfg");!1!==h&&u(a,(function(e,a){e.get("className")===o&&(e._id=function(t,e,n){var i,r=t.get("type"),a="geom"+n+"-"+r,s=t.getXScale(),o=t.getYScale(),h=s.field||"x",l=o.field||"y",c=e[l];return i=s.isIdentity?s.value:e[h],a+="interval"===r||"schema"===r?"-"+i:"line"===r||"area"===r||"path"===r?"-"+r:s.isCategory?"-"+i:"-"+i+"-"+c,u(t._getGroupScales(),(function(t){var n=t.field;"identity"!==t.type&&(a+="-"+e[n])})),a}(t,e.get("origin")._origin,r),e.set("coord",n),e.set("animateCfg",h),e.set("index",a),i.push(e))})),t.set("shapes",a)})),i}function Ea(t,e,n,i){return a(i)?i:p(i)?ma.Action[i]:ma.getAnimation(t,e,n)}function Na(t,e,n){if(!1===n||h(n)&&!1===n[e])return!1;var i=ma.getAnimateCfg(t,e);return n&&n[e]?Y({},i,n[e]):i}function Fa(t,e,n){var i,r,s=[],h=[];u(e,(function(e){var n=t[e._id];n?(e.set("cacheShape",n),s.push(e),delete t[e._id]):h.push(e)})),u(t,(function(t){var e=t.className,s=t.coord,o=t._id,h=t.attrs,u=t.index,l=t.type;if(!1===(r=Na(e,"leave",t.animateCfg)))return!0;if(i=Ea(e,s,"leave",r.animation),a(i)){var c=n.addShape(l,{attrs:h,index:u,canvas:n,className:e});c._id=o,i(c,r,s)}})),u(s,(function(t){var e=t.get("className");if(!1===(r=Na(e,"update",t.get("animateCfg"))))return!0;var n=t.get("coord"),s=t.get("cacheShape").attrs,h=function(t,e){var n={};for(var i in e)(x(t[i])&&t[i]!==e[i]||o(t[i])&&JSON.stringify(t[i])!==JSON.stringify(e[i]))&&(n[i]=e[i]);return n}(s,t._attrs.attrs);if(Object.keys(h).length)if(i=Ea(e,n,"update",r.animation),a(i))i(t,r,n);else{var l={};u(h,(function(t,e){l[e]=s[e]})),t.attr(l),t.animate().to({attrs:h,duration:r.duration,easing:r.easing,delay:r.delay}).onEnd((function(){t.set("cacheShape",null)}))}})),u(h,(function(t){var e=t.get("className"),n=t.get("coord");if(!1===(r=Na(e,"enter",t.get("animateCfg"))))return!0;if(i=Ea(e,n,"enter",r.animation),a(i))if("interval"===e&&n.isPolar&&n.transposed){var o=t.get("index"),h=s[o-1];i(t,r,h)}else i(t,r,n)}))}function za(t,e){if(!t)return null;var n=e.get("animate");return t.indexOf("guide-tag")>-1&&(t="guide-tag"),h(n)?n[t]:!1!==n&&null}function Ba(){(Da=new ha).play()}function La(t){if(!1!==t.get("animate")){var e=t.get("isUpdate"),n=t.get("canvas"),i=t.get("coord"),r=t.get("geoms"),o=n.get("caches")||[];0===o.length&&(e=!1);var h=Oa(r,t,i),l=t.get("axisController"),c=l.frontPlot,f=l.backPlot,g=c.get("children").concat(f.get("children")),p=[];t.get("guideController")&&(p=t.get("guideController").guideShapes);var d,v,y=[];if(g.concat(p).forEach((function(e){var n=za(e.get("className"),t);e.set("coord",i),e.set("animateCfg",n),y.push(e),h.push(e)})),n.set("caches",function(t){for(var e={},n=0,i=t.length;n<i;n++){var r=t[n];if(r._id&&!r.isClip){var a=r._id;e[a]={_id:a,type:r.get("type"),attrs:D({},r._attrs.attrs),className:r.get("className"),geomType:r.get("className"),index:r.get("index"),coord:r.get("coord"),animateCfg:r.get("animateCfg")}}}return e}(h)),e)Fa(o,h,n);else u(r,(function(e){var n=e.get("type"),r=s(e.get("animateCfg"))?za(n,t):e.get("animateCfg");if(!1!==r)if(d=Na(n,"appear",r),v=Ea(n,i,"appear",d.animation),a(v))u(e.get("shapes"),(function(t){v(t,d,i)}));else if(Ya[n]){v=Ia[d.animation]||Ya[n](i);var o=e.getYScale(),h=i.convertPoint({x:0,y:o.scale(e.getYMinValue())}),l=e.get("container");v&&v(l,d,i,h)}})),u(y,(function(t){var e=t.get("animateCfg"),n=t.get("className");if(e&&e.appear){var r=ma.getAnimateCfg(n,"appear"),s=Y({},r,e.appear),o=Ea(n,i,"appear",s.animation);a(o)&&o(t,s,i)}}))}}function Xa(){Da.stop()}var ja={afterCanvasInit:Ba,beforeCanvasDraw:La,afterCanvasDestroyed:Xa},Ga=Object.freeze({__proto__:null,afterCanvasInit:Ba,beforeCanvasDraw:La,afterCanvasDestroyed:Xa,default:ja});Gi._Interactions={},Gi.registerInteraction=function(t,e){Gi._Interactions[t]=e},Gi.getInteraction=function(t){return Gi._Interactions[t]},Gi.prototype.interaction=function(t,e){var n=this._interactions||{};n[t]&&n[t].destroy();var i=new(Gi.getInteraction(t))(e,this);return n[t]=i,this._interactions=n,this},Gi.prototype.clearInteraction=function(t){var e=this._interactions;if(e)return t?(e[t]&&e[t].destroy(),delete e[t]):u(e,(function(t,n){t.destroy(),delete e[n]})),this};var Ra=[0,1],Ha=function(){function t(t){var e=this;wt(this,"chart",null),wt(this,"values",null),wt(this,"range",Ra),wt(this,"startRange",Ra),wt(this,"minCount",10),wt(this,"_afterinit",(function(){var t=e.getPinchScale(),n=[].concat(t.values);e.values=n,e.minScale||(e.minScale=e.minCount/n.length),e.range!==Ra&&(e.updateRange(e.range),e.updateTicks())})),wt(this,"_afterdatachange",(function(){e.updateRange(e.range)})),this.chart=t,this._initEvent(t)}var e=t.prototype;return e._initEvent=function(t){t.on("afterinit",this._afterinit),t.on("afterdatachange",this._afterdatachange)},e.getPinchScale=function(){return this.chart.getXScale()},e.getFollowScale=function(){return(this.chart.getYScales()||[])[0]},e.start=function(){var t=this.range,e=this.getPinchScale(),n=t[0],i=t[1];this.startRange=[n,i],this.lastTickCount=e.tickCount},e.doZoom=function(t,e,n){var i=this.startRange,r=this.minScale,a=i[0],s=i[1],o=(s-a)*(1-n),h=o*t,u=o*e,l=Math.max(0,a-h),c=Math.min(1,s+u),f=[l,c];c-l<r||this.updateRange(f)},e.doMove=function(t){if(t){var e,n=this.startRange,i=n[0],r=n[1],a=r-i,s=a*t,o=i-s,h=r-s;e=o<0?[0,a]:h>1?[1-a,1]:[o,h],this.updateRange(e)}},e.updateRange=function(t){var e=this.values,n=t[0],i=t[1];n=Math.max(0,n),i=Math.min(1,i),this.range=[n,i];var r=e.length,a=n*r,s=i*r,o=e.slice(a,s);this.repaint(o)},e.repaint=function(t){var e=this.chart,n=this.getPinchScale(),i=n.values,r=n.ticks;(function(t,e){if(t.length!==e.length)return!1;var n=t.length-1;return t[0]===e[0]&&t[n]===e[n]})(i,t)||(this.updateScale(n,{ticks:r,values:t}),this.updateFollowScale(n,t),e.repaint())},e.updateFollowScale=function(t,e){var n=this.chart,i=this.getFollowScale(),r=t.field,a=t.type,s=i.field,o=[],h={};e.forEach((function(t){h[t]=!0})),n.get("data").forEach((function(t){if("timeCat"===a){var e=yt(t[r]);h[e]&&o.push(t[s])}}));var u=U(o),l=u.min,c=u.max;this.updateScale(i,{min:l,max:c,nice:!0})},e.updateScale=function(t,e){t&&t.change(e)},e.updateTicks=function(){var t=this.chart,e=this.values,n=this.getPinchScale(),i=n.values,r=n.tickCount,a=Math.round(r*e.length/i.length),s=ae("cat")({tickCount:a,values:e});this.updateScale(n,{ticks:s,values:i}),t.repaint()},e.destroy=function(){var t=this.chart;t.off("afterinit",this._afterinit),t.off("afterdatachange",this._afterdatachange)},t}(),Wa=function(){var t=e.prototype;function e(t,e){var n=this;wt(this,"type",""),wt(this,"startEvent","touchstart"),wt(this,"processEvent","touchmove"),wt(this,"endEvent","touchend"),wt(this,"resetEvent",null),wt(this,"context",null),wt(this,"_start",(function(t){n.preStart&&n.preStart(t),n.start(t),n.onStart&&n.onStart(t)})),wt(this,"_process",(function(t){n.preProcess&&n.preProcess(t),n.process(t),n.onProcess&&n.onProcess(t)})),wt(this,"_end",(function(t){n.preEnd&&n.preEnd(t),n.end(t),n.onEnd&&n.onEnd(t)})),wt(this,"_reset",(function(t){n.preReset&&n.preReset(t),n.reset(t),n.onReset&&n.onReset(t)})),D(this,this.getDefaultCfg(),t),this.context=this.getInteractionContext(e),this.chart=e;var i=this.range;i&&(this.context.range=i),this._bindEvents(e)}return t.getDefaultCfg=function(){return{}},t.getInteractionContext=function(t){var e=t.get("interactionContext");return e||(e=new Ha(t),t.set("interactionContext",e),e)},t._bindEvents=function(t){var e=this.startEvent,n=this.processEvent,i=this.endEvent,r=this.resetEvent,a=t.get("canvas");a.on(e,this._start),a.on(n,this._process),a.on(i,this._end),a.on(r,this._reset)},t._clearEvents=function(){var t=this.chart,e=this.startEvent,n=this.processEvent,i=this.endEvent,r=this.resetEvent,a=t.get("canvas");a.off(e,this._start),a.off(n,this._process),a.off(i,this._end),a.off(r,this._start)},t.start=function(){},t.process=function(){},t.end=function(){},t.reset=function(){},t.destroy=function(){this.context.destroy(),this._clearEvents()},e}(),Va=function(t){function e(){return t.apply(this,arguments)||this}Ct(e,t);var n=e.prototype;return n.getDefaultCfg=function(){return{type:"pan",startEvent:"panstart",processEvent:"pan",endEvent:"panend"}},n.start=function(){this.context.start()},n.process=function(t){var e=t.direction,n=t.deltaX;if("up"!==e&&"down"!==e){t.preventDefault&&t.preventDefault();var i=this.context,r=i.chart.get("coord"),a=r.start,s=n/(r.end.x-a.x);i.doMove(s)}},e}(Wa),qa=function(t){Ct(n,t);var e=n.prototype;function n(e,n){var i;return D(kt(i=t.call(this,e,n)||this).context,e),i}return e.getDefaultCfg=function(){return{type:"pinch",startEvent:"pinchstart",processEvent:"pinch",endEvent:"pinchend"}},e.start=function(){this.context.start()},e.process=function(t){t.preventDefault&&t.preventDefault();var e=t.zoom,n=t.center,i=this.context,r=i.chart.get("coord"),a=r.start,s=r.end,o=s.x-a.x,h=Math.abs(n.x-a.x)/o,u=Math.abs(s.x-n.x)/o;i.doZoom(h,u,e)},e.end=function(){this.context.updateTicks()},n}(Wa);Gi.registerInteraction("pan",Va),Gi.registerInteraction("pinch",qa);var Za={Marker:ni.Marker,Tooltip:Er};Gi.plugins.register([Wr,sa,Kr,Ga]);var Ua={Component:Za,Global:Mt,Chart:Gi,Shape:Zt,G:zi,Util:mt,Helper:Xi,track:Ri,Animate:ma};t.Animate=ma,t.Chart=Gi,t.Component=Za,t.G=zi,t.Global=Mt,t.Helper=Xi,t.Shape=Zt,t.Util=mt,t.default=Ua,t.track=Ri,Object.defineProperty(t,"__esModule",{value:!0})}));
