

export default defineAppConfig({
  pages: ['pages/tabs/market',
    'pages/tabs/calc',
    'pages/tabs/fav',
    'pages/tabs/mine',
    'pages/mine/feedback-edit',
    'pages/mine/feedback-list',
    'pages/mine/playground',
    'pages/mine/vip-purchase',
    'pages/mine/vip-order-list',
    'pages/mine/vip-order-detail',
    'pages/mine/points-list',
    'pages/mine/settings',
    'pages/mine/message-list',
  ],
  tabBar: {
    custom: true,
    list: [
      {
        pagePath: 'pages/tabs/market',
        text: '行情',
      },
      {
        pagePath: 'pages/tabs/calc',
        text: '计算器',
      },
      {
        pagePath: 'pages/tabs/fav',
        text: '收藏',
      },
      {
        pagePath: 'pages/tabs/mine',
        text: '我的',
      },
    ],
  },
  darkmode: true, // 所有基础组件均会根据系统主题展示不同的默认样式，navigation bar 和 tab bar 也会根据下面的配置自动切换
  themeLocation: 'theme.json',
  window: {
  //   // navigationBarBackgroundColor: '#f5f8f7',
  //   // navigationBarTextStyle: 'black'
     backgroundColor: '@bgColor', // 窗口的背景色
     backgroundTextStyle: '@bgTextStyle', // 下拉 loading 的样式，仅支持 dark / light，默认 dark
     navigationStyle: 'custom', // 全局导航栏样式，仅支持以下值：default 默认样式；custom 自定义导航栏，只保留右上角胶囊按钮
     navigationBarBackgroundColor: '@navBgColor', // 导航栏背景颜色，默认 #000000
     navigationBarTitleText: '鲜切花报价', // 导航栏标题文字内容
     navigationBarTextStyle: '@navTextStyle' // 导航栏标题颜色，仅支持 black | white
  }
})
