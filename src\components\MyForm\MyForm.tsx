import { createEffect, JSX } from 'solid-js'
import { View } from '@tarojs/components'
import { FormContext, useMyForm } from './useMyForm'
import { MyFormItem } from './MyFormItem'
import { MyFormInput } from './MyFormInput'
import { MyFormSelect } from './MyFormSelect'
import { MyFormTextArea } from './MyFormTextArea'
import { MyFormImageUpload } from './MyFormImageUpload'

export interface TMyFormProps {
  form: ReturnType<typeof useMyForm>
  children?: JSX.Element
  onSubmit?: (values: Record<string, any>) => void
  onValuesChange?: (values: Record<string, any>) => void
}

export function MyForm(props: TMyFormProps) {
  createEffect(() => {
    if (props.onSubmit) {
      props.form._setOnSubmit(props.onSubmit)
    }
  })

  return (
    <FormContext.Provider value={props.form}>
      <View class="MyForm">
        {props.children}
      </View>
    </FormContext.Provider>
  )
}

MyForm.Item = MyFormItem
MyForm.Input = MyFormInput
MyForm.Select = MyFormSelect
MyForm.TextArea = MyFormTextArea
MyForm.ImageUpload = MyFormImageUpload