import { Utils } from "@/utils/utils"
import { View, Text } from "@tarojs/components"
import { Iconfont, TIconfontProps } from "@/components/Iconfont"
import clsx from "clsx"
import Taro from "@tarojs/taro"
import { $isDarkTheme } from "@/store/appStore"
import { useStore } from "@nanostores/solid"

interface TTabItem {
  title: string
  icon: string
  selectedIcon: string
  path: string
}

export const Tabbar = () => {
  const safeBottom = Utils.safeBottom
  const height = 50 + safeBottom
  const isDarkTheme = useStore($isDarkTheme)

  const tabs: TTabItem[] = [
    {title: '返回', icon: 'arrowleft', selectedIcon: 'arrowleft', path: 'back'},
    {title: '行情', icon: 'funds', selectedIcon: 'funds-fill', path: '/pages/tabs/market'},
    {title: '计算器', icon: 'calculator', selectedIcon: 'calculator-fill', path: '/pages/tabs/calc'},
    {title: '收藏', icon: 'star', selectedIcon: 'star-fill', path: '/pages/tabs/fav'},
    {title: '我的', icon: 'mine', selectedIcon: 'mine-fill', path: '/pages/tabs/mine'},
  ]

  const currentPath = Utils.getCurrentPath()

  const onClick = (tab: TTabItem) => {
    if(tab.path === 'back') {
      Utils.back()
      return
    }
    Utils.routeTo(tab.path)
  }

  return (
    <View class="Tabbar">
      <View class="w-ful" style={{ height: `${height}px` }}></View>
      
      <View class="bg-white dark:bg-stone-900 fixed bottom-0 left-0 right-0" style={{ height: `${height}px`, 'box-shadow': '0 -2px 10px rgba(128, 128, 128, 0.1)' }}>
        <View class="h-50px flex">
          {tabs.map((tab) => (
            <View class={clsx('flex-1 flex flex-col items-center justify-end', { 'text-#22c55e': tab.path === currentPath })} onClick={() => onClick(tab)}>
              <Iconfont class="text-48rpx" name={(tab.path === currentPath ? tab.selectedIcon : tab.icon) as TIconfontProps['name']} color={tab.path === currentPath ? '#22c55e' : (isDarkTheme() ? '#fff' : '#333')} />
              <Text class="text-20rpx">{tab.title}</Text>
            </View>
          ))}
        </View>
      </View>
    </View>
  )
}