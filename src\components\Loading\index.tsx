import { View } from '@tarojs/components';
import { Component } from 'solid-js';

interface LoadingProps {
  size?: number;
  color?: string;
  className?: string;
}

export const Loading: Component<LoadingProps> = (props) => {
  const size = props.size || 24;
  const color = props.color || '#3b82f6'; // blue-500
  
  return (
    <View 
      class={`inline-block animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite] ${props.className || ''}`}
      style={{
        width: `${size}px`,
        height: `${size}px`,
        'border-color': color,
        'border-right-color': 'transparent',
      }}
      role="status"
    >
      <span class="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
        加载中...
      </span>
    </View>
  );
};

export default Loading;
