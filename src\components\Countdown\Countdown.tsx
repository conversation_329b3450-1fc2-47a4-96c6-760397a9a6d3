import dayjs from "dayjs"
import { createEffect, createSignal, mergeProps } from "solid-js"

export interface TCountdownProps {
  endTime: string
  onEnd?: () => void
}

export const Countdown = (_props: TCountdownProps) => {
  const props = mergeProps({
    onEnd: () => { }
  }, _props)

  const [cdText, setCdText] = createSignal('')

  createEffect(() => {
    let interval: any
    interval = setInterval(() => {
      const endTime = dayjs(props.endTime)
      const now = dayjs()
      const diff = endTime.diff(now)
      if (diff <= 0) {
        setCdText('已结束')
        props.onEnd?.()
        clearInterval(interval)
        return
      }
      const cd = dayjs(diff).format('HH:mm:ss')
      setCdText(cd)
    }, 1000)
    return () => clearInterval(interval)
  })

  return cdText()
}