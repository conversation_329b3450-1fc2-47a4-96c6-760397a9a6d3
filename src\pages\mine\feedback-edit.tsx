import { BasePage } from '@/components/BasePage'
import { Iconfont } from '@/components/Iconfont'
import { MyForm, useMyForm } from '@/components/MyForm'
import { MyNavbar } from '@/components/MyNavbar'
import { View } from '@tarojs/components'
import { apiFeedbackSubmit } from '@/apis/apis'
import Taro from '@tarojs/taro'
import { createSignal } from 'solid-js'
import { Utils } from '@/utils/utils'
import { MyButton } from '@/components/MyButton'

export default function FeedbackPage() {
  const form = useMyForm()
  const [loading, setLoading] = createSignal(false)
  const onSubmit = async (values: {
    content: string;
    attachments: string[];
    mobile: string;
  }) => {
    if (loading()) return
    try {
      setLoading(true)
      await apiFeedbackSubmit(values)
      setLoading(false)
      Utils.toast('提交成功')
      setTimeout(() => {
        Taro.navigateBack()
      }, 1000)
    } catch (error) {
      console.error(error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <BasePage >
      <MyNavbar title="反馈建议" />
      <View class="p-4 box-border flex flex-col gap-4">
        <View class="bg-red-100 text-red-500 p-2 rounded text-xs flex items-center gap-2">
          <Iconfont name="warning-circle" size={24} />
          此反馈建议为本小程序自有渠道，非微信官方渠道
        </View>
        <MyForm form={form} onSubmit={onSubmit}>
          <MyForm.Item label="内容" name="content" requiredSgin rules={[{ required: true }]}>
            <MyForm.TextArea bordered class="bg-white dark:bg-stone-800" placeholder="请描述具体问题或建议" />
          </MyForm.Item>
          <MyForm.Item label="图片" name="attachments">
            <MyForm.ImageUpload />
          </MyForm.Item>
          <MyForm.Item label="手机号" name="mobile" requiredSgin rules={[{ required: true, pattern: /^1[3-9]\d{9}$/ }]}>
            <MyForm.Input bordered class="bg-white dark:bg-stone-800" placeholder="请输入手机号" />
          </MyForm.Item>
          <View>
            <MyButton type="primary" block onClick={form.submit} loading={form.submitting()}>提交</MyButton>
          </View>
        </MyForm>

      </View>
    </BasePage>
  )
}
