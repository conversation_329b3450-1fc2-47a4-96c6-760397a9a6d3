import { createMemo, mergeProps, Show } from "solid-js"
import { Text } from "@tarojs/components"
import './Iconfont.scss'

export interface TIconfontProps {
  class?: string;
  style?: any;
  name: 'feedback' | 'order' | 'wallet' | 'bell' | 'top' | 'share' | 'doubledown' | 'vip' | 'empty' | 'loading' | 'plus' | 'clear' | 'edit-square' | 'setting' | 'mine-fill' | 'mine' | 'check' | 'close' | 'sort-up' | 'sort' | 'sort-down' | 'swap' | 'search' | 'question-circle' | 'warning-circle' | 'star' | 'fall' | 'rise' | 'star-fill' | 'calculator' | 'right' | 'arrowleft' | 'calculator-fill' | 'funds' | 'funds-fill' | '';
  color?: string | string[] | Record<number, string>
  size?: number
}

export const Iconfont = (_props: TIconfontProps) => {
  const props = mergeProps({}, _props)


  const style = createMemo(() => {
    const res: any = props.style || {}
    if(props.color) {
      res.color = props.color
    }
    if(props.size) {
      res['font-size'] = props.size + 'rpx'
    }
    return res
  })

  return (
    <Show when={props.name} >
      <Text class={`iconfont icon-${props.name} ${props.class || ''}`} style={style()} />
    </Show>
  )
}

