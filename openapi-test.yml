openapi: 3.0.1
info:
  title: '<`jimmer.client.openapi.properties.info.title` is unspecified>'
  description: '<`jimmer.client.openapi.properties.info.description` is unspecified>'
  version: '<`jimmer.client.openapi.properties.info.version` is unspecified>'
tags:
  - name: ClientFlowerMarketApi
    description: 鲜花价格
paths:
  /flower/{id}:
    get:
      summary: 获取鲜花详情和趋势
      tags:
        - ClientFlowerMarketApi
      operationId: getFlowerMarket
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Dynamic_Flower'
components:
  schemas:
    Dynamic_Flower:
      type: object
      description: 鲜花
      properties:
        id:
          type: string
        name:
          description: 鲜花名称
          type: string
        parentId:
          nullable: true
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        deletedAt:
          nullable: true
          type: string
        parent:
          nullable: true
          $ref: '#/components/schemas/Dynamic_Flower'
        flowerMarkets:
          type: array
          items:
            $ref: '#/components/schemas/Dynamic_FlowerMarket'
        newestFlowerMarket:
          nullable: true
          $ref: '#/components/schemas/Dynamic_FlowerMarket'
    Dynamic_FlowerMarket:
      type: object
      properties:
        id:
          type: string
        flowerId:
          type: string
        temp:
          type: string
        lastPrice:
          nullable: true
          type: number
          format: double
        lastWeekPrice:
          nullable: true
          type: number
          format: double
        lastMonthPrice:
          nullable: true
          type: number
          format: double
        relativeIndex:
          type: number
          format: double
        createdAt:
          type: string
        updatedAt:
          type: string
        deletedAt:
          nullable: true
          type: string
        flower:
          $ref: '#/components/schemas/Dynamic_Flower'
