import { apiGetFavorites } from '@/apis/apis'
import { BasePage } from '@/components/BasePage'
import { MyNavbar } from '@/components/MyNavbar'
import { MyTable, TMyTableRef } from '@/components/MyTable/MyTable'
import { Tabbar } from '@/components/Tabbar'
import { Utils } from '@/utils/utils'
import { View } from '@tarojs/components'
import Taro, { useDidShow, usePullDownRefresh, useReachBottom } from '@tarojs/taro'
import { useFlowerTableColumns } from '@/hooks/useFlowerTableColumns'
import { $isDarkTheme } from '@/store/appStore'
import { useStore } from '@nanostores/solid'

export default function FavoritePage() {

  let tableRef!: TMyTableRef
  const pageSize = 20

  const isDarkTheme = useStore($isDarkTheme)

  const flowerTableColumns = useFlowerTableColumns({
    openFlower: flowerId => Utils.routeTo(`/pages/tabs/market?flowerId=${flowerId}`),
  })


  usePullDownRefresh(() => {
    tableRef?.reload()
    Taro.stopPullDownRefresh()
  })

  useReachBottom(() => {
    tableRef?.loadMore()
  })

  useDidShow(() => {
    tableRef?.reload()
  })

  useDidShow(() => {
    Utils.backPush()
  })

  return (
    <BasePage>
      <MyNavbar background={isDarkTheme() ? '#1C1917' : '#fff'} title="我的收藏" />
      <View class="bg-white dark:bg-stone-900 box-border" style={{
        'min-height': `${Utils.pageSize.tabPageHeight}px`
      }}>
        <MyTable
          bordered
          ref={tableRef}
          columns={flowerTableColumns}
          pageSize={pageSize}
          request={async (params: { pageNum: number }, sorter: { field: string, order: 'asc' | 'desc' }) => {
            const res = await apiGetFavorites({
              pageIndex: params.pageNum - 1,
              pageSize: pageSize,
              orderBy: sorter ? [sorter.field, sorter.order].join(',') : undefined,
            })
            return {
              list: res.rows,
              total: res.totalRowCount
            }
          }}
        />
      </View>
      <Tabbar />
    </BasePage>
  )
}
