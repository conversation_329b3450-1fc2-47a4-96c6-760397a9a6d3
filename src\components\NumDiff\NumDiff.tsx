import { View } from "@tarojs/components"
import { createMemo, mergeProps } from "solid-js"
import { Iconfont } from "../Iconfont";
import { useStore } from "@nanostores/solid";
import { $isDarkTheme } from "@/store/appStore";

export interface TNumDiffProps {
  oldValue?: number;
  newValue?: number;
  showIcon?: boolean;
  diffType?: 'percent'
}

export const NumDiff = (_props: TNumDiffProps) => {
  const props = mergeProps({
    showIcon: true,
    diffType: 'percent'
  }, _props)

  const isDarkTheme = useStore($isDarkTheme)

  const data = createMemo(() => {
    if (!props.oldValue || !props.newValue || (props.oldValue === props.newValue)) {
      return {
        value: '--',
        icon: '',
        color: isDarkTheme() ? '' : ''
      };
    }

    const diff = (props.newValue - props.oldValue) / props.oldValue * 100;
    return {
      value: diff > 0 ? `+${diff.toFixed(1)}%` : `${diff.toFixed(1)}%`,
      icon: diff > 0 ? 'rise' : 'fall',
      color: diff > 0 ? (isDarkTheme() ? '#ff0000' : '#FF4D4D') : (isDarkTheme() ? '#00ff00' : '#00CC55'),
    }
  })


  return (
    <View class="flex-inline gap-1 items-center" style={{ color: data().color }}>
      <View >{data().value}</View>
      <Iconfont name={data().icon as any} />
    </View>
  )
}