```tsx
{/* 单色图标自动跟随父级颜色 */}
<Iconfont name='close' />
{/* 单色图标设置大小或颜色 */}
<Iconfont name='close' color='green' size={64} />
{/* 多色图标如果不设置大小会自动跟随父级字体大小颜色，但颜色不变 */}
<Iconfont name='qianbao'></Iconfont>
{/* 多色图标设置大小 */}
<Iconfont name='qianbao' size={64}></Iconfont>
{/* 多色图标如果设置为单色 */}
<Iconfont name='qianbao' color='green'></Iconfont>
{/* 多色图标如果颜色是数组，按顺序覆盖path的颜色 */}
<Iconfont name='qianbao' color={['red', 'green', 'blue']}></Iconfont>
{/* 多色图标如果颜色是对象，按对象索引覆盖path的颜色 */}
<Iconfont name='qianbao' color={{ 8: 'red' }}></Iconfont>
```