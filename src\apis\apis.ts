import { HttpUtils } from "@/utils/http";

/**
 * 更新用户设置信息
 * @returns TClientUserSettingsResDto 对象
 */
export function apiUpdateSettings(data: TClientUserSettingsReqDto) {
  return HttpUtils.request<TClientUserSettingsResDto>({
    url: `/auth/settings`,
    method: 'POST',
    data,
  })
}

/**
 * @returns string 对象
 */
export function apiShareQrcode() {
  return HttpUtils.request<string>({
    url: `/auth/shareQrcode`,
  })
}

/**
 * 微信小程序登录
 * @returns TClientLoginResDto 对象
 */
export function apiWxLogin(data: TWxLoginReqDto) {
  return HttpUtils.request<TClientLoginResDto>({
    url: `/auth/wx-login`,
    method: 'POST',
    data,
  })
}

/**
 * 手动关闭指定账单并退回积分
 * @returns boolean 对象
 */
export function apiCloseBill(billId: string, query?: { reason: string }) {
  return HttpUtils.request<boolean>({
    url: `/bill/close/${billId}`,
    method: 'POST',
    query,
  })
}

/**
 * 添加收藏
 * @returns boolean 对象
 */
export function apiAddFavorite(flowerId: string) {
  return HttpUtils.request<boolean>({
    url: `/favorites/add/${flowerId}`,
    method: 'POST',
  })
}

/**
 * 检查是否已收藏
 * @returns string[] 对象
 */
export function apiCheckFavorite(data: TFavoriteCheckReqDto) {
  return HttpUtils.request<string[]>({
    url: `/favorites/check`,
    method: 'POST',
    data,
  })
}

/**
 * 获取收藏ID列表
 * @returns string[] 对象
 */
export function apiFavoriteFlowerIds() {
  return HttpUtils.request<string[]>({
    url: `/favorites/flowerIds`,
  })
}

/**
 * 获取收藏列表
 * @returns TFlowerPage 对象
 */
export function apiGetFavorites(query?: { pageIndex: number; pageSize: number; orderBy?: string }) {
  return HttpUtils.request<TFlowerPage>({
    url: `/favorites/pageList`,
    query,
  })
}

/**
 * 取消收藏
 * @returns boolean 对象
 */
export function apiRemoveFavorite(flowerId: string) {
  return HttpUtils.request<boolean>({
    url: `/favorites/remove/${flowerId}`,
    method: 'POST',
  })
}

/**
 * 获取反馈详情
 * @param id 反馈ID
 * @returns TFeedback 对象
 */
export function apiFeedbackMyDetail(id: string) {
  return HttpUtils.request<TFeedback>({
    url: `/feedback/myDetail/${id}`,
  })
}

/**
 * 获取我的反馈列表
 * @returns TFeedbackPage 对象
 */
export function apiFeedbackMyPageList(data: TMyFeedbackListReqDto) {
  return HttpUtils.request<TFeedbackPage>({
    url: `/feedback/myPageList`,
    method: 'POST',
    data,
  })
}

/**
 * 提交反馈
 * @returns TFeedback 对象
 */
export function apiFeedbackSubmit(data: TFeedbackSubmitReqDto) {
  return HttpUtils.request<TFeedback>({
    url: `/feedback/submit`,
    method: 'POST',
    data,
  })
}

/**
 * 上传文件
 * @returns string 对象
 */
export function apiUpload() {
  return HttpUtils.request<string>({
    url: `/file/upload`,
    method: 'POST',
  })
}

/**
 * 搜索鲜花
 * @returns TFlowerPage 对象
 */
export function apiFlowerSearch(query?: { keyword: string; pageIndex?: number; pageSize?: number; onlyFlower?: boolean }) {
  return HttpUtils.request<TFlowerPage>({
    url: `/flower/search`,
    query,
  })
}

/**
 * 获取鲜花图表数据
 * @returns TFlowerChartDto[] 对象
 */
export function apiFlowerMarketChartData(flowerId: string, days: number) {
  return HttpUtils.request<TFlowerChartDto[]>({
    url: `/flower/${flowerId}/chartData/${days}`,
  })
}

/**
 * 获取最新的鲜花价格
 * @returns TFlowerPrice[] 对象
 */
export function apiFlowerPrices(flowerId: string) {
  return HttpUtils.request<TFlowerPrice[]>({
    url: `/flower/${flowerId}/prices`,
  })
}

/**
 * 获取鲜花详情和趋势
 * @returns TFlower 对象
 */
export function apiFlowerMarketGet(id: string) {
  return HttpUtils.request<TFlower>({
    url: `/flower/${id}`,
  })
}

/**
 * 获取下级鲜花分页及相关信息
 * @param parentId 父鲜花ID
 * @param pageIndex 页码，从1开始
 * @param pageSize 每页数量
 * @returns TFlowerPage 对象
 */
export function apiFlowerMarketChildrenPage(parentId: string, query?: { pageIndex?: number; pageSize?: number; orderBy?: string }) {
  return HttpUtils.request<TFlowerPage>({
    url: `/flower/${parentId}/childrenPage`,
    query,
  })
}

/**
 * 获取消息详情
 * @returns TClientMessage 对象
 */
export function apiMessageGetById(query?: { id: string }) {
  return HttpUtils.request<TClientMessage>({
    url: `/message/getById`,
    query,
  })
}

/**
 * 标记所有消息为已读
 * @returns number 对象
 */
export function apiMessageMarkAllRead() {
  return HttpUtils.request<number>({
    url: `/message/markAllRead`,
    method: 'POST',
  })
}

/**
 * 标记消息为已读
 * @returns number 对象
 */
export function apiMessageMarkRead(data: TClientMessageMarkReadReqDto) {
  return HttpUtils.request<number>({
    url: `/message/markRead`,
    method: 'POST',
    data,
  })
}

/**
 * 获取我的消息分页列表
 * @returns TClientMessagePage 对象
 */
export function apiMessagePageList(data: TClientMessagePageListReqDto) {
  return HttpUtils.request<TClientMessagePage>({
    url: `/message/pageList`,
    method: 'POST',
    data,
  })
}

/**
 * 获取未读消息数量
 * @returns number 对象
 */
export function apiMessageUnreadCount() {
  return HttpUtils.request<number>({
    url: `/message/unreadCount`,
  })
}

/**
 * @returns TWxPayParamsDto 对象
 */
export function apiMakePay() {
  return HttpUtils.request<TWxPayParamsDto>({
    url: `/playground/makePay`,
    method: 'POST',
  })
}

/**
 * 获取我的点数变化记录分页列表
 * @returns TClientUserPointsRecordPage 对象
 */
export function apiPointsRecordMyPageList(query?: { pageIndex: number; pageSize: number; recordType?: string; createdAtStart?: string; createdAtEnd?: string }) {
  return HttpUtils.request<TClientUserPointsRecordPage>({
    url: `/pointsRecord/myPageList`,
    query,
  })
}

/**
 * 关闭VIP费用订单
 * @returns boolean 对象
 */
export function apiVipFeeOrderClose(id: string) {
  return HttpUtils.request<boolean>({
    url: `/vipFeeOrder/close/${id}`,
  })
}

/**
 * 获取我的VIP费用订单详情
 * @returns TVipFeeOrder 对象
 */
export function apiVipFeeOrderMyGetById(id: string) {
  return HttpUtils.request<TVipFeeOrder>({
    url: `/vipFeeOrder/myGetById/${id}`,
  })
}

/**
 * 获取我的VIP费用订单分页列表
 * @returns TVipFeeOrderPage 对象
 */
export function apiVipFeeOrderMyPageList(query?: { pageIndex: number; pageSize: number }) {
  return HttpUtils.request<TVipFeeOrderPage>({
    url: `/vipFeeOrder/myPageList`,
    query,
  })
}

/**
 * 对待支付的VIP费用订单进行支付
 * @returns TWxPayParamsDto 对象
 */
export function apiVipFeeOrderPay(data: TBaseIdReqDto) {
  return HttpUtils.request<TWxPayParamsDto>({
    url: `/vipFeeOrder/pay`,
    method: 'POST',
    data,
  })
}

/**
 * 获取对我可见的VIP费用类型分页列表
 * @returns TVipFeeType[] 对象
 */
export function apiVipFeeTypeMyList() {
  return HttpUtils.request<TVipFeeType[]>({
    url: `/vipFeeType/myList`,
  })
}

/**
 * 创建VIP费用订单并生成支付参数
 * @returns TWxPayParamsDto 对象
 */
export function apiVipFeeTypePay(data: TBaseIdReqDto) {
  return HttpUtils.request<TWxPayParamsDto>({
    url: `/vipFeeType/pay`,
    method: 'POST',
    data,
  })
}

