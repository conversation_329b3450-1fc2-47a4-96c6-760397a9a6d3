import { apiFlowerMarketChildrenPage, apiFlowerMarketGet } from '@/apis/apis';
import { BasePage } from '@/components/BasePage';
import { FlowerSelect } from '@/components/FlowerSelect';
import { Iconfont } from '@/components/Iconfont';
import { MarketBreadcrumb } from '@/components/MarketComps/MarketBreadcrumb';
import { MarketPageChart } from '@/components/MarketComps/MarketPageChart';
import { MyButton } from '@/components/MyButton';
import { MyNavbar } from '@/components/MyNavbar';
import { MyPopup } from '@/components/MyPopup';
import { MyTable, TMyTableRef } from '@/components/MyTable';
import { NumDiff } from '@/components/NumDiff';
import { Tabbar } from '@/components/Tabbar';
import { useFlowerTableColumns } from '@/hooks/useFlowerTableColumns';
import { $currentBackQuery, $favoriteFlowerIdsSet, $isDarkTheme, $userInfo, toggleFavoriteFlower } from '@/store/appStore';
import { Utils } from '@/utils/utils';
import { useStore } from '@nanostores/solid';
import { View, Text } from '@tarojs/components';
import Taro, { useDidShow, usePullDownRefresh, useReachBottom, useShareAppMessage, usePageScroll } from '@tarojs/taro';
import { createEffect, createMemo, createSignal, onMount, Show } from 'solid-js';

const SHOW_CHART = !Utils.cacheGet<boolean>('hideChart')

export default function MarketPage() {

  const { flowerId: baseFlowerId = Utils.cacheGet('defaultFlowerId') || 'root' } = Utils.getQuery<{
    flowerId: string
  }>()

  const currentBackQuery = useStore($currentBackQuery)

  const userInfo = useStore($userInfo)

  const isDarkTheme = useStore($isDarkTheme)
  const [flowerId, setFlowerId] = createSignal(baseFlowerId)
  const [flower, setFlower] = createSignal<TFlower>()

  const favoriteFlowerIdsSet = useStore($favoriteFlowerIdsSet)

  const [hideHasMoreTips, setHideHasMoreTips] = createSignal(Utils.cacheGet<boolean>('hideHasMoreTips') || false)

  const [tempModalVisible, setTempModalVisible] = createSignal(false)
  const [relativeModalVisible, setRelativeModalVisible] = createSignal(false)
  const [shareModalVisible, setShareModalVisible] = createSignal(false)

  const isRoot = createMemo(() => flowerId() === 'root')
  const isCategory = createMemo(() => flower()?.parent?.id === 'root')
  const isFlower = createMemo(() => !isRoot() && !isCategory())
  const temp = createMemo(() => Utils.getTemp(flower()?.latestFlowerMarket?.day7DiffPercent))

  const showHasMoreTips = createMemo(() => SHOW_CHART && !isFlower() && !hideHasMoreTips())


  let childrenTableRef!: TMyTableRef

  const loadData = async () => {
    const res = await apiFlowerMarketGet(flowerId())
    setFlower(res)
  }

  const openFlower = (flowerId: string) => {
    console.log('openFlower', flowerId)
    setFlowerId(flowerId)
    loadData()
    childrenTableRef?.reload()
    Utils.backPush({ query: { flowerId } })
  }

  const flowerTableColumns = useFlowerTableColumns({
    openFlower,
  })


  const know = () => {
    setHideHasMoreTips(true)
    Utils.cacheSet('hideHasMoreTips', true)
  }

  const setDefaultFlowerId = () => {
    Utils.cacheSet('defaultFlowerId', flowerId())
    Utils.toast('设置成功')
  }

  onMount(loadData)

  usePullDownRefresh(() => {
    loadData()
    childrenTableRef?.reload()
    Taro.stopPullDownRefresh()
  })

  useReachBottom(() => {
    childrenTableRef?.loadMore()
  })

  createEffect(() => {
    if (currentBackQuery()?.path === '/pages/tabs/market') {
      const query = currentBackQuery()?.query
      const flowerId = query?.flowerId || 'root'
      $currentBackQuery.set(undefined)
      openFlower(flowerId)
    }
  })

  useShareAppMessage(() => {
    return {
      title: `花卉行情-${flower()?.name}`,
      path: `/pages/tabs/market?flowerId=${flowerId()}&fromUserId=${userInfo()?.id}`,
    }
  })

  useDidShow(() => {
    Utils.backPush()
  })

  usePageScroll(() => {
    if (showHasMoreTips()) {
      Taro.createSelectorQuery()
        .select('#tableTop')
        .boundingClientRect((rect) => {
          if (rect && !Array.isArray(rect) && rect.bottom !== undefined) {
            // 获取系统信息来计算距离底部的距离
            Taro.getSystemInfo({
              success: (systemInfo) => {
                const distanceToBottom = systemInfo.windowHeight - rect.bottom
                if (distanceToBottom > 200 && !hideHasMoreTips()) {
                  setHideHasMoreTips(true)
                }
              }
            })
          }
        })
        .exec()
    }
  })

  return (
    <BasePage>
      <MyNavbar background={isDarkTheme() ? '#1C1917' : '#fff'}>
        <FlowerSelect onChange={(value) => openFlower(value.id)} value={flower()} renderSelect={(value) => (
          <Show when={value}>
            <View class="flex-inline items-center gap-2 h-full">
              <Text class="text-lg font-bold">{value.name}</Text>
              <Iconfont name="swap" size={36} class="text-green-500" />
            </View>
          </Show>
        )} />

      </MyNavbar>
      <Show when={flower()?.parent}>
        <View class='h-90rpx bg-white dark:bg-stone-900 fixed w-full z-999 px-4'>
          <MarketBreadcrumb flower={flower()!} onOpenFlower={openFlower} />
        </View>
        <View class="h-90rpx"></View>
      </Show>
      <View class="flex flex-col gap-4 my-4">
        <Show when={flower()?.latestFlowerMarket}>
          <View class="bg-white dark:bg-stone-900">
            <View class="flex justify-between">
              <View class="p-4 flex flex-col gap-1">
                <View class="text-32rpx font-bold">行情趋势</View>
                <View class="text-24rpx op-50">更新时间 {Utils.formatDayTime(flower()!.latestFlowerMarket!.batchTime)}</View>
              </View>
              <View class='flex items-center gap-4 pr-4'>
                <View class="flex items-center flex-col gap-1" onClick={() => setDefaultFlowerId()}>
                  <Iconfont name="top" size={48} color={'#22c55e'} />
                  <Text class='text-20rpx op-50'>设首页</Text>
                </View>
                <View class="flex items-center flex-col gap-1" onClick={() => setShareModalVisible(true)}>
                  <Iconfont name="share" size={48} color={'#22c55e'} />
                  <Text class='text-20rpx op-50'>赚佣金</Text>
                </View>
                <View class="flex items-center flex-col gap-1" onClick={() => toggleFavoriteFlower(flowerId())}>
                  <Iconfont name={favoriteFlowerIdsSet().has(flowerId()) ? "star-fill" : "star"} size={48} color={'#22c55e'} />
                  <Text class='text-20rpx op-50'>收藏</Text>
                </View>
              </View>
            </View>


            <View class="grid grid-cols-2 gap-3 mb-2 px-4">
              <Show when={isRoot()}>
                <View class="h-150rpx  flex flex-col gap-1 text-24rpx">
                  <View class="text-28rpx mb-2">最新指数</View>
                  <View class="flex items-baseline gap-1">
                    <View class="text-38rpx font-bold">{flower()!.latestFlowerMarket!.price!}</View>
                    <NumDiff oldValue={flower()!.latestFlowerMarket?.day1Price} newValue={flower()!.latestFlowerMarket!.price} />
                  </View>
                </View>
              </Show>
              <Show when={!isRoot()}>
                <View class="h-150rpx  flex flex-col gap-1 text-24rpx">
                  <View class="text-28rpx mb-2">最新价格</View>
                  <View class="flex items-baseline gap-1">
                    <View class="text-38rpx font-bold">{flower()!.latestFlowerMarket!.price!.toFixed(2)}</View>
                    <View class="text-20rpx op-70">元/扎</View>
                  </View>
                  <NumDiff oldValue={flower()!.latestFlowerMarket?.day1Price} newValue={flower()!.latestFlowerMarket!.price} />
                </View>
              </Show>

              <View class="h-150rpx flex flex-col gap-1 text-24rpx">
                <View class="text-28rpx mb-2">近期走势</View>
                <View class="flex items-center">
                  <View class="op-70 mr-auto">近一周变动</View>
                  <NumDiff oldValue={flower()!.latestFlowerMarket?.day7Price} newValue={flower()!.latestFlowerMarket!.price} />
                </View>
                <View class="flex items-center">
                  <View class="op-70 mr-auto">近一月变动</View>
                  <NumDiff oldValue={flower()!.latestFlowerMarket?.day30Price} newValue={flower()!.latestFlowerMarket!.price} />
                </View>
              </View>
              <View class="h-150rpx flex flex-col gap-1 text-24rpx">
                <View class="text-28rpx mb-1 flex items-center gap-1" onClick={() => setTempModalVisible(true)}>
                  <View class='underline'>趋势温度</View>
                  <View><Iconfont name="question-circle" /></View>
                </View>
                <View class="flex items-center gap-2">
                  <View class="font-bold text-60rpx" style={{ 'color': temp().color }}>{temp().text}</View>
                  <View class="flex flex-col gap-1 op-70">
                    <View>{
                      {
                        '冻': '左侧',
                        '寒': '左侧',
                        '凉': '准左侧',
                        '平': '中间',
                        '温': '准右侧',
                        '热': '右侧',
                        '沸': '右侧',
                      }[temp().text] || '--'
                    }</View>
                    <View>{
                      {
                        '冻': '趋势下行',
                        '寒': '趋势下行',
                        '凉': '趋势下行',
                        '平': '趋势平稳',
                        '温': '趋势上行',
                        '热': '趋势上行',
                        '沸': '趋势上行',
                      }[temp().text] || '--'
                    }</View>
                  </View>
                </View>
              </View>
              <Show when={!isRoot()}>
                <View class="h-150rpx flex flex-col gap-1 text-24rpx">
                  <View class="text-28rpx mb-1 flex items-center gap-1" onClick={() => setRelativeModalVisible(true)}>
                    <View class='underline'>趋势相对强度</View>
                    <View><Iconfont name="question-circle" /></View>
                  </View>
                  <View class="flex items-center gap-2">
                    <View class="font-bold text-60rpx text-amber">{flower()!.latestFlowerMarket!.strength}</View>
                    <View class="flex flex-col gap-1 op-70">
                      <View>近期跑赢</View>
                      <View>{flower()!.latestFlowerMarket!.strength}%的品种</View>
                    </View>
                  </View>
                </View>
              </Show>

            </View>
          </View>
        </Show>
        <Show when={SHOW_CHART}>
          <MarketPageChart flowerId={flowerId()} batchTime={Utils.formatDayTime(flower()?.latestFlowerMarket?.batchTime || '')} />
        </Show>
        <Show when={showHasMoreTips()}>
          <View class="fixed left-0 right-0 z-999 dark:bg-stone-900 flex flex-col gap-4 items-center justify-end h-200px p-4" style={{
            bottom: (Utils.pageSize.tabbarHeight + Utils.pageSize.safeBottom) + 'px',
            background: 'linear-gradient(to top,#888888 0%, #888888aa 50%, #88888800 100%)',
          }}>
            <Iconfont name="doubledown" size={81} class="-mb-4 animate-bounce text-white" />
            <View class='text-white'>下方还有内容哦</View>
            <MyButton size='small' onClick={know}>知道了</MyButton>
          </View>
        </Show>


        <Show when={!isFlower()}>
          <View class="bg-white dark:bg-stone-900">
            <View class="p-4 flex flex-col gap-1">
              <View class="text-32rpx font-bold">包含品种</View>
              <Show when={flower()?.latestFlowerMarket}>
                <View class="text-24rpx op-50">更新时间 {Utils.formatDayTime(flower()!.latestFlowerMarket!.batchTime)}</View>
              </Show>
            </View>
            <View class="box-border">
              <View id="tableTop"></View>
              <MyTable
                bordered
                ref={childrenTableRef}
                columns={flowerTableColumns}
                pageSize={20}
                request={async (params: { pageNum: number }, sorter: { field: string, order: 'asc' | 'desc' }) => {
                  const res = await apiFlowerMarketChildrenPage(flowerId(), {
                    pageIndex: params.pageNum - 1,
                    pageSize: 20,
                    orderBy: sorter ? [sorter.field, sorter.order].join(',') : undefined,
                  })
                  return {
                    list: res.rows,
                    total: res.totalRowCount
                  }
                }}
              />
            </View>
          </View>
        </Show>

        <MyPopup open={tempModalVisible()} onOpenChange={(v) => setTempModalVisible(v)} title="趋势温度说明">
          <View class='p-4 line-height-6'>
            <View><Text class="font-bold mr-2">沸</Text><Text class="op-70">趋势正快速拉升、波动放大。</Text></View>
            <View><Text class="font-bold mr-2">热</Text><Text class="op-70">趋势处于上涨行情右侧确认。</Text></View>
            <View><Text class="font-bold mr-2">温</Text><Text class="op-70">趋势盘整，有即将启动上涨的迹象。</Text></View>
            <View><Text class="font-bold mr-2">平</Text><Text class="op-70">趋势横盘</Text></View>
            <View><Text class="font-bold mr-2">凉</Text><Text class="op-70">趋势盘整，有即将启动下跌的迹象。</Text></View>
            <View><Text class="font-bold mr-2">寒</Text><Text class="op-70">趋势处于下跌行情，左侧确认。</Text></View>
            <View><Text class="font-bold mr-2">冻</Text><Text class="op-70">趋势正快速下跌、波动放大。</Text></View>
            <View><Text class="op-70">当趋势温度变为热，趋势进入右侧</Text></View>
            <View><Text class="op-70">当趋势温度变为寒，趋势进入左侧</Text></View>
          </View>
        </MyPopup>
        <MyPopup open={relativeModalVisible()} onOpenChange={(v) => setRelativeModalVisible(v)} title="趋势相对强度说明">
          <View class='p-4 line-height-6'>
            <View><Text class="op-70">趋势相对强度，是该品种近期趋势在同类所有品种内涨幅排名的位次值。</Text></View>
            <View><Text class="font-bold mr-2">90~100分</Text><Text class="op-70">趋势排名领头、相对极强势</Text></View>
            <View><Text class="font-bold mr-2">70~90分</Text><Text class="op-70">趋势排名靠前、相对强势</Text></View>
            <View><Text class="font-bold mr-2">50~70分</Text><Text class="op-70">趋势排名中上、相对偏强</Text></View>
            <View><Text class="font-bold mr-2">30~50分</Text><Text class="op-70">趋势排名中下、相对偏弱</Text></View>
            <View><Text class="font-bold mr-2">10~30分</Text><Text class="op-70">趋势排名靠后、相对弱势</Text></View>
            <View><Text class="font-bold mr-2">O~10分</Text><Text class="op-70">趋势排名倒数、相对极弱势</Text></View>
          </View>
        </MyPopup>

        <MyPopup open={shareModalVisible()} onOpenChange={(v) => setShareModalVisible(v)} title="分佣说明">
          <View class='p-4 line-height-6'>
            <View><Text class="font-bold">分享邀请 → 完成支付 → 积分入账</Text></View>
            <View><Text class="op-70">分享邀请：无需申请，对所有用户开放</Text></View>
            <View><Text class="op-70">完成支付：被邀请用户首次登录并购买会员</Text></View>
            <View><Text class="op-70">积分入账：购买金额(以分为单位)的20%视为佣金自动入账</Text></View>
            <View><Text class="op-70">积分使用：可用于支付抵扣</Text></View>
            <View class='flex items-center gap-2 mt-4'>
              <View class='flex-1'>
                <MyButton class='w-full' size='small' onClick={() => {
                  setShareModalVisible(false)
                  Taro.showShareMenu({
                    showShareItems: ['shareAppMessage', 'shareTimeline']
                  })
                }}>分享微信</MyButton>
              </View>
            </View>
          </View>
        </MyPopup>
      </View>
      <Tabbar />
    </BasePage>
  )
}