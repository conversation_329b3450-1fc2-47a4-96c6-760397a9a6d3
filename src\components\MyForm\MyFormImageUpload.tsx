import { Image, View, Text } from "@tarojs/components";
import { FormItemContext } from "./useMyForm";
import { createEffect, createSignal, useContext } from "solid-js";
import clsx from "clsx";
import { Iconfont } from "../Iconfont";
import { HttpUtils } from "@/utils/http";

type FileStatus = 'normal' | 'uploading' | 'success' | 'error';

interface FileItem {
  url: string;
  status: FileStatus;
  message?: string;
  progress?: number;
}

export interface TMyFormImageUploadProps {
  class?: any;
  maxCount?: number;
  sourceType?: ('album' | 'camera')[];
  mediaType?: ('image' | 'video')[];
}

export function MyFormImageUpload(props: TMyFormImageUploadProps) {
  const formItem = useContext(FormItemContext);
  const [files, setFiles] = createSignal<FileItem[]>(
    (formItem.value() || []).map((url: string) => ({
      url,
      status: 'success' as FileStatus
    }))
  );

  createEffect(() => {
    if ((formItem.value() || []).length > 0 && (files() || []).length === 0) {
      setFiles(formItem.value().map((url: string) => ({
        url,
        status: 'success' as FileStatus
      })))
    }
  })

  const maxCount = props.maxCount || 9;
  const remainingCount = () => maxCount - (files()?.length || 0);

  const handleUpload = () => {
    if (remainingCount() <= 0) return;

    HttpUtils.uploadMedia({
      sourceType: props.sourceType || ['album', 'camera'],
      mediaType: props.mediaType || ['image'],
      onChange: (data) => {
        if (data.status === 'uploading') {
          // 更新上传进度
          setFiles(prev => {
            const newFiles = [...prev];
            const uploadingFile = newFiles[newFiles.length - 1];
            if (uploadingFile && uploadingFile.status === 'uploading') {
              uploadingFile.progress = data.progress;
            }
            return newFiles;
          });
        } else if (data.status === 'success') {
          // 上传成功
          setFiles(prev => {
            const newFiles = [...prev];
            const uploadingFile = newFiles[newFiles.length - 1];
            if (uploadingFile && uploadingFile.status === 'uploading') {
              uploadingFile.status = 'success';
              uploadingFile.url = data.url || '';
              uploadingFile.progress = 100;
            }
            return newFiles;
          });
          updateFormValue();
        } else if (data.status === 'fail') {
          // 上传失败
          setFiles(prev => {
            const newFiles = [...prev];
            const failedFile = newFiles[newFiles.length - 1];
            if (failedFile && failedFile.status === 'uploading') {
              failedFile.status = 'error';
              failedFile.message = data.message || '上传失败';
            }
            return newFiles;
          });
        }
      }
    }).then(() => {
      // 添加上传中的文件
      setFiles(prev => [...prev, { url: '', status: 'uploading' }]);
    }).catch(err => {
      console.error('上传失败:', err);
    });
  };

  const handleRemove = (index: number) => {
    setFiles(prev => {
      const newFiles = [...prev];
      newFiles.splice(index, 1);
      return newFiles;
    });
    updateFormValue();
  };

  const updateFormValue = () => {
    const urls = files()
      .filter(file => file.status === 'success' && file.url)
      .map(file => file.url);
    formItem.onChange?.(urls);
  };



  return (
    <View>
      <View class={clsx('flex flex-wrap gap-2', props.class)}>
        {files()?.map((file, index) => (
          <View class="w-24 h-24 rounded relative bg-white dark:bg-stone-800 flex items-center justify-center">
            {file.status === 'uploading' ? (
              <View class="w-full h-full flex flex-col items-center justify-center">
                <View class="w-16 h-1 bg-gray-200 rounded-full overflow-hidden">
                  <View
                    class="h-full bg-blue-500 transition-all duration-300"
                    style={{ width: `${file.progress || 0}%` }}
                  />
                </View>
                <Text class="text-xs mt-1 text-gray-500">上传中 {file.progress}%</Text>
              </View>
            ) : file.status === 'error' ? (
              <View class="w-full h-full flex flex-col items-center justify-center p-2">
                <Iconfont name="question-circle" size={24} color="#f5222d" />
                <Text class="text-xs text-red-500 text-center mt-1">{file.message}</Text>
                <View
                  class="absolute top-0 right-0 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemove(index);
                  }}
                >
                  <Iconfont name="close" size={24} color="white" />
                </View>
              </View>
            ) : (
              <>
                <Image src={file.url} class="w-full h-full rounded" mode="aspectFill" />
                <View
                  class="absolute -top-2 -right-2 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemove(index);
                  }}
                >
                  <Iconfont name="close" size={24} color="white" />
                </View>
              </>
            )}
          </View>
        ))}

        {remainingCount() > 0 && (
          <View
            class="bg-white dark:bg-stone-800 border border-solid border-stone-200 dark:border-stone-700 w-24 h-24 rounded flex items-center justify-center"
            onClick={handleUpload}
          >
            <Iconfont name="plus" size={64} color="#999" />
          </View>
        )}
      </View>
    </View>

  );
}