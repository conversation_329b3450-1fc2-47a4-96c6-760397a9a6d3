import { View, Text } from "@tarojs/components";
import { createSignal, onMount, For, Show } from "solid-js";
import { BasePage } from "@/components/BasePage";
import { MyNavbar } from "@/components/MyNavbar";
import { Utils } from "@/utils/utils";
import { apiVipFeeTypeMyList, apiVipFeeTypePay } from "@/apis/apis";
import { MyButton } from "@/components/MyButton";
import { login } from "@/store/appStore";
import Empty from "@/components/Empty";

export default function VipPurchase() {
  const [vipTypes, setVipTypes] = createSignal<TVipFeeType[]>([]);
  const [loading, setLoading] = createSignal(false);
  const [payingId, setPayingId] = createSignal<string>('');

  // 加载VIP类型列表
  const loadVipTypes = async () => {
    try {
      setLoading(true);
      const data = await apiVipFeeTypeMyList();
      setVipTypes(data);
    } catch (error) {
      Utils.toast('加载失败');
    } finally {
      setLoading(false);
    }
  };

  // 购买VIP
  const handlePurchase = async (vipType: TVipFeeType) => {
    if (payingId()) return;

    try {
      setPayingId(vipType.id);

      // 获取支付参数
      const payParams = await apiVipFeeTypePay({ id: vipType.id });

      // 调用支付并监听状态
      const success = await Utils.payAndListenStatus(payParams);

      if (success) {
        Utils.toast('支付成功');
        // 可以跳转到订单列表或其他页面
        setTimeout(() => {
          Utils.routeTo('/pages/mine/vip-order-list');
        }, 1500);
        login()
      }
    } catch (error: any) {
      Utils.toast(error.message || '购买失败');
    } finally {
      setPayingId('');
    }
  };

  // 格式化价格
  const formatPrice = (amountCent: number) => {
    return (amountCent / 100).toFixed(2);
  };

  onMount(() => {
    loadVipTypes();
  });

  return (
    <BasePage>
      <MyNavbar title="开通会员" />
      <View class="p-4 min-h-screen">
        <Show when={!loading()} fallback={
          <View class="flex justify-center items-center h-400px">
            <Text class="text-gray-500">加载中...</Text>
          </View>
        }>
          <Show when={vipTypes().length > 0} fallback={
            <Empty description="暂无可购买的会员套餐" />
          }>
            <View class="grid grid-cols-3 gap-2">
              <For each={vipTypes()}>
                {(vipType) => (
                  <View class="bg-white dark:bg-stone-900 rounded-2 py-4 px-2 box-border shadow flex flex-col items-center gap-2">
                    <View class="text-32rpx font-bold text-gray-800 dark:text-white mb-2">{vipType.name}</View>
                    {/* <View class="text-28rpx text-gray-600 dark:text-gray-300 mb-2">{vipType.days}天</View> */}
                    <View class="flex items-baseline text-green-400">
                      <Text class="text-24rpx mr-1">¥</Text>
                      <Text class="text-56rpx font-bold">{formatPrice(vipType.amountCent)}</Text>
                    </View>
                    <View class="text-24rpx text-gray-600 dark:text-gray-300">{(vipType.amountCent / 100 /vipType.days).toFixed(2)}元/天</View>
                    <MyButton
                      class="mt-2"
                      type="primary"
                      variant="outline"
                      size="small"
                      loading={payingId() === vipType.id}
                      disabled={!!payingId()}
                      onClick={() => handlePurchase(vipType)}
                      loadingText="支付中"
                    >
                      立即购买
                    </MyButton>
                  </View>
                )}
              </For>
            </View>
          </Show>
        </Show>
      </View>
    </BasePage>
  );
}
