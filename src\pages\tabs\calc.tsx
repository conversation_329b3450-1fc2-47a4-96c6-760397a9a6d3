import { BasePage } from '@/components/BasePage'
import { MyNavbar } from '@/components/MyNavbar'
import { Tabbar } from '@/components/Tabbar'
import { View, Input, Text, ScrollView, Image, Switch } from '@tarojs/components'
import { createSignal, createEffect, For, Show } from 'solid-js'
import { MyPopup } from '@/components/MyPopup'
import { apiFlowerSearch, apiFlowerPrices } from '@/apis/apis'
import { Iconfont } from '@/components/Iconfont'
import { MyTable } from '@/components/MyTable'
import { $isDarkTheme, $wxShareQrCode } from '@/store/appStore'
import { useStore } from '@nanostores/solid'
import { Utils } from '@/utils/utils'
import { useDebounce } from '@/hooks/useDebounce'
import Taro, { useDidShow } from '@tarojs/taro'
import qrCodeSrc from '@/assets/qrcode.jpg'
import dayjs from 'dayjs'

// 供货单项目接口（本地使用）
interface StockItem {
  id: string
  category: string // 品类
  variety: string // 品种
  varietyArr?: string[] // 品种数组
  grade: string // 等级
  price: number // 价格
  quantity: number // 数量
  actualPrice: number // 实际价格（可修改）
}

const systemRateOptions = [0.05, 0.1, 0.15]
const defaultCacheRate = Utils.cacheGet<number>('calc:rate') || systemRateOptions[0]
const defaultRateOptions = Array.from(new Set([...systemRateOptions, defaultCacheRate])).sort((a, b) => a - b)

const cachedPriceResults = Utils.cacheGet<TFlowerPrice[]>('calc:priceResults') || []
const cachedStockList = Utils.cacheGet<StockItem[]>('calc:stockList') || []
const cacheHideAppLogo = Utils.cacheGet<boolean>('calc:hideAppLogo') || false // 是否隐藏App品牌


export default function CalculatorPage() {
  const isDarkTheme = useStore($isDarkTheme)
  const wxShareQrCode = useStore($wxShareQrCode)

  // 费率相关状态
  const [rate, setRate] = createSignal<number>(defaultCacheRate) // 默认10%的费率
  const [rateOptions, setRateOptions] = createSignal<number[]>([...defaultRateOptions])
  const [showRatePopup, setShowRatePopup] = createSignal(false)
  const [customRate, setCustomRate] = createSignal<string>('')
  const [hideAppLogo, setHideAppLogo] = createSignal(cacheHideAppLogo)

  // 搜索相关状态
  const [searchKeyword, setSearchKeyword] = createSignal('')
  const [searchResults, setSearchResults] = createSignal<TFlower[]>([]) // 下拉搜索结果
  const [searchPageIndex, setSearchPageIndex] = createSignal(0)
  const [searchPageTotal, setSearchPageTotal] = createSignal(0)

  const [showSearchResults, setShowSearchResults] = createSignal(false)
  const [searchLoading, setSearchLoading] = createSignal(false)

  // 最新价格相关状态
  const [priceResults, setPriceResults] = createSignal<TFlowerPrice[]>(cachedPriceResults) // 最新价格列表
  const [priceLoading, setPriceLoading] = createSignal(false)

  // 供货单相关状态
  const [stockList, setStockList] = createSignal<StockItem[]>(cachedStockList)
  const [showQuantityPopup, setShowQuantityPopup] = createSignal(false)
  const [showPricePopup, setShowPricePopup] = createSignal(false)
  const [currentItem, setCurrentItem] = createSignal<StockItem | null>(null)
  const [editQuantity, setEditQuantity] = createSignal<string>('')
  const [editPrice, setEditPrice] = createSignal<string>('')

  // 分享图片相关状态
  const [shareImageUrl, setShareImageUrl] = createSignal<string>('')
  const [showSharePopup, setShowSharePopup] = createSignal(false)
  const [isCanvasReady, setIsCanvasReady] = createSignal(false)

  // 使用API搜索花卉数据（输入框下拉搜索）
  const baseSearchFlowers = async (keyword: string) => {
    const pageSize = 20
    if (!keyword || keyword.length < 1 || (searchPageIndex() > 0 && searchPageIndex() + 1 > searchPageTotal())) return
    try {
      setSearchLoading(true)
      const response = await apiFlowerSearch({
        keyword,
        onlyFlower: true,
        pageIndex: searchPageIndex(),
        pageSize: pageSize // 下拉框显示10条结果
      })
      setSearchResults([...searchResults(), ...response.rows])
      setSearchPageTotal(response.totalPageCount)
      setShowSearchResults(true)
    } catch (error) {
      console.error('搜索花卉失败', error)
    } finally {
      setSearchLoading(false)
    }
  }

  const searchFlowers = useDebounce(async () => {
    const keyword = searchKeyword().trim()
    if (!keyword) {
      setSearchResults([])
      setShowSearchResults(false)
      return
    }
    setSearchPageIndex(0)
    setSearchResults([])
    setSearchPageTotal(0)
    return baseSearchFlowers(keyword)
  }, 200)

  const loadMoreSearchResults = () => {
    console.log('加载更多搜索结果')
    setSearchPageIndex(searchPageIndex() + 1)
    return baseSearchFlowers(searchKeyword().trim())
  }

  // 获取花卉最新价格
  const getFlowerPrices = async (flower: TFlower) => {
    try {
      setPriceLoading(true)
      const prices = await apiFlowerPrices(flower.id)
      prices.forEach(price => {
        price.flower = flower
      })
      // 将新获取的价格添加到价格列表中（避免重复）
      setPriceResults(prices)
      setShowSearchResults(false) // 隐藏搜索结果下拉框
    } catch (error) {
      console.error('获取花卉价格失败', error)
    } finally {
      setPriceLoading(false)
    }
  }

  // 添加到供货单（从价格列表添加）
  const addToStock = (price: TFlowerPrice) => {
    const existingItem = stockList().find(item => item.id === price.id)
    if (existingItem) {
      // 如果已存在，增加数量
      setStockList(prev => prev.map(item =>
        item.id === price.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ))
    } else {
      // 如果不存在，添加新项目
      // 从TFlowerPrice转换为StockItem
      const newItem: StockItem = {
        id: price.id,
        category: price.flower.parent?.name || '',
        variety: price.flower.name,
        grade: price.grade || '',
        price: calculatePrice(price.price),
        quantity: 1,
        actualPrice: calculatePrice(price.price)
      }
      setStockList(prev => [...prev, newItem])
    }
  }

  // 根据费率计算价格
  const calculatePrice = (basePrice: number) => {
    return parseFloat((basePrice * (1 + rate())).toFixed(2))
  }

  // 打开数量编辑弹窗
  const openQuantityPopup = (item: StockItem) => {
    setCurrentItem(item)
    setEditQuantity(item.quantity.toString())
    setShowQuantityPopup(true)
  }

  // 打开价格编辑弹窗
  const openPricePopup = (item: StockItem) => {
    setCurrentItem(item)
    setEditPrice(item.actualPrice.toString())
    setShowPricePopup(true)
  }

  // 更新数量
  const updateQuantity = () => {
    if (!currentItem()) return
    const quantity = parseInt(editQuantity())
    if (isNaN(quantity) || quantity <= 0) return

    setStockList(prev => prev.map(item =>
      item.id === currentItem()?.id
        ? { ...item, quantity }
        : item
    ))
    setShowQuantityPopup(false)
  }

  // 增加数量
  const increaseQuantity = () => {
    const current = parseInt(editQuantity())
    if (isNaN(current)) {
      setEditQuantity('1')
    } else {
      setEditQuantity((current + 1).toString())
    }
  }

  // 减少数量
  const decreaseQuantity = () => {
    const current = parseInt(editQuantity())
    if (!isNaN(current) && current > 1) {
      setEditQuantity((current - 1).toString())
    }
  }

  // 更新价格
  const updatePrice = () => {
    if (!currentItem()) return
    const price = parseFloat(editPrice())
    if (isNaN(price) || price <= 0) return

    setStockList(prev => prev.map(item =>
      item.id === currentItem()?.id
        ? { ...item, actualPrice: price, }
        : item
    ))
    setShowPricePopup(false)
  }

  // 从供货单中移除
  const removeFromStock = (id: string) => {
    setStockList(prev => prev.filter(item => item.id !== id))
  }

  // 添加自定义费率
  const addCustomRate = () => {
    let rateValue = parseFloat(customRate())
    if (isNaN(rateValue) || rateValue < 0) return
    rateValue = rateValue / 100
    // 添加到选项中（如果不存在）
    if (!rateOptions().includes(rateValue)) {
      setRateOptions(Array.from(new Set([...systemRateOptions, rateValue])).sort((a, b) => a - b))
    }
    // 设置为当前费率
    setRate(rateValue)
    setShowRatePopup(false)
  }

  // 选择费率选项
  const selectRateOption = (value: number) => {
    setRate(value)
    setShowRatePopup(false)
  }

  // 计算供货单统计信息
  const stockSummary = () => {
    const items = stockList()
    if (items.length === 0) return { totalQuantity: 0, totalAmount: 0, averagePrice: 0 }

    const totalQuantity = items.reduce((sum, item) => sum + item.quantity, 0)
    const totalAmount = items.reduce((sum, item) => sum + item.quantity * item.actualPrice, 0)
    const averagePrice = totalQuantity > 0 ? totalAmount / totalQuantity : 0

    return {
      totalQuantity,
      totalAmount: parseFloat(totalAmount.toFixed(2)),
      averagePrice: parseFloat(averagePrice.toFixed(2))
    }
  }

  // 分享供货单
  const shareStockList = () => {

    if (stockList().length === 0) {
      Taro.showToast({
        title: '供货单为空',
        icon: 'none'
      })
      return
    }

    // 直接显示分享弹窗，在弹窗打开后绘制Canvas
    setShowSharePopup(true)

    // 在下一个渲染周期绘制Canvas
    setTimeout(() => {
      drawShareCanvas()
    }, 100)
  }

  let qrcodeImg: any = null

  // 绘制分享用的Canvas
  const drawShareCanvas = async () => {
    try {
      setIsCanvasReady(false)

      const canvasWidth = 750
      const columns = [
        { title: '品类', align: 'left', widthRatio: 50, content: (item: StockItem) => item.category },
        { title: '品种', align: 'left', widthRatio: 70, content: (item: StockItem) => item.varietyArr },
        { title: '等级', align: 'center', widthRatio: 20, content: (item: StockItem) => item.grade },
        { title: '数量(扎)', align: 'right', widthRatio: 30, content: (item: StockItem) => item.quantity.toString() },
        { title: '价格', align: 'right', widthRatio: 30, content: (item: StockItem) => item.actualPrice.toFixed(2) },
        { title: '小计(元)', align: 'right', widthRatio: 50, content: (item: StockItem) => (item.quantity * item.actualPrice).toFixed(2) },
      ]
      const totalWidth = columns.reduce((sum, col) => sum + col.widthRatio, 0)
      const columnWidths = columns.map(col => col.widthRatio / totalWidth * (canvasWidth - 60))
      const varietyWidth = columnWidths[1]

      const varietyRowLetterCount = Math.floor(varietyWidth / 20)

      // 获取供货单数据
      const items = stockList().map(item => ({
        ...item,
        varietyArr: item.variety.split('').reduce((acc: string[], char: string) => {
          if (acc[acc.length - 1].length >= varietyRowLetterCount) {
            return [...acc, char]
          } else {
            acc[acc.length - 1] += char
          }
          return acc
        }, [''])
      }))

      const summary = stockSummary()

      // 计算画布高度
      const headerHeight = 160 // 标题和表头的高度
      const footerHeight = 150 // 底部统计信息的高度
      // const appLogoHeight = hideAppLogo() ? 0 : 120 // 底部logo区域高度


      // 计算内容高度
      const contentHeight = headerHeight + (items.reduce((acc, item) => acc + item.varietyArr.length * 30 + 20, 0)) + footerHeight
      const canvasHeight = contentHeight



      // 创建离屏canvas
      const canvas = Taro.createOffscreenCanvas({
        type: '2d',
        width: canvasWidth,
        height: canvasHeight
      })

      const ctx = canvas.getContext('2d') as CanvasRenderingContext2D

      // 设置背景
      ctx.fillStyle = '#FFFFFF'
      ctx.fillRect(0, 0, canvasWidth, canvasHeight)

      // // 加载二维码图片
      // let qrCodePath: string | null = null
      // if (!hideAppLogo()) {
      //   try {
      //     // 使用Taro.getImageInfo获取图片信息
      //     const res = await Taro.getImageInfo({
      //       src: '/assets/qrcode.jpg'
      //     })
      //     qrCodePath = res.path
      //   } catch (error) {
      //     console.error('加载二维码失败:', error)
      //   }
      // }




      // 设置背景
      ctx.fillStyle = '#FFFFFF'
      ctx.fillRect(0, 0, canvasWidth, canvasHeight)


      // 设置标题
      ctx.fillStyle = '#000000'
      ctx.font = 'bold 36px sans-serif'
      ctx.textAlign = 'center'
      ctx.fillText('供货单', canvasWidth / 2, 54)

      // 设置日期
      const today = dayjs().format('YYYY年MM月DD日')
      ctx.font = '20px sans-serif'
      ctx.textAlign = 'center'
      ctx.fillText(today, canvasWidth / 2, 100)

      // 设置表头
      ctx.font = 'bold 20px sans-serif'

      // 计算列头位置
      let xPos = 30 // 起始位置
      columns.forEach((col, index) => {
        const width = columnWidths[index]

        // 根据对齐方式设置文本对齐
        ctx.textAlign = col.align as CanvasTextAlign

        // 计算实际绘制位置
        let textX = xPos
        if (col.align === 'center') {
          textX += width / 2
        } else if (col.align === 'right') {
          textX += width - 10 // 右侧留出10像素的空间
        }

        ctx.fillText(col.title, textX, 150)
        xPos += width
      })

      // 绘制表头分隔线
      ctx.beginPath()
      ctx.moveTo(30, 160)
      ctx.lineTo(canvasWidth - 30, 160)
      ctx.strokeStyle = '#000000'
      ctx.lineWidth = 2
      ctx.stroke()

      // 绘制表格内容
      ctx.font = '20px sans-serif'

      let y = headerHeight + 40

      items.forEach((item, index) => {
        // 使用columns定义绘制每一行
        let xPos = 30

        columns.forEach((col, colIndex) => {
          const width = columnWidths[colIndex]

          // 根据对齐方式设置文本对齐
          ctx.textAlign = col.align as CanvasTextAlign

          // 计算实际绘制位置
          let textX = xPos
          if (col.align === 'center') {
            textX += width / 2
          } else if (col.align === 'right') {
            textX += width - 10 // 右侧留出10像素的空间
          }


          // 使用content函数获取实际显示内容
          const content = col.content(item)
          if (Array.isArray(content)) {
            content.forEach((line, lineIndex) => {
              ctx.fillText(line, textX, y + lineIndex * 30)
            })
          } else {
            ctx.fillText(content as string, textX, y)
          }
          xPos += width
        })
        y += 20 + 30 * item.varietyArr.length

        // 绘制行分隔线
        if (index < items.length - 1) {
          ctx.beginPath()
          ctx.moveTo(30, y - 30)
          ctx.lineTo(canvasWidth - 30, y - 30)
          ctx.strokeStyle = '#DDDDDD'
          ctx.lineWidth = 1
          ctx.stroke()
        }
      })


      // 绘制底部分隔线
      y -= 20
      ctx.beginPath()
      ctx.moveTo(30, y)
      ctx.lineTo(canvasWidth - 30, y)
      ctx.strokeStyle = '#000000'
      ctx.lineWidth = 2
      ctx.stroke()

      // 绘制统计信息
      const summaries = [
        { label: '总数', value: summary.totalQuantity },
        // { label: '均价', value: summary.averagePrice },
        { label: '总金额', value: summary.totalAmount },
      ]
      y += 50
      ctx.font = 'bold 22px sans-serif'
      ctx.textAlign = 'right'
      summaries.forEach((summary, index) => {
        ctx.fillText(`${summary.label}: ${summary.value}`, canvasWidth - 30, y + index * 50)
      })

      // 绘制底部logo区域
      if (!hideAppLogo()) {
        const qrCodeSize = 80
        const qrCodeX = 30  // 左侧位置
        const qrCodeY = y - 20

        if (!qrcodeImg) {
          // 绘制二维码
          await new Promise<void>((resolve) => {
            const img = canvas.createImage() as any
            img.onload = () => {
              qrcodeImg = img
              resolve()
            }
            img.onerror = () => {
              console.error('加载二维码图片失败')
              resolve()
            }
            img.src = wxShareQrCode() || qrCodeSrc
          })
        }

        ctx.drawImage(qrcodeImg, qrCodeX, qrCodeY - 10, qrCodeSize, qrCodeSize)


        // 绘制小程序名称和提示文字（二维码右侧）
        const textX = qrCodeX + qrCodeSize + 20

        // 小程序名称
        ctx.fillStyle = '#000000'
        ctx.font = 'bold 24px sans-serif'
        ctx.textAlign = 'left'
        ctx.fillText('鲜切花报价', textX, qrCodeY + 20)

        // 提示文字
        ctx.font = '16px sans-serif'
        ctx.fillText('长按识别小程序码', textX, qrCodeY + 50)
      }

      // 转换为图片
      Taro.canvasToTempFilePath({
        canvas: canvas as any,
        fileType: 'jpg',
        quality: 1,
        success: (res) => {
          setShareImageUrl(res.tempFilePath)
          setIsCanvasReady(true)
        },
        fail: (err) => {
          console.error('生成图片失败:', err)
          Taro.showToast({
            title: '生成分享图片失败',
            icon: 'none'
          })
        }
      })
      // })
    } catch (error) {
      console.error('生成分享图片失败:', error)
      Taro.showToast({
        title: '生成分享图片失败',
        icon: 'none'
      })
    }
  }

  const shareImage = () => {
    Taro.showShareImageMenu({
      path: shareImageUrl(),
      needShowEntrance: !hideAppLogo(),
      success: () => {
        Taro.showToast({
          title: '转发成功',
          icon: 'success'
        })
        setShowSharePopup(false)
      },
      fail: (err) => {
        console.error('转发失败', err)
        Taro.showToast({
          title: '转发失败',
          icon: 'none'
        })
      }
    } as any)
  }

  const clear = () => {
    if (stockList().length === 0 && priceResults().length === 0) {
      Taro.showModal({
        title: '提示',
        content: '当前等级价格和供货单为空，无需清理',
      })
      return
    }
    Taro.showModal({
      title: '提示',
      content: '确定要清空所有数据吗？',
      success: (res) => {
        if (res.confirm) {
          setStockList([])
          setPriceResults([])
          Utils.cacheRemove('stockList')
          Utils.cacheRemove('priceResults')
        }
      }
    })

  }

  createEffect(() => {
    Utils.cacheSet('calc:rate', rate())
  })

  createEffect(() => {
    Utils.cacheSet('calc:stockList', stockList())
  })
  createEffect(() => {
    Utils.cacheSet('calc:priceResults', priceResults())
  })

  // 当费率变化时，更新供货单中的价格
  createEffect(() => {
    const currentRate = rate()
    setStockList(prev => prev.map(item => {
      // 只更新未修改过的价格（即与计算价格相同的项目）
      const calculatedPrice = parseFloat((item.price * (1 + currentRate)).toFixed(2))
      const originalCalculatedPrice = parseFloat((item.price * (1 + currentRate - 0.1)).toFixed(2)) // 假设之前是10%

      // 如果当前价格等于原始计算价格，则更新为新计算价格
      if (Math.abs(item.actualPrice - originalCalculatedPrice) < 0.01) {
        return { ...item, actualPrice: calculatedPrice }
      }
      return item
    }))
  })

  createEffect(() => {
    if (showSearchResults()) {

    }
  })

  useDidShow(() => {
    Utils.backPush()
  })




  return (
    <BasePage>
      <MyNavbar background={isDarkTheme() ? '#1C1917' : '#ffffff'} >
        <View class="flex-inline items-center gap-2 h-full">
          <Text class="text-lg font-bold">计算器</Text>
          <View class="-m-1 p-1" onClick={clear}>
            <Iconfont name="clear" size={36} class="text-green-500" />
          </View>

        </View>
      </MyNavbar>
      {/* 费率与搜索区域 */}
      <View class="flex h-64rpx p-4 pt-2 bg-white dark:bg-stone-900">
        <View class="text-x h-full w-28 shrink-0 rounded-lt rounded-bl border border-r-none border-solid border-gray-300 dark:border-stone-600 flex items-center box-border px-2" onClick={() => setShowRatePopup(true)}>
          <View class="flex-1 ">费率 {(rate() * 100).toFixed(0)}%</View>
          <Iconfont name="right" size={24} class="transform rotate-90 opacity-50" />
        </View>

        <View class="flex-1 h-full flex items-center relative searchBox">
          <Input
            class="flex-1 h-full box-border px-2 py-1 text-sm border border-solid border-gray-300 dark:border-stone-600"
            type="text"
            placeholder="搜索花卉"
            value={searchKeyword()}
            onInput={(e) => {
              setSearchKeyword(e.detail.value)
              if (e.detail.value.trim().length > 0) {
                searchFlowers()
              } else {
                setShowSearchResults(false)
              }
            }}
            onFocus={() => {
              if (searchKeyword().trim().length > 0 && searchResults().length > 0) {
                setShowSearchResults(true)
              }
            }}

          />
          {showSearchResults() && <View class="w-full h-full fixed top-0 left-0 right-0 z-0" onClick={() => setShowSearchResults(false)}></View>}
          <View
            class="bg-green-500 text-white text-sm w-14 h-full flex items-center justify-center rounded-tr rounded-br"
            style={{
              color: searchLoading() ? 'gray' : 'white',
            }}
            onClick={searchFlowers}
          >
            {searchLoading() ? '搜索中...' : '搜索'}
          </View>

          {/* 下拉搜索结果 */}
          <Show when={showSearchResults() && searchResults().length > 0}>
            <ScrollView
              class="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-stone-900 rounded shadow-lg z-50 max-h-60 overflow-y-auto"
              scrollY
              onScrollToLower={loadMoreSearchResults}
            >
              <For each={searchResults()}>
                {(flower) => (
                  <View
                    class="p-2 cursor-pointer flex justify-between items-center"
                    onClick={() => getFlowerPrices(flower)}
                  >
                    <Text>{flower.name}</Text>
                    <Text class="op-50">{flower.parent?.name || '无分类'}</Text>
                  </View>
                )}
              </For>
            </ScrollView>
          </Show>
        </View>
      </View>
      <View class="flex flex-col gap-4 my-4">
        {/* 最新价格 */}

        <View class="bg-white dark:bg-stone-900">
          <View class="p-4 flex flex-col gap-1">
            <View class="text-32rpx font-bold">等级价格</View>
            <View class="text-24rpx op-50">价格仅供参考</View>
          </View>
          <Show when={priceResults().length > 0}>
            <View class="px-4 pb-4">
              <MyTable
                bordered
                columns={[
                  { title: '品种', dataIndex: 'flower.name', width: 150 },
                  { title: '等级', dataIndex: 'grade', width: 60, align: 'center', render: (record) => <View class="flex items-center justify-center">{record.grade}</View> },
                  {
                    title: '价格(元/扎)', dataIndex: 'price', width: 200, render: (record) => {
                      return <View>
                        <Text>{record.price}</Text>
                        <Text class="op-50">×</Text>
                        <Text>{(rate() * 100 + 100).toFixed(0)}%</Text>
                        <Text class="op-50">=</Text>
                        <Text>{calculatePrice(record.price)}</Text>
                      </View>
                    }
                  },
                  {
                    title: '操作', dataIndex: 'action', width: 80, align: 'center', render: (record) => {
                      return <View class="h-full flex items-center justify-center -my-2" onClick={() => addToStock(record)}>
                        <View class="text-green-500 px-2 py-1 rounded">添加</View>
                      </View>
                    }
                  },
                ]}
                dataSource={priceResults()}
              />
            </View>
          </Show>
          {/* 加载中状态 */}
          <Show when={priceLoading()}>
            <View class="p-4 pt-0 op-50">
              <Text>加载价格中...</Text>
            </View>
          </Show>
          <Show when={!priceLoading() && priceResults().length === 0}>
            <View class="p-4 pt-0 op-50">
              <Text>请搜索并选择花卉</Text>
            </View>
          </Show>
        </View>
      </View>

      <View class="flex flex-col gap-4 my-4">
        {/* 最新价格 */}
        <View class="bg-white dark:bg-stone-900">
          <View class="p-4 flex flex-col gap-1">
            <View class="text-32rpx font-bold">供货单</View>
            <View class="text-24rpx op-50">可点击修改数量或价格</View>
          </View>
          <Show when={stockList().length > 0} fallback={<View class="p-4 pt-0 op-50">暂无备货项</View>}>
            <View class="px-4 pb-4">
              <MyTable
                bordered
                columns={[
                  { title: '品种', dataIndex: 'variety', width: 170 },
                  {
                    title: '等级', dataIndex: 'grade', width: 60, align: 'center', render: (record) => {
                      return <View class="flex items-center justify-center">
                        <Text>{record.grade}</Text>
                      </View>
                    }
                  },
                  {
                    title: '数量', dataIndex: 'quantity', width: 80, align: 'center', render: (record) => {
                      return <View onClick={() => openQuantityPopup(record)} class="flex items-center justify-center">
                        <Text class="text-green-500">{record.quantity}</Text>
                        <Iconfont name="edit-square" size={24} class="ml-1 text-green-500" />
                      </View>
                    }
                  },
                  {
                    title: '价格(元/扎)', dataIndex: 'price', width: 140, align: 'center', render: (record) => {
                      return <View onClick={() => openPricePopup(record)} class="flex gap-1 justify-center">
                        <Show when={record.actualPrice !== record.price}>
                          <Text class="line-through op-50">{record.price}</Text>
                        </Show>
                        <Text class="text-green-500">{record.actualPrice}</Text>
                        <Iconfont name="edit-square" size={24} class="ml-1 text-green-500" />
                      </View>
                    }
                  },
                  {
                    title: '小计', dataIndex: 'subtotal', width: 100, align: 'center', render: (record) => {
                      return <View>
                        <Text>{(record.quantity * record.actualPrice).toFixed(2)}</Text>
                      </View>
                    }
                  },
                  {
                    title: '操作', dataIndex: 'action', width: 100, align: 'center', render: (record) => {
                      return <View class="h-full flex items-center justify-center -my-2" onClick={() => removeFromStock(record.id)}>
                        <View class="text-green-500 px-2 py-1 rounded">删除</View>
                      </View>
                    }
                  },
                ]}
                dataSource={stockList()}
              />
              <View class="mt-4 flex justify-between items-center">
                <View class="flex gap-4 text-xs font-bold">
                  <Text>总数: {stockSummary().totalQuantity}</Text>
                  <Text>均价: {stockSummary().averagePrice}</Text>
                  <Text>总金额: {stockSummary().totalAmount}</Text>
                </View>
                <View onClick={shareStockList} class="bg-green-500 text-white px-2 py-1 rounded -my-2" >分享供货单</View>
              </View>
            </View>
          </Show>
        </View>
      </View>

      {/* 费率设置弹窗 */}
      <MyPopup
        open={showRatePopup()}
        onOpenChange={setShowRatePopup}
        title="设置费率"
        btns="close"
      >
        <View class="p-4">
          <View class="mb-4">
            <View class="mb-2 font-bold">选择费率</View>
            <View class="flex flex-wrap gap-1 justify-start">
              <For each={rateOptions()}>
                {(option) => (
                  <View
                    class={` text-sm px-4 py-1 rounded ${rate() === option ? 'bg-green-500 text-white' : 'bg-gray-100 dark:bg-stone-600'}`}
                    onClick={() => selectRateOption(option)}
                  >
                    {(option * 100).toFixed(0)}%
                  </View>
                )}
              </For>
            </View>
          </View>

          <View class="mb-4">
            <View class="mb-2 font-bold">自定义费率</View>
            <View class="flex items-center h-64rpx">
              <View class="flex-1 relative h-full">
                <Input type="number" placeholder="输入自定义费率百分比" class="box-border flex-1 h-full border border-solid border-gray-300 dark:border-stone-600  px-2 rounded-lt rounded-bl"
                  value={customRate()}
                  onInput={(e) => setCustomRate(e.detail.value)}
                />
                <Text class="absolute right-2 top-1/2 transform -translate-y-1/2 op-50">%</Text>
              </View>


              <View
                class="h-full bg-green-500 text-white text-sm rounded-tr rounded-br px-4 flex items-center"
                onClick={addCustomRate}
              >
                确定
              </View>
            </View>
          </View>
        </View>
      </MyPopup>

      {/* 数量编辑弹窗 */}
      <MyPopup
        open={showQuantityPopup()}
        onOpenChange={setShowQuantityPopup}
        title="修改数量"
        btns="cancelAndOk"
        onOk={updateQuantity}
      >
        <View class="p-4">
          <View class="flex items-center justify-center gap-4 mb-4">
            <View
              class={`bg-gray-200 w-10 h-10 flex items-center justify-center rounded-full ${parseInt(editQuantity()) > 1 ? 'bg-green-500 text-white' : 'bg-gray-400 op-50'}`}
              onClick={decreaseQuantity}
            >
              <Text class="text-xl">-</Text>
            </View>
            <Input
              type="number"
              placeholder="输入数量"
              class="p-2 border border-solid border-stone-200 rounded-lg text-center w-200rpx"
              value={editQuantity()}
              onInput={(e) => setEditQuantity(e.detail.value)}
            />

            <View
              class="w-10 h-10 flex items-center justify-center rounded-full bg-green-500 text-white"
              onClick={increaseQuantity}
            >
              <Text class="text-xl">+</Text>
            </View>
          </View>

        </View>
      </MyPopup>

      {/* 价格编辑弹窗 */}
      <MyPopup
        open={showPricePopup()}
        onOpenChange={setShowPricePopup}
        title="修改价格"
        btns="cancelAndOk"
        onOk={updatePrice}
      >
        <View class="p-4">
          <View class="mb-4">
            <Input
              type="number"
              placeholder="输入价格"
              class="p-2 border border-solid border-stone-200 rounded-lg"
              value={editPrice()}
              onInput={(e) => setEditPrice(e.detail.value)}
            />
          </View>
        </View>
      </MyPopup>

      {/* 分享图片弹窗 */}
      <MyPopup
        open={showSharePopup()}
        onOpenChange={setShowSharePopup}
        title="分享供货单"
        btns="cancelAndOk"
        okText="转发"
        onOk={shareImage}
      >

        {/* 显示logo开关 */}
        <View class="box-border p-4 w-full flex items-center justify-between">
          <Text>显示小程序来源</Text>
          <Switch
            checked={!hideAppLogo()}
            onChange={() => {
              const newValue = !hideAppLogo()
              setHideAppLogo(newValue)
              Utils.cacheSet('calc:hideAppLogo', newValue)
              // 重新生成分享图
              setTimeout(() => {
                drawShareCanvas()
              }, 100)
            }}
            color="#f97316"
          />
        </View>

        <View class="h-60vh py-2 box-border w-full overflow-y-auto flex flex-col items-center justify-center bg-zinc-100 dark:bg-stone-800 rounded-lg">
          <Show when={isCanvasReady()} fallback={(
            <View class="p-4 text-center flex-1 flex items-center justify-center">
              <Text>正在生成分享图片...</Text>
            </View>
          )}>
            <Image
              src={shareImageUrl()}
              onClick={() => Taro.previewImage({ urls: [shareImageUrl()] })}
              class="w-full h-full"
              mode="aspectFit"
              showMenuByLongpress
            />
          </Show>
        </View>

      </MyPopup>

      <Tabbar />
    </BasePage>
  )
}
