import { View, Text } from "@tarojs/components";
import { createMemo, createSignal, onMount, Show } from "solid-js";
import { BasePage } from "@/components/BasePage";
import { MyNavbar } from "@/components/MyNavbar";
import { Utils } from "@/utils/utils";
import { apiVipFeeOrderClose, apiVipFeeOrderMyGetById, apiVipFeeOrderPay } from "@/apis/apis";
import { MyButton } from "@/components/MyButton";
import { SafeBottom } from "@/components/SafeBottom";
import { AgreeDicts } from "@/utils/agreeDict";
import { login } from "@/store/appStore";
import dayjs from "dayjs";
import { useCountdown } from "@/hooks/useCountdown";
import { produce } from 'immer';

export default function VipOrderDetail() {
  const [order, setOrder] = createSignal<TVipFeeOrder | null>(null);
  const [loading, setLoading] = createSignal(true);
  const [paying, setPaying] = createSignal(false);

  const query = Utils.getQuery<{ id: string }>();

  const displayOrder = createMemo(() => {
    const item = order();
    return item ? {
      order: item,
      id: item.id,
      title: item.vipFeeTypeName,
      statusText: AgreeDicts.VipFee_status.valueLabelMap[item.status],
      statusColor: AgreeDicts.VipFee_status.valueDataMap[item.status]?.data?.color || '#000',
      infos: [
        { title: '订单编号', value: item.id, show: true },
        { title: '会员类型', value: item.vipFeeTypeName, show: true },
        { title: '会员时长', value: item.vipFeeTypeDays + '天', show: true },
        { title: '订单金额', value: <Text class="text-green-500 font-bold">¥{Utils.formatPrice(item.amountCent)}</Text>, show: true },
        { title: '创建时间', value: Utils.formatDayTime(item.createdAt), show: true },
        { title: '自动关闭时间', value: Utils.formatDayTime(item.autoCloseTime!), show: item.status !== 'PAID' },
        { title: '生效开始时间', value: Utils.formatDayTime(item.startTime!), show: item.status === 'PAID' },
        { title: '生效结束时间', value: Utils.formatDayTime(item.endTime!), show: item.status === 'PAID' },
      ].filter(item => item.show),
      actions: [
        {
          text: <Text>{useCountdown(item.autoCloseTime!, {
            onEnd: () => {
              if(item.status !== 'WAIT_PAY') return;
              setOrder(produce(item, draft => {
                draft.status = 'CLOSED'
              }))
              apiVipFeeOrderClose(item.id)
            }
          })} 支付</Text>,
          loading: paying(),
          onClick: handlePay,
          show: item.status === 'WAIT_PAY' && dayjs(item.autoCloseTime!).isAfter(dayjs())
        }
      ].filter(item => item.show),
    } : null;
  });

  // 加载订单详情
  const loadOrderDetail = async () => {
    if (!query.id) {
      Utils.toast('订单ID不能为空');
      return;
    }

    try {
      setLoading(true);
      console.log('query.id', query.id);
      const data = await apiVipFeeOrderMyGetById(query.id);
      setOrder(data);
    } catch (error) {
      Utils.toast('加载失败');
    } finally {
      setLoading(false);
    }
  };

  // 支付订单
  const handlePay = async () => {
    const currentOrder = order();
    if (!currentOrder || paying()) return;

    try {
      setPaying(true);

      // 获取支付参数
      const payParams = await apiVipFeeOrderPay({ id: currentOrder.id });

      // 调用支付并监听状态
      const success = await Utils.payAndListenStatus(payParams);

      if (success) {
        Utils.toast('支付成功！');
        // 重新加载订单详情
        loadOrderDetail();
        login()
      }
    } catch (error: any) {
      Utils.toast(error.message || '支付失败');
    } finally {
      setPaying(false);
    }
  };

  onMount(() => {
    loadOrderDetail();
  });

  return (
    <BasePage>
      <MyNavbar title="会员费订单详情" />
      <View class="p-4">
        <Show when={!loading()} fallback={
          <View class="flex justify-center items-center h-300px">
            <Text class="text-gray-500">加载中...</Text>
          </View>
        }>
          <Show when={displayOrder()} fallback={
            <View class="flex justify-center items-center h-300px">
              <Text class="text-gray-500">订单不存在</Text>
            </View>
          }>
            {(currentOrder) => (
              <>
                <View class="bg-white dark:bg-stone-900 rounded-2 p-4 mb-4 shadow">
                  <View class="flex justify-between items-center mb-4 ">
                    <View class="font-bold">{currentOrder().title}</View>
                    <View style={{ color: currentOrder().statusColor }}>
                      {currentOrder().statusText}
                    </View>
                  </View>
                  <View class="flex flex-col">
                    {currentOrder().infos.map((info, index) => (
                      <View class="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                        <Text class="text-24rpx text-gray-600 dark:text-gray-300">{info.title}</Text>
                        <Text class="text-24rpx text-gray-800 dark:text-white text-right max-w-100 break-all">{info.value}</Text>
                      </View>
                    ))}
                  </View>
                </View>

                <Show when={currentOrder().actions.length > 0}>
                  <View class="fixed bottom-0 left-0 right-0 bg-white dark:bg-stone-900 shadow-lg">
                    <View class="flex gap-4 px-4 pt-2">
                      {currentOrder().actions.map((action) => (
                        <MyButton
                          type="primary"
                          // size="small"
                          class="flex-1"
                          loading={action.loading}
                          onClick={action.onClick}
                        >
                          {action.text}
                        </MyButton>
                      ))}
                    </View>
                    <SafeBottom />
                  </View>
                </Show>
              </>
            )}
          </Show>
        </Show>
        <SafeBottom />
      </View>
    </BasePage>
  );
}
