import path from 'path';
import fs from 'fs';
import _ from 'lodash';

const componentName = _.upperFirst(_.camelCase(process.argv[2]));
const componentDirPath = path.join('./src/components', componentName);

if (fs.existsSync(componentDirPath)) {
  throw new Error(`Component ${componentName} 已存在`);
}

fs.mkdirSync(componentDirPath, { recursive: true });
const tsxFileContent = `import { View } from "@tarojs/components"
import { mergeProps } from "solid-js"

export interface T${componentName}Props {
  // TODO: add props
}

export const ${componentName} = (_props: T${componentName}Props) => {
  const props = mergeProps({
    // TODO: default props
  }, _props)

  return (
    <View class="${componentName}">
      ${componentName}
    </View>
  )
}`
const tsxFilePath = path.join(componentDirPath, `${componentName}.tsx`);
fs.writeFileSync(tsxFilePath, tsxFileContent);

const indexFilePath = path.join(componentDirPath, `index.ts`);
const indexFileContent = `export * from './${componentName}'`;
fs.writeFileSync(indexFilePath, indexFileContent);
