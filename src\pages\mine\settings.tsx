import { View, Input, Switch, Image } from "@tarojs/components";
import { createSignal, onMount, Show } from "solid-js";
import { BasePage } from "@/components/BasePage";
import { MyNavbar } from "@/components/MyNavbar";
import { Utils } from "@/utils/utils";
import { apiUpdateSettings } from "@/apis/apis";
import { SafeBottom } from "@/components/SafeBottom";
import { $userInfo, updateUserInfo } from "@/store/appStore";
import { useStore } from "@nanostores/solid";
import { Iconfont } from "@/components/Iconfont";
import { MyButton } from "@/components/MyButton";
import { HttpUtils } from "@/utils/http";

export default function SettingsPage() {
  const userInfo = useStore($userInfo);
  
  // 表单状态
  const [nickname, setNickname] = createSignal('');
  const [email, setEmail] = createSignal('');
  const [autoUsePoints, setAutoUsePoints] = createSignal(false);
  const [avatar, setAvatar] = createSignal('');
  
  // UI 状态
  const [uploadingAvatar, setUploadingAvatar] = createSignal(false);

  // 初始化表单数据
  const initFormData = () => {
    if (userInfo()) {
      setNickname(userInfo()?.nickname || '');
      setEmail(userInfo()?.email || '');
      setAutoUsePoints(userInfo()?.autoUsePoints || false);
      setAvatar(userInfo()?.avatar || '');
    }
  };

  // 上传头像
  const handleUploadAvatar = async () => {
    try {
      setUploadingAvatar(true);
      
      await HttpUtils.uploadMedia({
        sourceType: ['album'],
        mediaType: ['image'],
        onChange: (data) => {
          if (data.status === 'success' && data.url) {
            setAvatar(data.url);
            handleSave('avatar');
          } else if (data.status === 'fail') {
            Utils.toast(data.message || '头像上传失败');
          }
        }
      });
    } catch (error) {
      Utils.toast('头像上传失败');
    } finally {
      setUploadingAvatar(false);
    }
  };

  // 保存设置
  const handleSave = async (key: string) => {
    let data: TClientUserSettingsReqDto = {}
    if(key === 'nickname') {
      if (!nickname().trim()) {
        Utils.toast('请输入昵称');
        return;
      }
      data.nickname = nickname().trim();
    } else if(key === 'email') {
      if (!email().trim()) {
        data.email = '';

      } else if (email().trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email().trim())) {
        Utils.toast('请输入正确的邮箱格式');
        return;
      }
      data.email = email().trim();
    } else if(key === 'autoUsePoints') {
      data.autoUsePoints = autoUsePoints();
    } else if(key === 'avatar') {
      data.avatar = avatar();
    }

    try {
     
      const result = await apiUpdateSettings(data);

      // 更新本地用户信息
      updateUserInfo({
        nickname: result.nickname,
        avatar: result.avatar,
        email: result.email,
        autoUsePoints: result.autoUsePoints,
      });

      Utils.toast('设置保存成功');
      
    } catch (error) {
      console.error('保存设置失败:', error);
    } 
  };

  onMount(() => {
    initFormData();
  });

  return (
    <BasePage>
      <MyNavbar title="个人设置" />
      
      <View class="p-4 flex flex-col gap-6">
        {/* 头像设置 */}
        <View class="bg-white dark:bg-stone-900 rounded-3 p-4">
          <View class="text-32rpx font-medium mb-4">头像</View>
          <View class="flex items-center gap-4">
            <View class="relative">
              <View class="w-20 h-20 rounded-full overflow-hidden bg-gray-100 dark:bg-stone-700">
                <Show when={avatar()} fallback={
                  <View class="w-full h-full flex items-center justify-center">
                    <Iconfont name="mine" size={60} class="text-gray-400" />
                  </View>
                }>
                  <Image 
                    src={Utils.imageSrc(avatar())} 
                    class="w-full h-full" 
                    mode="aspectFill" 
                  />
                </Show>
              </View>
              <Show when={uploadingAvatar()}>
                <View class="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                  <Iconfont name="loading" size={40} class="text-white animate-spin" />
                </View>
              </Show>
            </View>
            <View class="flex-1">
              <MyButton 
                size="small" 
                variant="outline"
                onClick={handleUploadAvatar}
                loading={uploadingAvatar()}
              >
                更换头像
              </MyButton>
              <View class="text-24rpx text-gray-500 mt-2">
                支持 JPG、PNG 格式，建议尺寸 200x200 像素
              </View>
            </View>
          </View>
        </View>

        {/* 基本信息 */}
        <View class="bg-white dark:bg-stone-900 rounded-3 p-4 box-border">
          <View class="text-32rpx font-medium mb-4">基本信息</View>
          
          {/* 昵称 */}
          <View class="mb-4">
            <View class="text-28rpx text-gray-600 dark:text-gray-300 mb-2">昵称</View>
            <Input
              class="p-3 bg-gray-50 dark:bg-stone-800 rounded-2 text-28rpx"
              placeholder="请输入昵称"
              value={nickname()}
              onInput={(e) => setNickname(e.detail.value)}
              onBlur={() => handleSave('nickname')}
              maxlength={20}
            />
          </View>

          {/* 邮箱 */}
          <View class="mb-4">
            <View class="text-28rpx text-gray-600 dark:text-gray-300 mb-2">邮箱</View>
            <Input
              class="p-3 bg-gray-50 dark:bg-stone-800 rounded-2 text-28rpx"
              placeholder="请输入邮箱地址（可选）"
              value={email()}
              onInput={(e) => setEmail(e.detail.value)}
              onBlur={() => handleSave('email')}
              type="text"
            />
            <View class="text-24rpx text-gray-500 mt-1">
              用于接收订阅内容
            </View>
          </View>
        </View>

        {/* 偏好设置 */}
        <View class="bg-white dark:bg-stone-900 rounded-3 p-4">
          <View class="text-32rpx font-medium mb-4">偏好设置</View>
          
          {/* 自动使用积分 */}
          <View class="flex items-center justify-between py-3">
            <View class="flex-1">
              <View class="text-28rpx text-gray-800 dark:text-gray-200 mb-1">
                自动使用积分
              </View>
              <View class="text-24rpx text-gray-500">
                支付时自动使用积分抵扣费用
              </View>
            </View>
            <Switch
              checked={autoUsePoints()}
              onChange={(e) => setAutoUsePoints(e.detail.value) && handleSave('autoUsePoints')}
              color="#22c55e"
            />
          </View>
        </View>
      </View>

      <SafeBottom />
    </BasePage>
  );
}
