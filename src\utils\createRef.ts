import { createRenderEffect, Ref } from "solid-js";

export function createRef<T extends Exclude<unknown, Function>>(
  getRef: () => Ref<T>,
  createRef: () => T,
) {
  createRenderEffect(() => {
    const refProp = getRef();
    if (!refProp) return
    if (typeof refProp !== "function") {
      throw new Error(
        "Should never happen, as solid always passes refs as functions",
      );
    }

    let refFunc = refProp as (value: T) => void;

    refFunc(createRef());
  });
}