{"name": "flowers-price-mini", "version": "1.0.0", "private": true, "description": "鲜切花报价", "templateInfo": {"name": "default", "typescript": true, "css": "Sass", "framework": "Solid"}, "postinstall": "weapp-tw patch", "scripts": {"upload": "tsx ./bin/upload.ts", "comp": "tsx ./bin/genComponent.ts", "page": "tsx ./bin/genPage.ts", "icon": "tsx ./bin/genIconfont.ts", "apis": "tsx ./bin/genApis.ts", "dev": "npm run dev:weapp", "build": "npm run build:weapp", "build:weapp": "taro build --type weapp --mode production", "build:swan": "taro build --type swan --mode production", "build:alipay": "taro build --type alipay --mode production", "build:tt": "taro build --type tt --mode production", "build:h5": "taro build --type h5 --mode production", "build:rn": "taro build --type rn --mode production", "build:qq": "taro build --type qq --mode production", "build:jd": "taro build --type jd --mode production", "build:harmony-hybrid": "taro build --type harmony-hybrid --mode production", "dev:weapp": "npm run build:weapp --mode development -- --watch", "dev:swan": "npm run build:swan --mode development -- --watch", "dev:alipay": "npm run build:alipay --mode development -- --watch", "dev:tt": "npm run build:tt --mode development -- --watch", "dev:h5": "npm run build:h5 --mode development -- --watch", "dev:rn": "npm run build:rn --mode development -- --watch", "dev:qq": "npm run build:qq --mode development -- --watch", "dev:jd": "npm run build:jd --mode development -- --watch", "dev:harmony-hybrid": "npm run build:harmony-hybrid --mode development -- --watch"}, "browserslist": ["defaults and fully supports es6-module", "maintained node versions"], "author": "", "dependencies": {"@babel/runtime": "^7.24.4", "@nanostores/solid": "^0.5.0", "@tarojs/components": "4.0.12", "@tarojs/helper": "4.0.12", "@tarojs/plugin-framework-solid": "4.0.12", "@tarojs/plugin-platform-alipay": "4.0.12", "@tarojs/plugin-platform-h5": "4.0.12", "@tarojs/plugin-platform-harmony-hybrid": "4.0.12", "@tarojs/plugin-platform-jd": "4.0.12", "@tarojs/plugin-platform-qq": "4.0.12", "@tarojs/plugin-platform-swan": "4.0.12", "@tarojs/plugin-platform-tt": "4.0.12", "@tarojs/plugin-platform-weapp": "4.0.12", "@tarojs/runtime": "4.0.12", "@tarojs/shared": "4.0.12", "@tarojs/taro": "4.0.12", "async-wait-until": "^2.0.27", "clsx": "^2.1.1", "dayjs": "^1.11.13", "immer": "^10.1.1", "lodash": "^4.17.21", "nanostores": "^0.11.4", "solid-js": "^1.9.5"}, "devDependencies": {"@babel/core": "^7.24.4", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@tarojs/cli": "4.0.12", "@tarojs/taro-loader": "4.0.12", "@tarojs/webpack5-runner": "4.0.12", "@types/classnames": "^2.3.4", "@types/js-yaml": "^4.0.9", "@types/lodash": "^4.17.15", "@types/node": "^18.19.76", "@types/webpack-env": "^1.13.6", "@unocss/preset-legacy-compat": "66.0.0", "@unocss/webpack": "66.0.0", "art-template": "^4.13.2", "autoprefixer": "^10.4.20", "babel-preset-taro": "4.0.12", "core-js": "^2.6.12", "eslint": "^8.57.0", "eslint-config-taro": "4.0.12", "form-data": "^4.0.2", "glob": "^11.0.1", "iconfont-parser": "^1.0.0", "js-yaml": "^4.1.0", "less": "^4.2.2", "miniprogram-ci": "^1.9.16", "mkdirp": "^3.0.1", "postcss": "^8.5.3", "sass": "^1.75.0", "stylelint": "^16.4.0", "tony-mockjs": "^1.1.4", "tsconfig-paths-webpack-plugin": "^4.1.0", "tsx": "^4.19.3", "typescript": "^5.4.5", "unocss": "66.0.0", "unocss-applet": "^0.10.0", "webpack": "5.91.0"}}