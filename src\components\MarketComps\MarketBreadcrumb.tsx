import { View } from "@tarojs/components"
import { Iconfont } from "../Iconfont"
import { createMemo, For } from "solid-js"

interface MarketBreadcrumbProps {
  flower: TFlower,
  onOpenFlower: (flowerId: string) => void
}

export function MarketBreadcrumb(props: MarketBreadcrumbProps) {
  const data = createMemo(() => {
    if (props.flower.id === 'root') return []
    if (props.flower.parent!.id === 'root') {
      return [
        {
          name: '全部',
          id: 'root'
        },
        {
          name: props.flower.name,
          id: props.flower.id
        }
      ]
    }
    return [
      {
        name: '全部',
        id: 'root'
      },
      {
        name: props.flower.parent!.name,
        id: props.flower.parent!.id
      },
      {
        name: props.flower.name,
        id: props.flower.id
      }
    ]
  })

  if (data().length === 0) return null

  return (
    <View class='flex items-center gap-1 h-full'>
      <For each={data()}>{(item, index) => {
        const isLast = index() === data().length - 1
        return (
          <>
            <View class={` ${isLast ? 'op-50' : ''}`} onClick={() => !isLast && props.onOpenFlower(item.id)}>{item.name}</View>
            {!isLast && <Iconfont name="right" class="op-50" />}
          </>
        )
      }}</For>
    </View>
  )
}
