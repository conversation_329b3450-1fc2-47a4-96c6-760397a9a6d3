import { Input } from "@tarojs/components";
import { FormItemContext } from "./useMyForm";
import { useContext } from "solid-js";
import clsx from "clsx";

export interface TMyFormInputProps {
  type?: 'text' | 'number'
  placeholder?: string
  class?: any
  style?: any
  disabled?: boolean
  bordered?: boolean
}

export function MyFormInput(props: TMyFormInputProps) {
  const formItem = useContext(FormItemContext)

  const onInput = (e: any) => {
    let value = e.detail.value
    if (props.type === 'number') {
      value = Number(value)
    }
    formItem.onChange(value)
  }
  return (
    <Input
      class={clsx({
        "py-2": true,
        'border border-solid border-stone-200 dark:border-stone-700 rounded-lg px-2': props.bordered,
        "text-gray-400 bg-stone-100 dark:bg-stone-800 ": props.disabled
      }, props.class)}
      style={props.style}
      type={props.type}
      disabled={props.disabled}
      value={formItem.value() ? formItem.value().toString() : ''}
      onInput={onInput}
      placeholder={props.placeholder}
    />
  )
}