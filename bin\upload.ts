import fs from 'fs';
import * as miniprogramCi from 'miniprogram-ci'

async function main() {
    const version = fs.readFileSync('./version.txt', 'utf-8').trim();
    console.log(`当前版本: ${version}`);

    const distConfigPath = './dist/project.config.json';
    // 检查dist/project.config.json是否存在
    if (!fs.existsSync(distConfigPath)) {
        console.error('错误: dist/project.config.json 不存在，请先执行 pnpm build 命令构建项目');
        process.exit(1);
    }

    // 读取配置文件
    const configContent = fs.readFileSync(distConfigPath, 'utf-8');
    const config = JSON.parse(configContent);

    const appId = config.appid;
    const appDesc = config.description;



    console.log(`正在部署 ${appId}`)
    config.appid = appId
    config.description = appDesc
    fs.writeFileSync(distConfigPath, JSON.stringify(config, null, 2), 'utf-8');
    console.log('已成功修改 dist/project.config.json 配置');
    const privateKeyPath = `./uploadCerts/private.${appId}.key`;
    if (!fs.existsSync(privateKeyPath)) {
        console.error(`错误: ${privateKeyPath} 不存在，请先生成私钥`);
        process.exit(1);
    }

    try {
        const project = new miniprogramCi.Project({
            appid: appId,
            type: 'miniProgram',
            projectPath: './dist',
            privateKeyPath,
            ignores: ['node_modules/**/*'],
        })
        const uploadResult = await miniprogramCi.upload({
            project,
            version,
            desc: '自动部署',
            robot: 1,
            setting: config.setting,
            onProgressUpdate: console.log,
        })
        console.log(uploadResult)
    } catch (error) {
        console.error('部署失败:', error);
    }

    const nextVersion = version.split('.').map((item, index) => {
        if (index === version.split('.').length - 1) {
            return Number(item) + 1;
        }
        return item;
    }).join('.');
    fs.writeFileSync('./version.txt', nextVersion);
    console.log(`已成功更新版本号，新版本号为 ${nextVersion}`);
}

main()