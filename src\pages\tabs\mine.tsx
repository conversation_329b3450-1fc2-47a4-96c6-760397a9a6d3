import { BasePage } from '@/components/BasePage'
import { MyNavbar } from '@/components/MyNavbar'
import { Tabbar } from '@/components/Tabbar'
import { $userInfo } from '@/store/appStore'
import { $unreadMessageCount } from '@/utils/messageWebSocket'
import { View, Text, Image } from '@tarojs/components'
import { useStore } from '@nanostores/solid'
import { Show } from 'solid-js'
import { Utils } from '@/utils/utils'
import { useDidShow } from '@tarojs/taro'
import { Iconfont } from '@/components/Iconfont'
import dayjs from 'dayjs'
import { MyButton } from '@/components/MyButton'
import { apiMessageUnreadCount } from '@/apis/apis'

export default function MinePage() {

  const userInfo = useStore($userInfo)
  const unreadMessageCount = useStore($unreadMessageCount)

  useDidShow(async () => {
    Utils.backPush()

    // 刷新未读消息数量
    try {
      const count = await apiMessageUnreadCount()
      $unreadMessageCount.set(count)
    } catch (error) {
      console.error('获取未读消息数量失败:', error)
    }
  })

  const vipText = () => {
    if (!userInfo()) return ''
    if (!userInfo()?.vipEndAt) return '普通用户'
    if (userInfo()?.vipEndAt && dayjs(userInfo()?.vipEndAt).isBefore(dayjs())) return '会员已过期'
    if (userInfo()?.vipEndAt && dayjs(userInfo()?.vipEndAt).isAfter(dayjs())) return '会员至：' + Utils.formatDay(userInfo()?.vipEndAt!)
  }

  //   const background = createMemo(() => {
  //     const svgStr = `<svg width="1" height="400" xmlns="http://www.w3.org/2000/svg">
  //   <defs>
  //     <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
  //       <stop offset="0%" style="stop-color:#22c55e;stop-opacity:0.3" />
  //       <stop offset="100%" style="stop-color:#22c55e;stop-opacity:0" />
  //     </linearGradient>
  //   </defs>
  //   <rect width="1" height="400" fill="url(#gradient)" />
  // </svg>`
  //     const svgUrl = `data:image/svg+xml;charset=utf8,${encodeURIComponent(svgStr)}`
  //     return `url("${svgUrl}") repeat-x`
  //   })

  return (
    // <BasePage background="url(https://fire-flies.oss-cn-chengdu.aliyuncs.com/images/flower-price-mini/mine-bg.png) repeat-x">
    <BasePage className='greenBg'>
      <MyNavbar background='transparent' />
      <View class="p-4 box-border flex flex-col gap-4 relative">
        <View class="flex items-center gap-4 mb-4">
          <View class="w-12 h-12 rounded-full overflow-hidden shadow bg-#11DB7D">
            <Image src={Utils.imageSrc(userInfo()?.avatar)} class="w-full h-full" mode="aspectFill" />
          </View>
          <View class="flex flex-col gap-1">
            <View class="flex items-center gap-2">
              <Text class="text-xl opacity-80">{userInfo()?.nickname || '用户'}</Text>
              {/* <View class="text-xs bg-yellow-200 px-2 py-1 rounded">绑定手机</View> */}
            </View>
            <View class="text-xs flex items-center gap-2">
              <Text class="opacity-60">{vipText()}</Text>
              {/* <View class="bg-zinc-500 w-1rpx h-2"></View>
                <Text class="opacity-60">手机待绑定</Text> */}
            </View>
          </View>
          <View class=" flex items-center gap-6 ml-auto">
            <View class="flex flex-col gap-1 items-center relative" onClick={() => Utils.routeTo('/pages/mine/message-list')}>
              <View class="relative">
                <Iconfont name="bell" size={48} />
                <Show when={unreadMessageCount() > 0}>
                  <View class="absolute -top-1 -right-1 bg-red-500 text-white text-xs min-w-4 h-4 rounded-full flex items-center justify-center px-1">
                    {unreadMessageCount() > 99 ? '99+' : unreadMessageCount()}
                  </View>
                </Show>
              </View>
              <Text class="text-xs opacity-60">消息</Text>
            </View>
            <View class="flex flex-col gap-1 items-center" onClick={() => Utils.routeTo('/pages/mine/settings')}>
              <Iconfont name="setting" size={48} />
              <Text class="text-xs opacity-60">设置</Text>
            </View>
          </View>

        </View>
        {/* 积分余额卡片 */}

        <View class='grid grid-cols-2 gap-4'>
          <View class="rounded-lg bg-white dark:bg-stone-800 p-4 shadow flex flex-col gap-2 overflow-hidden relative">
            <View class="flex justify-between items-center">
              <View class="text-sm opacity-80 mb-1">积分</View>
              <MyButton
                type="primary"
                size="mini"
                shape='round'
                onClick={() => Utils.routeTo('/pages/mine/points-list')}
              >
                明细
              </MyButton>
            </View>

            <View class="text-xl font-bold">{userInfo()?.points || 0}</View>
            <View class='absolute right-2 -bottom-4 opacity-10 text-green-500'>
              <Iconfont name="wallet" size={120} />
            </View>

          </View>
          <View class="rounded-lg bg-white dark:bg-stone-800 p-4 shadow flex flex-col gap-2 overflow-hidden relative">
            <View class="flex justify-between items-center">
              <View class="text-sm opacity-80 mb-1">会员</View>
              <MyButton
                type="primary"
                size="mini"
                shape='round'
                onClick={() => Utils.routeTo('/pages/mine/vip-purchase')}
              >
                {userInfo()?.vipEndAt && dayjs(userInfo()?.vipEndAt).isAfter(dayjs()) ? '续费' : '开通'}
              </MyButton>
            </View>
            <Show when={userInfo()?.isVip} fallback={
              <View class="text-sm opacity-60">开会员享特权</View>
            }>
              <View>
                <Text class='text-xs opacity-60'>剩</Text>
                <Text class="text-xl font-bold mx-1">{dayjs(userInfo()?.vipEndAt).diff(dayjs(), 'day')}</Text>
                <Text class='text-xs opacity-60'>天</Text>
              </View>
            </Show>

            <View class='absolute right-2 -bottom-4 opacity-10 text-green-500'>
              <Iconfont name="vip" size={120} />
            </View>

          </View>

        </View>


        <View class="rounded-lg bg-white dark:bg-stone-800 grid grid-cols-4 gap-x-4 gap-y-6 p-4 box-border shadow">
          <View class="flex flex-col items-center justify-center gap-2" onClick={() => Utils.routeTo('/pages/tabs/fav')}>
            <Iconfont name="star" size={54} />
            <Text class="text-xs opacity-80">收藏</Text>
          </View>

          <View class="flex flex-col items-center justify-center gap-2" onClick={() => Utils.routeTo('/pages/mine/vip-purchase')}>
            <Iconfont name="vip" size={54} />
            <Text class="text-xs opacity-80">开通会员</Text>
          </View>
          <View class="flex flex-col items-center justify-center gap-2" onClick={() => Utils.routeTo('/pages/mine/vip-order-list')}>
            <Iconfont name="order" size={54} />
            <Text class="text-xs opacity-80">会员订单</Text>
          </View>
          <View class="flex flex-col items-center justify-center gap-2" onClick={() => Utils.routeTo('/pages/mine/points-list')}>
            <Iconfont name="wallet" size={54} />
            <Text class="text-xs opacity-80">会员积分</Text>
          </View>
          <View class="flex flex-col items-center justify-center gap-2 relative" onClick={() => Utils.routeTo('/pages/mine/message-list')}>
            <View class="relative">
              <Iconfont name="bell" size={54} />
              <Show when={unreadMessageCount() > 0}>
                <View class="absolute -top-1 -right-1 bg-red-500 text-white text-xs min-w-4 h-4 rounded-full flex items-center justify-center px-1">
                  {unreadMessageCount() > 99 ? '99+' : unreadMessageCount()}
                </View>
              </Show>
            </View>
            <Text class="text-xs opacity-80">消息中心</Text>
          </View>
          <View class="flex flex-col items-center justify-center gap-2" onClick={() => Utils.routeTo('/pages/mine/settings')}>
            <Iconfont name="setting" size={54} />
            <Text class="text-xs opacity-80">我的设置</Text>
          </View>
          <View class="flex flex-col items-center justify-center gap-2" onClick={() => Utils.routeTo('/pages/mine/feedback-list')}>
            <Iconfont name="feedback" size={54} />
            <Text class="text-xs opacity-80">反馈建议</Text>
          </View>
        </View>
      </View>

      <Tabbar />
    </BasePage>
  )
}
