import { createSignal, createContext, Accessor } from 'solid-js'

export interface TMyFormRule {
  /**  将字段值转换成目标值后进行校验 */
  transform?: (value: any) => any
  /** 仅在 type 为 array 类型时有效，用于指定数组元素的校验规则 */
  defaultField?: TMyFormRule
  /** 仅在 type 为 object 类型时有效，用于指定子元素的校验规则 */
  fields?: Record<string, TMyFormRule>
  /** 仅在 type 为 string、number 类型时有效，用于指定可选值 */
  enum?: (string | number)[]
  /** string 类型时为字符串长度；number 类型时为确定数字； array 类型时为数组长度 */
  len?: number
  /** string 类型时为字符串最大长度；number 类型时为最大值； array 类型时为数组最大长度 */
  max?: number
  /** string 类型时为字符串最小长度；number 类型时为最小值； array 类型时为数组最小长度 */
  min?: number
  /** 是否为必选字段 */
  required?: boolean
  /** 错误信息，不设置时会通过模板自动生成 */
  message?: string
  /** 仅在 type 为 string 类型时有效，用于指定正则表达式匹配 */
  pattern?: RegExp
  /** 类型 */
  type?: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'enum' | 'mobile' | 'url' | 'email'

  /** 自定义校验，接收 Promise 作为返回值 */
  validator?: (rule: TMyFormRule, value: any) => Promise<void>

  /**  仅在 type 为 string 类型时有效, 是否包含空白字符 */
  whitespace?: boolean
  /** 是否在修改时进行校验，默认为true */
  validateOnChange?: boolean
  /** 仅警告，不阻塞表单提交 */
  warningOnly?: boolean
}

/** 表单实例 */
export interface TMyFormInstance {
  setFieldsValue: (values: Record<string, any>) => void
  validateFields: () => Promise<Record<string, any>>
  submit: () => void
  submitting: Accessor<boolean>
  errors: Accessor<Record<string, string[]>>
  values: Accessor<Record<string, any>>
  _registerItem: (item: TMyFormItemMeta) => void
  _unregisterItem: (name: string) => void
  _setOnSubmit: (fn?: ((values: Record<string, any>) => void)) => void
}

interface TMyFormItemMeta {
  name: string
  rules?: TMyFormRule[]
}

export const FormContext = createContext<any>(null)
export const FormItemContext = createContext<any>(null)

export const useMyForm = (): TMyFormInstance => {
  const [values, setValues] = createSignal<Record<string, any>>({})
  const [errors, setErrors] = createSignal<Record<string, string[]>>({})
  const [submitting, setSubmitting] = createSignal(false)
  const [items, setItems] = createSignal<TMyFormItemMeta[]>([])

  const registerItem = (item: TMyFormItemMeta) => {
    setItems(prev => [...prev, item])
  }

  const unregisterItem = (name: string) => {
    setItems(prev => prev.filter(item => item.name !== name))
  }

  const handleRule = (rule: TMyFormRule, value: any, errors: string[]) => {
    // 应用transform转换
    if (rule.transform) {
      value = rule.transform(value)
    }

    // 空值检查
    const isEmpty = value === undefined || value === null || value === ''
    if (isEmpty) {
      if (rule.required) {
        errors.push(rule.message || '此字段是必填的')
      }
      return
    }

    // 类型检查
    if (rule.type) {
      const actualType = typeof value
      if (rule.type === 'number' && actualType !== 'number') {
        errors.push(rule.message || '请输入数字')
      } else if (rule.type === 'string' && actualType !== 'string') {
        errors.push(rule.message || '请输入文本')
      } else if (rule.type === 'boolean' && actualType !== 'boolean') {
        errors.push(rule.message || '请输入布尔值')
      } else if (rule.type === 'array' && !Array.isArray(value)) {
        errors.push(rule.message || '请输入数组')
      } else if (rule.type === 'object' && (actualType !== 'object' || Array.isArray(value))) {
        errors.push(rule.message || '请输入对象')
      } else if (rule.type === 'enum' && !rule.enum?.includes(value)) {
        errors.push(rule.message || '请选择有效的选项')
      } else if (rule.type === 'mobile' && !/^1[3-9]\d{9}$/.test(value)) {
        errors.push(rule.message || '请输入有效的手机号')
      } else if (rule.type === 'email' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        errors.push(rule.message || '请输入有效的邮箱地址')
      } else if (rule.type === 'url' && !/^https?:\/\//.test(value)) {
        errors.push(rule.message || '请输入有效的URL')
      }
    } else {
      rule.type = Array.isArray(value) ? 'array' : typeof value as any
    }

    // 空白字符检查
    if (rule.whitespace && typeof value === 'string' && !value.trim()) {
      errors.push(rule.message || '不能只包含空白字符')
    }

    // 长度检查
    if (rule.len !== undefined) {
      const length = Array.isArray(value) ? value.length : String(value).length
      if (length !== rule.len) {
        errors.push(rule.message || `长度必须为${rule.len}`)
      }
    }

    

    // 最大/最小值检查
    if (rule.max !== undefined && ['number', 'string', 'array'].includes(rule.type || '')) {
      const type = rule.type as 'number' | 'string' | 'array'
      let size = 0;
      switch (type) {
        case 'number':
          size = Number(value)
          break
        case 'string':
          size = String(value).length
          break
        case 'array':
          size = value.length
          break
      }
      if (size > rule.max) {
        errors.push(rule.message || {
          'number': `不能大于${rule.max}`,
          'string': `长度不能大于${rule.max}`,
          'array': `个数不能大于${rule.max}`,
        }[type])
      }
    }
    if (rule.min !== undefined) {
      const type = rule.type as 'number' | 'string' | 'array'
      let size = 0;
      switch (type) {
        case 'number':
          size = Number(value)
          break
        case 'string':
          size = String(value).length
          break
        case 'array':
          size = value.length
          break
      }
      if (size < rule.min) {
        errors.push(rule.message || {
          'number': `不能小于${rule.min}`,
          'string': `长度不能小于${rule.min}`,
          'array': `个数不能小于${rule.min}`,
        }[type])
      }
    }

    // 正则表达式检查
    if (rule.pattern && !rule.pattern.test(value)) {
      errors.push(rule.message || '格式不正确')
    }

    // 自定义验证器
    if (rule.validator) {
      rule.validator(rule, value).catch(error => {
        errors.push(error.message || rule.message || '验证失败')
      })
    }
  }

  const validateField = (name: string, value: any) => {
    const item = items().find(item => item.name === name)
    if (!item || !item.rules) return

    const itemErrors: string[] = []
    item.rules.forEach(rule => {
      if (rule.validateOnChange === false) return
      handleRule(rule, value, itemErrors)
    })

    setErrors(prev => {
      const newErrors = { ...prev }
      if (itemErrors.length > 0) {
        newErrors[name] = itemErrors
      } else {
        delete newErrors[name]
      }
      return newErrors
    })

    return itemErrors.length === 0
  }

  const validateFields = async () => {
    const newErrors: Record<string, string[]> = {}
    const currentValues = values()
    items().forEach(item => {
      const value = currentValues[item.name]
      const itemErrors: string[] = []

      item.rules?.forEach(rule => {
        handleRule(rule, value, itemErrors)
      })

      if (itemErrors.length > 0) {
        newErrors[item.name] = itemErrors
      }
    })

    setErrors(newErrors)

    if (Object.keys(newErrors).length > 0) {
      console.error('表单验证失败', newErrors)
      throw new Error('表单验证失败')
    }

    return currentValues
  }

  let onSubmit: ((values: Record<string, any>) => void) | undefined = undefined


  return {
    submitting,
    errors,
    values,
    // getFieldsValue: () => values(),
    setFieldsValue: (newValues) => {
      setValues(prev => ({ ...prev, ...newValues }))
      Object.entries(newValues).forEach(([name, value]) => {
        validateField(name, value)
      })
    },
    validateFields,
    submit: async () => {
      try {
        setSubmitting(true)
        const validValues = await validateFields()
        onSubmit?.(validValues)
      } catch (error) {
        console.error(error)
      } finally {
        setSubmitting(false)
      }
    },

    _registerItem: registerItem,
    _unregisterItem: unregisterItem,
    _setOnSubmit: (fn: typeof onSubmit) => { onSubmit = fn },
  }
}