import { createEffect, onCleanup, JSX, useContext, createMemo, mergeProps } from 'solid-js'
import { View, Text } from '@tarojs/components'
import { FormContext, FormItemContext, TMyFormRule, useMyForm } from './useMyForm'

export interface TMyFormItemProps {
  /** 表单实例 */
  form?: ReturnType<typeof useMyForm>
  /** 标签 */
  label?: string
  /** 标签宽度 */
  labelWidth?: number
  /** 字段名 */
  name: string
  /** 表单项规则 */
  rules?: TMyFormRule[]
  /** 是否显示必填星号 */
  requiredSgin?: boolean
  /** 子元素 */
  children?: JSX.Element
  /** 是否显示错误提示 */
  showError?: boolean
}

export function MyFormItem(_props: TMyFormItemProps) {

  const props = mergeProps({
    showError: true,
  }, _props)

  const form = props.form || useContext(FormContext)

  if (!form) {
    throw new Error('MyFormItem must be used within MyForm')
  }

  createEffect(() => {
    if (props.name) {
      form._registerItem({
        name: props.name,
        rules: props.rules
      })
    }
  })

  onCleanup(() => {
    if (props.name) {
      form._unregisterItem(props.name)
    }
  })

  const value = createMemo(() => form.values()[props.name])
  

  const onChange = (newValue: any) => {
    form.setFieldsValue({
      [props.name]: newValue
    })
  }

  return (
    <View class="mb-4">
      <View class="text-sm mb-2">
        {props.requiredSgin && <Text class="my-form-item-required">*</Text>}
        {props.label}
      </View>
      <View class="my-form-item-control">
        <FormItemContext.Provider value={{ value, onChange }}>
          {props.children}
        </FormItemContext.Provider>
        {form.errors()[props.name]?.map?.((error: string) => (
          <Text class="text-red-500 my-form-item-error">{error}</Text>
        ))}
      </View>
    </View>
  )
}