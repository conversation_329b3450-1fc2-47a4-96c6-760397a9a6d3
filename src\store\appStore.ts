import { apiAddFavorite, apiFavoriteFlowerIds, apiRemoveFavorite, apiShareQrcode, apiWxLogin } from '@/apis/apis';
import { HttpUtils } from '@/utils/http';
import { Utils } from '@/utils/utils';
import Taro from '@tarojs/taro';
import waitUntil from 'async-wait-until';
import { atom, computed } from 'nanostores';

export const $isDarkTheme = atom<boolean>(Taro.getAppBaseInfo().theme === 'dark')

export const $themeVars = computed($isDarkTheme, (isDarkTheme) => ({
  bgColor: isDarkTheme ? '#222222' : '#f4f4f4',
  textColor: isDarkTheme ? '#ffffff' : '#000000',
}))

export const $currentBackQuery = atom<{ path: string, query: Record<string, string> } | undefined>(undefined)

export const $isLogin = atom<boolean>(!!Taro.getStorageSync('token'))
export const $userInfo = atom<TClientUser | undefined>(undefined)

export const $favoriteFlowerIdsSet = atom<Set<string>>(new Set())

export const $wxShareQrCode = atom<string | undefined>(undefined)

export const toggleFavoriteFlower = async (flowerId: string) => {
  if ($favoriteFlowerIdsSet.get().has(flowerId)) {
    $favoriteFlowerIdsSet.set(new Set([...$favoriteFlowerIdsSet.get().values()].filter(id => id !== flowerId)))
    try {
      await apiRemoveFavorite(flowerId)
    } catch (error) {
      $favoriteFlowerIdsSet.set(new Set([...$favoriteFlowerIdsSet.get().values(), flowerId]))
    }
  } else {
    $favoriteFlowerIdsSet.set(new Set([...$favoriteFlowerIdsSet.get().values(), flowerId]))
    try {
      await apiAddFavorite(flowerId)
    } catch (error) {
      $favoriteFlowerIdsSet.set(new Set([...$favoriteFlowerIdsSet.get().values()].filter(id => id !== flowerId)))
    }
  }
}

export const loadFavoriteFlowerIds = async () => {
  const res = await apiFavoriteFlowerIds()
  $favoriteFlowerIdsSet.set(new Set(res))
}

export async function login(fromUserId?: string) {
  console.log('login fromUserId', fromUserId)
  const { code } = await Taro.login()
  const res = await apiWxLogin({
    code,
    fromUserId,
  })
  $isLogin.set(true)
  $userInfo.set(res.user)
  HttpUtils.setToken(res.token, res.expiredAt)
  if (res?.user?.roles) {
    Utils.cacheSet('DEV_ROLE', (res?.user?.roles || []).includes('DEV'))
  }
  if (res?.user?.wxShareQrCode) {
    $wxShareQrCode.set(res.user.wxShareQrCode)
  } else {
    apiShareQrcode().then(res => {
      $wxShareQrCode.set(res)
    })
  }
}

export function logout() {
  $isLogin.set(false)
  $userInfo.set(undefined)
  HttpUtils.setToken('')
}

export function updateUserInfo(userInfo: Partial<TClientUser>) {
  const currentUser = $userInfo.get()
  if (currentUser) {
    $userInfo.set({ ...currentUser, ...userInfo })
  }
}

export function waitLogin() {
  return waitUntil(() => $isLogin.get(), 10000)
}


Taro.onThemeChange((res) => {
  $isDarkTheme.set(res.theme === 'dark')
})