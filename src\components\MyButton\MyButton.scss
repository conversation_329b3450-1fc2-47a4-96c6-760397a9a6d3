

// 主色变量
$primary-color: #22c55e;
$primary-light: rgba($primary-color, 0.1);

// 文字颜色
$text-color: #333;
$text-color-light: #666;
$text-color-lighter: #999;
$white: #fff;

// 边框和背景
$border-color: #eee;
$bg-color: #f5f5f5;
$bg-color-hover: #f0f0f0;

// 状态色
$success-color: #10b981;
$warning-color: #f59e0b;
$danger-color: #ef4444;
$info-color: #3b82f6;
$default-color: #e5e7eb;

// 尺寸
$font-size-base: 32rpx;
$font-size-lg: 36rpx;
$font-size-sm: 28rpx;
$font-size-xs: 24rpx;

$height-base: 80rpx;
$height-lg: 96rpx;
$height-sm: 64rpx;
$height-xs: 48rpx;

$padding-base: 0 24rpx;
$padding-lg: 0 32rpx;
$padding-sm: 0 24rpx;
$padding-xs: 0 16rpx;

$border-radius: 8rpx;
$border-radius-round: 80rpx;
$border-radius-square: 0;

// 基础按钮样式
.my-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  font-size: $font-size-base;
  line-height: 1.2;
  text-align: center;
  text-decoration: none;
  border-radius: $border-radius;
  transition: all 0.3s;
  padding: $padding-base;
  height: $height-base;
  color: $text-color;
  background-color: $default-color;
  border: 2rpx solid transparent;
  white-space: nowrap;
  user-select: none;
  touch-action: manipulation;
  vertical-align: middle;
  -webkit-tap-highlight-color: transparent;
  -webkit-tap-highlight-color: transparent;

  &--block {
    display: flex;
    width: 100%;
  }

  // 尺寸
  &--large {
    height: $height-lg;
    font-size: $font-size-lg;
    padding: $padding-lg;
  }

  &--normal {
    height: $height-base;
    font-size: $font-size-base;
    padding: $padding-base;
  }

  &--small {
    height: $height-sm;
    font-size: $font-size-sm;
    padding: $padding-sm;
  }

  &--mini {
    height: $height-xs;
    font-size: $font-size-xs;
    padding: $padding-xs;
  }

  // 形状
  &--round {
    border-radius: $border-radius-round;
  }

  &--square {
    border-radius: $border-radius-square;
  }

  // 变体
  &--solid {
    // 实心按钮
    &.my-button--primary {
      color: $white;
      background-color: $primary-color;
      border-color: $primary-color;

      &:active {
        opacity: 0.8;
      }
    }

    &.my-button--success {
      color: $white;
      background-color: $success-color;
      border-color: $success-color;

      &:active {
        opacity: 0.8;
      }
    }

    &.my-button--warning {
      color: $white;
      background-color: $warning-color;
      border-color: $warning-color;

      &:active {
        opacity: 0.8;
      }
    }

    &.my-button--danger {
      color: $white;
      background-color: $danger-color;
      border-color: $danger-color;

      &:active {
        opacity: 0.8;
      }
    }

    &.my-button--info {
      color: $white;
      background-color: $info-color;
      border-color: $info-color;

      &:active {
        opacity: 0.8;
      }
    }

    &.my-button--default {
      color: $text-color;
      background-color: $default-color;
      border-color: $default-color;

      &:active {
        opacity: 0.8;
      }
    }
  }

  &--outline {
    // 描边按钮
    background-color: transparent;

    &.my-button--primary {
      color: $primary-color;
      border-color: $primary-color;

      &:active {
        opacity: 0.8;
      }
    }

    &.my-button--success {
      color: $success-color;
      border-color: $success-color;

      &:active {
        opacity: 0.8;
      }
    }

    &.my-button--warning {
      color: $warning-color;
      border-color: $warning-color;

      &:active {
        opacity: 0.8;
      }
    }

    &.my-button--danger {
      color: $danger-color;
      border-color: $danger-color;

      &:active {
        opacity: 0.8;
      }
    }

    &.my-button--info {
      color: $info-color;
      border-color: $info-color;

      &:active {
        opacity: 0.8;
      }
    }

    &.my-button--default {
      color: $text-color-light;
      border-color: $border-color;

      &:active {
        color: $text-color;
        background-color: $bg-color-hover;
      }
    }
  }

  &--text {
    // 文字按钮
    background-color: transparent;
    border-color: transparent;
    padding-left: 16rpx;
    padding-right: 16rpx;

    &.my-button--primary {
      color: $primary-color;

      &:active {
        background-color: $primary-light;
      }
    }

    &.my-button--success {
      color: $success-color;

      &:active {
        background-color: rgba($success-color, 0.1);
      }
    }

    &.my-button--warning {
      color: $warning-color;

      &:active {
        background-color: rgba($warning-color, 0.1);
      }
    }

    &.my-button--danger {
      color: $danger-color;

      &:active {
        background-color: rgba($danger-color, 0.1);
      }
    }

    &.my-button--info {
      color: $info-color;

      &:active {
        background-color: rgba($info-color, 0.1);
      }
    }

    &.my-button--default {
      color: $text-color-light;

      &:active {
        background-color: $bg-color-hover;
      }
    }
  }

  // 禁用状态
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  // 加载状态
  &--loading {
    cursor: default;
    opacity: 0.8;
  }

  // 内部元素
  &__icon {
    display: inline-flex;
    align-items: center;
    margin-right: 8rpx;
    line-height: 1;
    font-size: inherit;

    &-svg {
      display: inline-block;
      animation: rotating 1s linear infinite;
    }
  }

  &__text {
    display: inline-block;
    vertical-align: middle;
  }

  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    &-icon {
      margin-right: 8rpx;
      animation: rotating 1s linear infinite;
    }

    &-text {
      margin-left: 8rpx;
    }
  }
}

// 旋转动画
@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 修复小程序按钮样式
button {
  &[disabled] {
    background-color: transparent !important;
    color: inherit !important;
    opacity: 1;
  }

  &::after {
    display: none;
  }

  &.my-button--disabled,
  &[disabled] {
    opacity: 0.5 !important;
  }
}