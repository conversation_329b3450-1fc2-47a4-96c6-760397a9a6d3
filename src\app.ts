import { ParentProps } from "solid-js";
import { useLaunch, useDidShow, useDidHide } from "@tarojs/taro";
import { loadFavoriteFlowerIds, login } from "@/store/appStore";
import { messageWebSocket } from "@/utils/messageWebSocket";
import "uno.css";
import "./app.css";

function App({ children }: ParentProps) {
  useLaunch(async (options) => {
    await login(options?.query?.fromUserId);
    await loadFavoriteFlowerIds();

    // 登录成功后连接WebSocket
    try {
      await messageWebSocket.connect();
    } catch (error) {
      console.error('WebSocket连接失败:', error);
    }
  });

  // 应用进入前台时重连WebSocket
  useDidShow(() => {
    if (!messageWebSocket.isConnected()) {
      messageWebSocket.resetReconnectAttempts();
      messageWebSocket.connect();
    }
  });

  // 应用进入后台时断开WebSocket
  useDidHide(() => {
    messageWebSocket.disconnect();
  });

  // children 是将要会渲染的页面
  return children;
}

export default App;
