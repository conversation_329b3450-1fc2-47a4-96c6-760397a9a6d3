import { View } from "@tarojs/components"
import { JSXElement, mergeProps, For, createMemo } from "solid-js"

export interface TMyTabsProps {
  activeKey: string
  onChange: (key: string) => void
  items: { key: string, title: JSXElement }[]
}

export const MyTabs = (_props: TMyTabsProps) => {
  const props = mergeProps({}, _props)

  const activeIndex = createMemo(() => props.items.findIndex(item => item.key === props.activeKey))

  const noneBorderIndexes = createMemo(() => [
    0,
    activeIndex(),
    activeIndex() + 1
  ])


  return (
    <View
      class="box-border rounded-lg h-66rpx bg-gray-200 dark:bg-stone-900 p-1 w-full"
    >
      <View class="relative w-full h-full flex items-center">
        <View class="absolute z-0 bg-white dark:bg-stone-500 shadow h-full transition-all duration-100 ease-in-out rounded-md"
          style={{ width: `${Math.round(100 / props.items.length)}%`, left: `${Math.round(100 / props.items.length * activeIndex())}%` }} />
        <For each={props.items}>
          {(item, index) => (
            <View
              class={
                `relative z-1 flex-1 flex justify-center items-center cursor-pointer transition-all duration-200 rounded-md text-base h-full text-sm user-select-none
              ${activeIndex() === index() ? 'text-black dark:text-white font-bold'
                  : 'text-stone-700 dark:text-gray-300'}
              ${!noneBorderIndexes().includes(index()) ? "before:content-[''] before:w-1px before:h-32rpx before:absolute before:top-1/2 before:transform before:-translate-y-1/2 before:left-0 before:bg-gray-300 dark:before:bg-stone-600" : ''}
              `
              }
              onClick={() => props.onChange(item.key)}
            >
              {item.title}
            </View>
          )}
        </For>
      </View>
    </View>
  )
}