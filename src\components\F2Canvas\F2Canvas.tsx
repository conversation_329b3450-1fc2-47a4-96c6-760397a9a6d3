import { Utils } from "@/utils/utils"
import { Canvas } from "@tarojs/components"
import Taro from "@tarojs/taro"
import F2 from './f2.js'
import { createMemo, createSignal, mergeProps, onMount, createEffect, on } from "solid-js"
import { CanvasTouchEvent } from "@tarojs/components/types/common"

export interface TF2CanvasProps {
  id?: string
  width?: number
  height?: number
  onInit?: (F2: any, config: any) => any
  data?: any
  shouldUpdate?: boolean
}

let INSTANCE_COUNTER = 0

function wrapEvent(e: CanvasTouchEvent) {
  if (!e.preventDefault) {
    e.preventDefault = function () { }
  }
  return e
}

export const F2Canvas = (_props: TF2CanvasProps) => {
  const props = mergeProps({
    width: 750,
    height: 500,
    shouldUpdate: true,
    data: null
  }, _props)

  const canvasId = createMemo(() => {
    return props.id || 'f2-canvas-' + (++INSTANCE_COUNTER)
  })

  const [canvasEl, setCanvasEl] = createSignal<any>(null)
  const [chart, setChart] = createSignal<any>(null)

  const onTouchStart = (e?: CanvasTouchEvent) => {
    if (canvasEl() && e) {
      canvasEl()?.dispatchEvent('touchstart', wrapEvent(e))
    }
  }

  const onTouchMove = (e?: CanvasTouchEvent) => {
    if (canvasEl() && e) {
      canvasEl()?.dispatchEvent('touchmove', wrapEvent(e))
    }
  }

  const onTouchEnd = (e?: CanvasTouchEvent) => {
    if (canvasEl() && e) {
      canvasEl()?.dispatchEvent('touchend', wrapEvent(e))
    }
  }

  const drawCanvas = async () => {
    let times = 0;
    const maxTryTimes = 3;
    
    const initCanvas = async () => {
      times++;
      const query = Taro.createSelectorQuery()
      query.select('#' + canvasId())
        .fields({
          node: true,
          size: true
        }).exec(async res => {
          if (!res[0]) {
            if (times <= maxTryTimes) {
              Taro.nextTick(() => {
                initCanvas()
              })
            }
            return
          }
          const { node } = res[0]
          const context = node.getContext('2d')

          const width = Utils.rpx2px(props.width)
          const height = Utils.rpx2px(props.height)

          // 高清设置
          node.width = width * Utils.windowInfo.pixelRatio
          node.height = height * Utils.windowInfo.pixelRatio

          const config = { context, width, height, pixelRatio: Utils.windowInfo.pixelRatio }
          const chart = props.onInit?.(F2, config)
          if (chart) {
            setChart(chart)
            setCanvasEl(chart.get('el'))
          }
        })
    }
    
    // 如果图表已经存在，先销毁它
    if (chart()) {
      chart().destroy();
      setChart(null);
    }
    
    // 初始化新的图表
    await initCanvas();
  }
  
  onMount(() => {
    Taro.nextTick(() => {
      setTimeout(drawCanvas, 200)
      drawCanvas()
    })
  })
  
  // 监听数据变化，重新渲染图表
  createEffect(on(() => props.data, () => {
    if (props.shouldUpdate && props.data) {
      drawCanvas();
    }
  }, { defer: true }))

  return (
    <Canvas class="F2Canvas"
      canvasId={canvasId()}
      id={canvasId()}
      type='2d'
      style={{
        width: props.width + 'rpx',
        height: props.height + 'rpx'
      }}
      onTouchStart={onTouchStart}
      onTouchMove={onTouchMove}
      onTouchEnd={onTouchEnd}
    >
    </Canvas>
  )
}