import { View, ScrollView } from "@tarojs/components";
import { createSignal, onMount, For, Show } from "solid-js";
import { BasePage } from "@/components/BasePage";
import { MyNavbar } from "@/components/MyNavbar";
import { Utils } from "@/utils/utils";
import { apiPointsRecordMyPageList } from "@/apis/apis";
import { SafeBottom } from "@/components/SafeBottom";
import { Empty } from "@/components/Empty";
import { usePullDownRefresh, useReachBottom, useDidShow } from "@tarojs/taro";
import Taro from "@tarojs/taro";
import dayjs from "dayjs";
import { AgreeDicts } from "@/utils/agreeDict";
import { $isDarkTheme, $userInfo } from "@/store/appStore";
import { useStore } from "@nanostores/solid";


export default function PointsListPage() {
  const [records, setRecords] = createSignal<TClientUserPointsRecord[]>([]);
  const [loading, setLoading] = createSignal(false);
  const [hasMore, setHasMore] = createSignal(true);
  const [pageIndex, setPageIndex] = createSignal(0);
  const isDarkTheme = useStore($isDarkTheme);

  const userInfo = useStore($userInfo);

  const pageSize = 20;

  // 加载积分记录
  const loadRecords = async (refresh = false) => {
    if (loading()) return;

    try {
      setLoading(true);
      const currentPage = refresh ? 0 : pageIndex();

      // 构建查询参数
      const query: any = {
        pageIndex: currentPage,
        pageSize: pageSize
      };

      const data = await apiPointsRecordMyPageList(query);

      if (refresh) {
        setRecords(data.rows);
        setPageIndex(1);
      } else {
        setRecords(prev => [...prev, ...data.rows]);
      }

      setHasMore(currentPage + 1 < data.totalPageCount);
      setPageIndex(currentPage + 1);
    } catch (error) {
      Utils.toast('加载失败');
    } finally {
      setLoading(false);
    }
  };

 
  // 格式化积分变化
  const formatPointsChange = (change: number) => {
    return change > 0 ? `+${change}` : change.toString();
  };

  // 获取记录类型颜色
  const getRecordTypeColor = (recordType: string) => {
    const typeInfo = AgreeDicts.ClientUserPointsRecord_type.valueDataMap[recordType];
    return typeInfo?.data?.color || 'gray';
  };

  // 获取记录类型标签
  const getRecordTypeLabel = (recordType: string) => {
    return AgreeDicts.ClientUserPointsRecord_type.valueLabelMap[recordType] || recordType;
  };

  onMount(() => {
    loadRecords(true);
  });

  useDidShow(() => {
    loadRecords(true);
  });

  usePullDownRefresh(() => {
    loadRecords(true);
    Taro.stopPullDownRefresh();
  });

  useReachBottom(() => {
    if (hasMore() && !loading()) {
      loadRecords();
    }
  });

  return (
    <BasePage>
      <MyNavbar title="我的积分" />
      <View class="p-4 flex flex-col gap-4">
        <View class="bg-white dark:bg-stone-900 rounded-2 p-4">
          <View class="flex items-center gap-4">
            <View class="text-64rpx font-bold">{userInfo()?.points || 0}</View>
            <View>
              <View class="text-28rpx opacity-80 mb-2">当前积分余额</View>
              <View class="text-24rpx opacity-60 mt-2">积分可用于抵扣消费</View>
            </View>

          </View>
        </View>

        <ScrollView
          scrollY
          style={{ height: `${Utils.pageSize.pageHeight - 320}px` }}
          onScrollToLower={() => {
            if (hasMore() && !loading()) {
              loadRecords();
            }
          }}
          lowerThreshold={50}
        >
          <Show when={records().length > 0} fallback={
            <Show when={!loading()}>
              <Empty description="暂无积分记录" />
            </Show>
          }>
            <View class="flex flex-col bg-white dark:bg-stone-900 rounded-2 px-4">
              <For each={records()}>
                {(record, index) => (
                  <View class="flex items-start justify-between py-4" style={{ 'border-top': index() === 0 ? 'none' : (isDarkTheme() ? '1rpx solid #333' : '1rpx solid #eee') }}>
                      <View class="flex-1">
                        <View class="flex items-center gap-2 mb-2">
                          <View
                            class={`px-2 py-1 rounded-1 text-24rpx text-white`}
                            style={{
                              'background-color': getRecordTypeColor(record.recordType) === 'orange' ? '#f97316' :
                                getRecordTypeColor(record.recordType) === 'blue' ? '#3b82f6' :
                                  getRecordTypeColor(record.recordType) === 'red' ? '#ef4444' :
                                    getRecordTypeColor(record.recordType) === 'green' ? '#22c55e' : '#6b7280'
                            }}
                          >
                            {getRecordTypeLabel(record.recordType)}
                          </View>
                          <View class="text-24rpx text-gray-500">
                            {dayjs(record.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                          </View>
                        </View>
                        <Show when={record.description}>
                          <View class="text-28rpx text-gray-800 dark:text-gray-200 mb-1">
                            {record.description}
                          </View>
                        </Show>
                      </View>
                      <View class="text-right">
                        <View
                          class={`text-36rpx font-bold ${record.pointsChange > 0 ? 'text-green-500' : 'text-red-500'
                            }`}
                        >
                          {formatPointsChange(record.pointsChange)}
                        </View>
                      </View>
                    </View>
                )}
              </For>
            </View>
          </Show>

          <Show when={loading()}>
            <View class="text-center py-4 text-gray-500">
              加载中...
            </View>
          </Show>

          <Show when={!hasMore() && records().length > 0}>
            <View class="text-center py-4 text-gray-500">
              没有更多记录了
            </View>
          </Show>
        </ScrollView>
      </View>

      <SafeBottom />
    </BasePage>
  );
}
