import { View, Button } from "@tarojs/components"
import { mergeProps, Component, JSXElement } from "solid-js"
import clsx from 'clsx'
import { Iconfont } from "../Iconfont"
import './MyButton.scss'

type ButtonType = 'primary' | 'default' | 'success' | 'warning' | 'danger' | 'info'
type ButtonSize = 'large' | 'normal' | 'small' | 'mini'
type ButtonVariant = 'solid' | 'outline' | 'text'
type ButtonShape = 'square' | 'round'

export interface TMyButtonProps {
  /** 按钮类型 */
  type?: ButtonType
  /** 按钮尺寸 */
  size?: ButtonSize
  /** 是否块级元素 */
  block?: boolean
  /** 按钮形状 */
  shape?: ButtonShape
  /** 是否加载中 */
  loading?: boolean
  /** 加载状态文字 */
  loadingText?: string
  /** 是否禁用状态 */
  disabled?: boolean
  /** 按钮变体 */
  variant?: ButtonVariant
  /** 图标类名 */
  icon?: string
  /** 自定义样式类 */
  class?: string
  /** 点击事件 */
  onClick?: (e: any) => void
  /** 按钮内容 */
  children?: JSXElement
  /** 原生 button 属性 */
  [key: string]: any
}

export const MyButton: Component<TMyButtonProps> = (_props) => {
  const local = mergeProps({
    type: 'primary',
    size: 'normal',
    block: false,
    disabled: false,
    loading: false,
    variant: 'solid',
    shape: '',
  }, _props)

  const handleClick = (e: any) => {
    if (local.loading || local.disabled) return
    local.onClick?.(e)
  }

  const renderLoadingIcon = () => {
    if (!local.loading) return null
    return (
      <View class="my-button__loading">
        <Iconfont name="loading" size={20} class="my-button__loading-icon" />
        {local.loadingText && <View class="my-button__loading-text">{local.loadingText}</View>}
      </View>
    )
  }

  const renderIcon = () => {
    if (!local.icon) return null
    return (
      <View class="my-button__icon">
        <Iconfont name={local.icon as any} size={20} class="my-button__icon-svg" />
      </View>
    )
  }

  const renderContent = () => {
    if (local.loading) {
      return renderLoadingIcon()
    }

    return (
      <>
        {renderIcon()}
        <View class="my-button__text">
          {local.children}
        </View>
      </>
    )
  }

  return (
    <Button
      class={clsx(
        'mx-0',
        'my-button',
        `my-button--${local.type}`,
        `my-button--${local.size}`,
        `my-button--${local.variant}`,
        {
          'my-button--round': local.shape === 'round',
          'my-button--square': local.shape === 'square',
          'my-button--block': local.block,
          'my-button--disabled': local.disabled,
          'my-button--loading': local.loading,
          [local.class || '']: !!local.class
        }
      )}
      disabled={local.disabled || local.loading}
      onClick={handleClick}
      
    >
      {renderContent()}
    </Button>
  )
}