import { BasePage } from '@/components/BasePage'
import { MyNavbar } from '@/components/MyNavbar'
import { Empty } from '@/components/Empty'
import { Loading } from '@/components/Loading'
import { MyButton } from '@/components/MyButton'
import { Iconfont } from '@/components/Iconfont'
import { apiMessagePageList, apiMessageMarkRead, apiMessageMarkAllRead, apiMessageUnreadCount } from '@/apis/apis'
import { $unreadMessageCount, $latestMessage } from '@/utils/messageWebSocket'
import { View, Text, ScrollView } from '@tarojs/components'
import { createSignal, createEffect, For, Show, onMount, onCleanup } from 'solid-js'
import { useStore } from '@nanostores/solid'
import { Utils } from '@/utils/utils'
import Taro, { usePullDownRefresh, useReachBottom } from '@tarojs/taro'
import dayjs from 'dayjs'

export default function MessageListPage() {
  const [messages, setMessages] = createSignal<TClientMessage[]>([])
  const [loading, setLoading] = createSignal(false)
  const [hasMore, setHasMore] = createSignal(true)
  const [pageIndex, setPageIndex] = createSignal(1)
  const [selectedMessageType, setSelectedMessageType] = createSignal<string>('')
  const [selectedReadStatus, setSelectedReadStatus] = createSignal<boolean | undefined>(undefined)
  const [expandedMessages, setExpandedMessages] = createSignal<Set<string>>(new Set())
  const [selectedMessages, setSelectedMessages] = createSignal<Set<string>>(new Set())
  const [isSelectionMode, setIsSelectionMode] = createSignal(false)

  // 使用WebSocket状态
  const unreadCount = useStore($unreadMessageCount)
  const latestMessage = useStore($latestMessage)

  const pageSize = 20

  // 消息类型选项
  const messageTypeOptions = [
    { label: '全部', value: '' },
    { label: '系统消息', value: 'SYSTEM' },
    { label: '通知消息', value: 'NOTICE' },
    { label: '推广消息', value: 'PROMOTION' }
  ]

  // 已读状态选项
  const readStatusOptions = [
    { label: '全部', value: undefined },
    { label: '未读', value: false },
    { label: '已读', value: true }
  ]

  // 加载消息列表
  const loadMessages = async (refresh = false) => {
    if (loading()) return

    setLoading(true)
    try {
      const currentPageIndex = refresh ? 0 : pageIndex()
    
      const res = await apiMessagePageList({
        pageIndex: currentPageIndex,
        pageSize,
        messageType: selectedMessageType() || undefined,
        isRead: selectedReadStatus()
      })

      if (refresh) {
        setMessages(res.rows)
        setPageIndex(1)
      } else {
        setMessages(prev => [...prev, ...res.rows])
        setPageIndex(prev => prev + 1)
      }

      setHasMore(res.rows.length === pageSize)
    } catch (error) {
      Utils.toast('加载失败')
    } finally {
      setLoading(false)
    }
  }

  // 加载未读消息数量
  const loadUnreadCount = async () => {
    try {
      const count = await apiMessageUnreadCount()
      $unreadMessageCount.set(count)
    } catch (error) {
      console.error('获取未读消息数量失败:', error)
    }
  }

  // 展开/折叠消息内容
  const toggleMessageExpand = async (messageId: string) => {
    const expanded = expandedMessages()
    const newExpanded = new Set(expanded)

    if (expanded.has(messageId)) {
      newExpanded.delete(messageId)
    } else {
      newExpanded.add(messageId)
      // 展开时标记为已读
      const message = messages().find(m => m.id === messageId)
      if (message && !message.isRead) {
        await markMessagesAsRead([messageId])
      }
    }

    setExpandedMessages(newExpanded)
  }

  // 标记消息为已读
  const markMessagesAsRead = async (messageIds: string[]) => {
    try {
      await apiMessageMarkRead({ messageIds })

      // 更新本地消息状态
      setMessages(prev => prev.map(msg =>
        messageIds.includes(msg.id) ? { ...msg, isRead: true } : msg
      ))

      // 更新未读数量
      await loadUnreadCount()
    } catch (error) {
      Utils.toast('标记失败')
    }
  }

  // 全部标记为已读
  const markAllAsRead = async () => {
    try {
      await apiMessageMarkAllRead()

      // 更新本地消息状态
      setMessages(prev => prev.map(msg => ({ ...msg, isRead: true })))

      // 更新未读数量
      $unreadMessageCount.set(0)

      Utils.toast('已全部标记为已读')
    } catch (error) {
      Utils.toast('操作失败')
    }
  }

  // 切换选择模式
  const toggleSelectionMode = () => {
    setIsSelectionMode(!isSelectionMode())
    setSelectedMessages(new Set<string>())
  }

  // 切换消息选择状态
  const toggleMessageSelection = (messageId: string) => {
    const selected = selectedMessages()
    const newSelected = new Set(selected)

    if (selected.has(messageId)) {
      newSelected.delete(messageId)
    } else {
      newSelected.add(messageId)
    }

    setSelectedMessages(newSelected)
  }

  // 批量标记已读
  const batchMarkAsRead = async () => {
    const selectedIds = Array.from(selectedMessages())
    if (selectedIds.length === 0) {
      Utils.toast('请选择要标记的消息')
      return
    }

    await markMessagesAsRead(selectedIds)
    setSelectedMessages(new Set<string>())
    setIsSelectionMode(false)
  }

  // const reloadMessages = async () => {
  //   console.log('reloadMessages')
  //   setMessages([])
  //   setPageIndex(1)
  //   await loadMessages(true)
  //   await loadUnreadCount()
  // }
  

  // 筛选变化时重新加载
  createEffect(() => {
    selectedMessageType()
    selectedReadStatus()
    console.log('筛选变化')
    loadMessages(true)
  })

  // onMount(() => {
  //   setInterval(() => {
  //     setLoading(!loading())
  //     console.log(loading())
  //   }, 1000)
  // })

  // 监听WebSocket新消息，自动刷新列表
  // createEffect(() => {
  //   const latest = latestMessage()
  //   if (latest) {
  //     // 如果当前筛选条件匹配新消息，则刷新列表
  //     const typeMatch = !selectedMessageType() || selectedMessageType() === latest.messageType
  //     const readMatch = selectedReadStatus() === undefined || selectedReadStatus() === latest.isRead

  //     if (typeMatch && readMatch) {
  //       loadMessages(true)
  //     }
  //   }
  // })

  // // 下拉刷新
  // usePullDownRefresh(async () => {
  //   await loadMessages(true)
  //   await loadUnreadCount()
  //   Taro.stopPullDownRefresh()
  // })

  // // 上拉加载更多
  // useReachBottom(() => {
  //   if (hasMore() && !loading()) {
  //     loadMessages()
  //   }
  // })

  // // 页面初始化
  // onMount(() => {
  //   loadMessages(true)
  //   loadUnreadCount()
  // })

  return (
    <BasePage>
      <MyNavbar title="消息中心" />

      {/* 筛选栏 */}
      <View class="p-4 bg-white dark:bg-stone-800 border-b border-gray-200 dark:border-stone-700">
        <View class="flex items-center justify-between mb-3">
          <View class="flex items-center gap-2">
            <Text class="text-sm font-medium">筛选条件</Text>
            <Show when={unreadCount() > 0}>
              <View class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                {unreadCount()}条未读
              </View>
            </Show>
          </View>

          <View class="flex items-center gap-2">
            <Show when={!isSelectionMode()}>
              <MyButton size="mini" onClick={toggleSelectionMode}>
                批量操作
              </MyButton>
              <MyButton size="mini" type="primary" onClick={markAllAsRead}>
                全部已读
              </MyButton>
            </Show>
            <Show when={isSelectionMode()}>
              <MyButton size="mini" onClick={batchMarkAsRead}>
                标记已读({selectedMessages().size})
              </MyButton>
              <MyButton size="mini" onClick={toggleSelectionMode}>
                取消
              </MyButton>
            </Show>
          </View>
        </View>

        <View class="flex flex-col gap-4">
          <View class="flex gap-2">
            <Text class="text-sm text-gray-600 dark:text-gray-400">类型:</Text>
            <View class='flex-1 flex flex-wrap gap-2'>
              <For each={messageTypeOptions}>
                {(option) => (
                  <View
                    class={`px-3 py-1 rounded-full text-sm cursor-pointer transition-colors ${selectedMessageType() === option.value
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-100 dark:bg-stone-700 text-gray-600 dark:text-gray-400'
                      }`}
                    onClick={() => setSelectedMessageType(option.value)}
                  >
                    {option.label}
                  </View>
                )}
              </For>
            </View>

          </View>

          <View class="flex items-center gap-2">
            <Text class="text-sm text-gray-600 dark:text-gray-400">状态:</Text>
            <For each={readStatusOptions}>
              {(option) => (
                <View
                  class={`px-3 py-1 rounded-full text-sm cursor-pointer transition-colors ${selectedReadStatus() === option.value
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-100 dark:bg-stone-700 text-gray-600 dark:text-gray-400'
                    }`}
                  onClick={() => setSelectedReadStatus(option.value)}
                >
                  {option.label}
                </View>
              )}
            </For>
          </View>
        </View>
      </View>

      {/* 消息列表 */}
      <ScrollView
        class="flex-1"
        scrollY
        enhanced
        showScrollbar={false}
      >
        <Show when={messages().length > 0} fallback={
          <Show when={!loading()}>
            <Empty description="暂无消息" />
          </Show>
        }>
          <View class="p-4">
            <For each={messages()}>
              {(message) => (
                <View class={`mb-4 p-4 rounded-lg border transition-all ${message.isRead
                  ? 'bg-white dark:bg-stone-800 border-gray-200 dark:border-stone-700'
                  : 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                  }`}>
                  <View class="flex items-start justify-between mb-2">
                    <View class="flex items-center gap-2 flex-1">
                      <Show when={isSelectionMode()}>
                        <View
                          class={`w-5 h-5 rounded border-2 flex items-center justify-center cursor-pointer ${selectedMessages().has(message.id)
                            ? 'bg-green-500 border-green-500'
                            : 'border-gray-300 dark:border-stone-600'
                            }`}
                          onClick={() => toggleMessageSelection(message.id)}
                        >
                          <Show when={selectedMessages().has(message.id)}>
                            <Iconfont name="check" size={12} color="white" />
                          </Show>
                        </View>
                      </Show>

                      <Text class="font-medium text-gray-900 dark:text-gray-100 flex-1">
                        {message.title}
                      </Text>

                      <Show when={!message.isRead}>
                        <View class="w-2 h-2 bg-red-500 rounded-full"></View>
                      </Show>
                    </View>

                    <View class="flex items-center gap-2 ml-2">
                      <View class={`px-2 py-1 rounded text-xs ${message.messageType === 'SYSTEM' ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400' :
                        message.messageType === 'NOTICE' ? 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400' :
                          'bg-orange-100 text-orange-600 dark:bg-orange-900/30 dark:text-orange-400'
                        }`}>
                        {message.messageType === 'SYSTEM' ? '系统' :
                          message.messageType === 'NOTICE' ? '通知' : '推广'}
                      </View>

                      <Text class="text-xs text-gray-500 dark:text-gray-400">
                        {dayjs(message.createdAt).format('MM-DD HH:mm')}
                      </Text>
                    </View>
                  </View>

                  <Show when={expandedMessages().has(message.id)} fallback={
                    <View
                      class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 cursor-pointer"
                      onClick={() => toggleMessageExpand(message.id)}
                    >
                      {message.content}
                    </View>
                  }>
                    <View class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      {message.content}
                    </View>
                    <View
                      class="text-xs text-green-500 cursor-pointer flex items-center gap-1"
                      onClick={() => toggleMessageExpand(message.id)}
                    >
                      <Text>收起</Text>
                      <Iconfont name="top" size={12} />
                    </View>
                  </Show>

                  <Show when={!expandedMessages().has(message.id)}>
                    <View
                      class="text-xs text-green-500 cursor-pointer flex items-center gap-1 mt-2"
                      onClick={() => toggleMessageExpand(message.id)}
                    >
                      <Text>展开</Text>
                      <Iconfont name="doubledown" size={12} />
                    </View>
                  </Show>
                </View>
              )}
            </For>
          </View>
        </Show>

        <Show when={loading()}>
          {/* <Loading /> */}
          <View class="text-center py-4 text-gray-500 dark:text-gray-400 text-sm">
            加载中...
          </View>
        </Show>

        <Show when={!hasMore() && messages().length > 0}>
          <View class="text-center py-4 text-gray-500 dark:text-gray-400 text-sm">
            没有更多消息了
          </View>
        </Show>
      </ScrollView>
    </BasePage>
  )
}
