import { fetchXml, XmlData } from 'iconfont-parser';
import fs from 'fs';
import path from 'path';

const trim_icon_prefix = 'icon'

const addAttribute = (domName: string, sub: XmlData['svg']['symbol'][number]['path'][number], colors: string[]) => {
  let template = '';

  if (sub && sub.$) {
    if (domName === 'path') {
      sub.$.fill = sub.$.fill || '#333333';
    }

    for (const attributeName of Object.keys(sub.$)) {
      if (attributeName === 'fill') {
        const color = sub.$[attributeName] as string;
        template += ` ${attributeName}='{{color}}'`;
        colors.push(color);
      } else {
        template += ` ${attributeName}='${sub.$[attributeName]}'`;
      }
    }
  }

  return template;
};

const generateCase = (data: XmlData['svg']['symbol'][number]) => {
  let template = `<svg viewBox='${data.$.viewBox}' xmlns='http://www.w3.org/2000/svg'>`;
  const colors: string[] = [];
  for (const domName of Object.keys(data)) {
    if (domName === '$') {
      continue;
    }
    if (data[domName].$) {
      template += `<${domName}${addAttribute(domName, data[domName], colors)} />`;
    } else if (Array.isArray(data[domName])) {
      data[domName].forEach((sub) => {
        template += `<${domName}${addAttribute(domName, sub, colors)} />`;
      });
    }
  }

  template += `</svg>`;
  return {
    template,
    colors,
  };
};

async function main() {
  const url = process.argv[2]
  const data = await fetchXml('https:' + url);
  const svgMap: Record<string, {
    template: string
    colors: string[]
  }> = {};
  data.svg.symbol.forEach((item) => {
    const iconId = item.$.id;
    const iconIdAfterTrim = trim_icon_prefix
      ? iconId.replace(
        new RegExp(`^${trim_icon_prefix}(.+?)$`),
        (_, value) => value.replace(/^[-_.=+#@!~*]+(.+?)$/, '$1')
      )
      : iconId;
    svgMap[iconIdAfterTrim] = generateCase(item);
  })

  const iconfontDirPath = path.join('./src/components', 'Iconfont')

  if (!fs.existsSync(iconfontDirPath)) {
    fs.mkdirSync(iconfontDirPath, { recursive: true });
  }

  const iconfontFileContent = `import { Image } from "@tarojs/components"
import { createMemo, createSignal, onMount, Show } from "solid-js"
import { Utils } from "@/utils/utils"
import Taro from "@tarojs/taro"
import customColors from "./customColors"

export interface TIconfontProps {
  class?: string;
  style?: any;
  name: ${Object.keys(svgMap).map(item => `'${item}'`).join(' | ')} | '';
  color?: string | string[] | Record<number, string>
  size?: number
}

export const Iconfont = (props: TIconfontProps) => {
  if (!svgMap[props.name]) {
    return null
  }

  const id = createMemo(() => 'icon-' + Utils.guid())
  const [size, setSize] = createSignal<string>((typeof props.size === 'string' ? props.size : (props.size || 28) + 'px'))
  const [defaultColor, setDefaultColor] = createSignal<string>()

  onMount(() => {
    Taro.createSelectorQuery().select('#' + id()).fields({
      computedStyle: ['font-size', 'color']
    }).exec((res) => {
      if (!props.size && res[0]?.['font-size']) {
        setSize(res[0]['font-size'])
      }
      if (!props.color && res[0]?.['color']) {
        setDefaultColor(res[0]['color'])
      }
    })
  })

  const svgStr = createMemo(() => {
    const svgData = svgMap[props.name]
    let colors = [...(customColors[props.name] ||svgData.colors || [])]
    if (typeof props.color === 'string') {
      colors = new Array(colors.length + 1).fill(props.color)
    } else if (Array.isArray(props.color)) {
      props.color.forEach((color, index) => {
        colors[index] = color
      })
    } else if (typeof props.color === 'object') {
      Object.keys(props.color).forEach((key) => {
        colors[Number(key)] = (props.color as Record<number, string>)[Number(key)]
      })
    } else if (defaultColor() && colors.length === 1) {
      colors = [defaultColor()!]
    }
    let index = 0
    const svg = svgData.template.replace(/{{color}}/g, () => colors[index++])
    return Utils.base64Encode(svg)
  })

  return (
    <Show when= { svgStr } >
      <Image class={props.class} id={id()} style={{ width: size(), height: size(), ...(props.style || {}) }} src={'data:image/svg+xml;base64,' + svgStr()} />
    </Show>
  )
}

const svgMap = ${JSON.stringify(svgMap, null, 2)}
`
  fs.writeFileSync(path.join(iconfontDirPath, 'Iconfont.tsx'), iconfontFileContent)

  if(!fs.existsSync(path.join(iconfontDirPath, 'customColors.ts'))) {
    fs.writeFileSync(path.join(iconfontDirPath, 'customColors.ts'), `const customColors: Record<string, string[]> = {};\nexport default customColors;`)
  }
}
main()

