import { createRef } from "@/utils/createRef";
import { Utils } from "@/utils/utils";
import { View, Text } from "@tarojs/components"
import { createSignal, createEffect, mergeProps, Show, For } from "solid-js"
import { Iconfont } from "@/components/Iconfont";

export interface TMyTableColumn {
  title: string;
  dataIndex?: string;
  sortable?: boolean;
  pageSize?: number;
  render?: (record: any) => any;
  width?: number; // 用于比例分配
  align?: 'left' | 'center' | 'right'; // 默认 left
}


export interface TMyTableRef {
  loadMore(): void
  reload(): void
}


export interface TMyTableProps {
  ref?: TMyTableRef,
  columns: TMyTableColumn[];
  height?: number;
  pageSize?: number;
  request?: (
    params: { pageNum: number },
    sorter?: { field: string; order: "asc" | "desc" }
  ) => Promise<{ list: any[]; total: number }>;
  dataSource?: any[];
  bordered?: boolean;
}

export const MyTable = (_props: TMyTableProps) => {

  const props = mergeProps({
    height: Utils.pageSize.pageHeight,
    pageSize: 10,
  }, _props)

  const [pageNum, setPageNum] = createSignal(1)
  const [sorter, setSorter] = createSignal<{ field: string; order: "asc" | "desc" } | undefined>(undefined)
  const [data, setData] = createSignal<any[]>([])
  const [total, setTotal] = createSignal(0)
  const [loading, setLoading] = createSignal(false)
  const [isLoadingMore, setIsLoadingMore] = createSignal(false)

  createRef(() => props.ref, () => ({
    loadMore: () => {
      console.log("loadMore")
      if (loading() || isLoadingMore()) return
      if (data().length >= total()) return
      setPageNum(pageNum() + 1)
    },
    reload: () => {
      resetToFirstPage()
    }
  }))

  // fetchData: append=true 时拼接，append=false 时替换
  const fetchData = async (append = false) => {
    // 如果提供了 dataSource，则直接使用而不发起请求
    if (props.dataSource !== undefined) {
      setData(props.dataSource)
      setTotal(props.dataSource.length)
      return
    }
    
    if (!props.request) {
      console.warn('MyTable: request prop is required when dataSource is not provided')
      return
    }
    
    if (append) setIsLoadingMore(true)
    else setLoading(true)
    const res = await props.request({ pageNum: pageNum() }, sorter())
    if (append) {
      setData([...data(), ...(res.list || [])])
    } else {
      setData(res.list || [])
    }
    setTotal(res.total)
    setLoading(false)
    setIsLoadingMore(false)
  }

  // 监听 pageNum 变化和 dataSource 变化
  createEffect(() => {
    // 如果提供了 dataSource，则直接使用
    if (props.dataSource !== undefined) {
      setData(props.dataSource)
      setTotal(props.dataSource.length)
      return
    }
    
    if (pageNum() === 0) {
      return
    }
    if (pageNum() === 1) {
      // setData([])
      fetchData(false)
    } else {
      fetchData(true)
    }
  })

  // 排序/切tab/外部触发时，重置页码为1（自动触发上面 effect）
  const resetToFirstPage = () => {
    if(pageNum() === 1) {
      setPageNum(0)
    }
    setPageNum(1)
  }

  const handleSort = (col: TMyTableColumn) => {
    if (!col.sortable) return
    const current = sorter()
    if (!current || current.field !== col.dataIndex) {
      setSorter({ field: col.dataIndex!, order: "desc" })
    } else if (current.order === "desc") {
      setSorter({ field: col.dataIndex!, order: "asc" })
    } else if (current.order === "asc") {
      setSorter(undefined)
    }
    resetToFirstPage()
  }


  // 计算总宽度
  const totalWidth = () => props.columns.reduce((sum, col) => sum + (col.width || 1), 0)
  // 计算每列百分比宽度
  const colPercent = (col: TMyTableColumn) => ((col.width || 1) / totalWidth()) * 100
  // 计算每列对齐方式
  const colAlignClass = (col: TMyTableColumn) => {
    const align = col.align || 'left'
    if (align === 'center') return 'text-center justify-center'
    if (align === 'right') return 'text-right justify-end'
    return 'text-left justify-start'
  }

  // 获取嵌套对象的值，处理路径中的点号和空值情况
  const getNestedValue = (obj: any, path: string): any => {
    if (!obj || !path) return "";
    
    const keys = path.split('.');
    let result = obj;
    
    for (const key of keys) {
      if (result === null || result === undefined) {
        return "";
      }
      result = result[key];
    }
    
    return result !== null && result !== undefined ? result : "";
  }

  return (
    <View class={`MyTable w-full overflow-x-auto text-24rpx`}>
      <View class={`flex border-zinc-200 dark:border-stone-600 ${props.bordered ? 'border-b-solid' : ''}`}>
        <For each={props.columns}>
          {(col) => (
            <View
              style={{ width: `${colPercent(col)}%` }}
              class={`box-border px-1 py-3 shrink-0 font-bold flex items-center gap-1 ${colAlignClass(col)} ${col.sortable ? 'cursor-pointer select-none' : ''}`}
              onClick={() => handleSort(col)}
            >
              <View>{col.title}</View>
              <Show when={col.sortable && sorter()?.field !== col.dataIndex}>
                <Iconfont name="sort" />
              </Show>
              <Show when={col.sortable && sorter()?.field === col.dataIndex && sorter()!.order === "asc"}>
                <Iconfont name="sort-up" class="text-red-6 dark:text-red" />
              </Show>
              <Show when={col.sortable && sorter()?.field === col.dataIndex && sorter()!.order === "desc"}>
                <Iconfont name="sort-down" class="text-green-6 dark:text-green" />
              </Show>
            </View>
          )}
        </For>
      </View>
      <For each={data()} fallback={!loading() ? <View class="p-4 text-center">暂无数据</View> : null}>
        {(record) => (
          <View class={`flex items-center border-zinc-100 dark:border-stone-700 border-1rpx ${props.bordered ? 'border-b-solid' : ''}`}>
            <For each={props.columns}>
              {(col) => (
                <View
                  style={{ width: `${colPercent(col)}%` }}
                  class={`box-border px-1 py-2 shrink-0 ${colAlignClass(col)}`}
                >
                  <Show when={col.render} fallback={<Text>{col.dataIndex ? getNestedValue(record, col.dataIndex) : ""}</Text>}>
                    {col.render?.(record)}
                  </Show>
                </View>
              )}
            </For>
          </View>
        )}
      </For>
      <Show when={isLoadingMore()}>
        <View class="p-2 text-center text-gray-400 dark:text-stone-600">加载中...</View>
      </Show>
      <Show when={!isLoadingMore() && data().length < total() && data().length > 0}>
        <View class="p-2 text-center text-gray-400 dark:text-stone-600">上拉加载更多</View>
      </Show>

    </View>
  )
}