import { View } from "@tarojs/components"
import { createEffect, JSXElement, mergeProps, Show } from "solid-js"
import { SafeBottom } from "../SafeBottom"
import { Iconfont } from "../Iconfont"
import { MyPortal } from "../MyPortal"

export interface TMyPopupProps {
  showHeader?: boolean
  title?: string
  btns?: 'none' | 'close' | 'cancelAndOk'
  open?: boolean
  onOpenChange?: (open: boolean) => void
  zIndex?: number
  children?: JSXElement
  closeOnClickMask?: boolean
  onOk?: () => void
  okText?: string
  cancelText?: string
}
export const MyPopup = (_props: TMyPopupProps) => {
  const props = mergeProps({
    btns: 'none',
    showHeader: true,
    closeOnClickMask: true,
    okText: '确定',
    cancelText: '取消'
  }, _props)


  const close = () => {
    if (!props.onOpenChange) return
    props.onOpenChange?.(false)
  }

  let maskRef: HTMLDivElement | undefined = undefined

  const onClickMask = () => {
    if (!props.closeOnClickMask) return
    close()
  }

  createEffect(() => {
    const mask = maskRef as HTMLDivElement | undefined
    if (!mask) return
    if (props.open) {
      mask.style.display = 'block';
    } else {
      setTimeout(() => {
        mask.style.display = 'none';
      }, 300);
    }
  })

  return (
    <MyPortal>
      <View class="MyPopup relative" style={{ 'z-index': props.zIndex || 9999 }}>
        <View class="MyPopup-mask fixed left-0 top-0 w-screen h-screen bg-black" ref={maskRef} style={{ transition: 'opacity 0.3s ease', opacity: props.open ? '0.5' : '0' }} onClick={onClickMask}></View>
        <View class="MyPopup-main fixed left-0 bottom-0 w-screen h-auto" style={{ transition: 'transform 0.3s ease', transform: props.open ? 'translate(0, 0)' : 'translate(0, 100%)' }}>
          <Show when={props.showHeader && (props.btns !== 'none' || props.title)}>
            <View class="MyPopup-header bg-white dark:bg-stone-900 h-12 relative flex items-center rounded-t-2xl" >
              <View class="MyPopup-header-title absolute left-0 right-0 w-full text-center text-36rpx font-bold text-black dark:text-white">{props.title}</View>
              <Show when={props.btns === 'close'}>
                <View class="MyPopup-header-close absolute right-1 p-2 flex items-center justify-center text-black dark:text-white" onClick={close}>
                  <Iconfont name='close' size={36} />
                </View>
              </Show>
              <Show when={props.btns === 'cancelAndOk'}>
                <View class="MyPopup-header-cancel box-border absolute left-0 h-full px-4 flex items-center justify-center text-green-500" onClick={close}>
                  {props.cancelText}
                </View>
                <View class="MyPopup-header-ok absolute right-0 h-full px-4 flex items-center justify-center text-green-500" onClick={props.onOk}>
                  {props.okText}
                </View>
              </Show>
            </View>
          </Show>
          <View class="MyPopup-content bg-white dark:bg-stone-900">{props.children}</View>
          <View class="MyPopup-footer bg-white dark:bg-stone-900">
            <SafeBottom />
          </View>
        </View>
      </View>
    </MyPortal>

  )

}