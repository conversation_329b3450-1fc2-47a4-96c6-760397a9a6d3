

.richTextImage {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 8PX;
  margin: 8PX 0;
}

:root {
  --primary-color: #22c55e;
  box-sizing: border-box;
}

.primaryBg {
  background-color: var(--primary-color);
}

.primaryText {
  color: var(--primary-color);
}

.greenBg {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg%20width%3D%221%22%20height%3D%22400%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%20%20%3Cdefs%3E%0A%20%20%20%20%3ClinearGradient%20id%3D%22gradient%22%20x1%3D%220%25%22%20y1%3D%220%25%22%20x2%3D%220%25%22%20y2%3D%22100%25%22%3E%0A%20%20%20%20%20%20%3Cstop%20offset%3D%220%25%22%20style%3D%22stop-color%3A%2322c55e%3Bstop-opacity%3A0.3%22%20%2F%3E%0A%20%20%20%20%20%20%3Cstop%20offset%3D%22100%25%22%20style%3D%22stop-color%3A%2322c55e%3Bstop-opacity%3A0%22%20%2F%3E%0A%20%20%20%20%3C%2FlinearGradient%3E%0A%20%20%3C%2Fdefs%3E%0A%20%20%3Crect%20width%3D%221%22%20height%3D%22400%22%20fill%3D%22url(%23gradient)%22%20%2F%3E%0A%3C%2Fsvg%3E") !important;
  background-repeat: repeat no-repeat !important;
}

