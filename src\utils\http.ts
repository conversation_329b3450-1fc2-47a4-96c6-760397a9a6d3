import Taro from "@tarojs/taro";
import { Utils } from "./utils";
import dayjs from "dayjs";
import waitUntil from "async-wait-until";

interface THttpOptions {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  data?: any;
  query?: any;
  header?: any;
}

interface THttpCustomOptions {
  autoLoading?: boolean;
  autoShowError?: boolean;
}


export class HttpUtils {
  static loadingCount = 0;

  static token = Utils.cacheGet('token');

  static baseUrl = Utils.apiUrl;

  static async request<T>(options: THttpOptions, customOptions?: THttpCustomOptions) {
    if (!options.url.startsWith('/auth/') && !this.token) {
      await waitUntil(() => !!this.token, { timeout: 10000 })
    }
    const { url, method = 'GET', data = {}, query = {}, header = {} } = options;
    const { autoLoading = false, autoShowError = true } = customOptions || {};

    if (autoLoading) {
      console.log('autoLoading showLoading')
      Taro.showLoading({
        title: '加载中',
      });
      this.loadingCount++;
    }

    const [urlWithOutQuery, urlQueryStr] = url.split('?');
    const urlQuery = urlQueryStr ? Utils.qsParse(urlQueryStr) : {};
    const finalQuery = { ...urlQuery, ...query };
    const finalUrl = `${this.baseUrl}${urlWithOutQuery}${Object.keys(finalQuery).length > 0 ? `?${Utils.qsStringify(finalQuery)}` : ''}`;

    const finalHeader = { 'Accept': 'application/json', ...header, "X-WX-APPID": Utils.appId };

    if (this.token) {
      finalHeader['Authorization'] = `Bearer ${this.token}`;
    }

    if (!header['Content-Type'] && ['POST', 'PUT'].includes(method) && data) {
      finalHeader['Content-Type'] = 'application/json';
    }

    try {
      const res = await Taro.request({
        url: finalUrl,
        method,
        header: finalHeader,
        data: ['POST', 'PUT'].includes(method) ? data : undefined,
      });
      if (res.statusCode !== 200) {
        throw new Error(res.data.message);
      }
      if (res.data.code !== 0) {
        throw new Error(res.data.message);
      }
      return res.data.data as T;
    } catch (error) {
      if (autoShowError) {
        Utils.toast(error.message);
      }
      throw error;
    } finally {
      if (autoLoading) {
        // Taro.hideLoading();
        this.loadingCount--;
        console.log('loadingCount', this.loadingCount)
        if (this.loadingCount <= 0) {
          this.loadingCount = 0;
          Taro.hideLoading();
          console.log('hideLoading')
        }
      }
    }
  }

  static setToken(token: string, expiredAt?: string) {
    this.token = token;
    if (token) {
      Utils.cacheSet('token', token, expiredAt ? dayjs(expiredAt).diff(dayjs(), 'ms') : 0);
    } else {
      Utils.cacheRemove('token');
    }
  }

  static async uploadMedia({
    sourceType = ['album'],
    mediaType = ['image', 'video'],
    onChange
  }: {
    sourceType?: string[],
    mediaType?: string[],
    onChange: (data: {
      status: 'uploading' | 'success' | 'fail',
      message?: string,
      progress?: number,
      url?: string
    }) => void,
  }) {
    const chooseMediaResult = await Taro.chooseMedia({
      count: 1,
      sourceType: sourceType as any,
      mediaType: mediaType as any,
    });
    const tempFile = chooseMediaResult.tempFiles[0]

    const uploadTask = Taro.uploadFile({
      url: `${this.baseUrl}/file/upload`,
      header: {
        "Authorization": `Bearer ${this.token}`,
      },
      filePath: tempFile.tempFilePath,
      name: "file",
      success: (res) => {
        if (res.statusCode !== 200) {
          onChange({
            status: 'fail',
            message: '上传失败'
          })
          return
        }
        const data = JSON.parse(res.data)
        if (data.code !== 0) {
          onChange({
            status: 'fail',
            message: data.message
          })
          return
        }

        onChange({
          status: 'success',
          url: data.data
        })
      },
      fail: (e) => {
        console.log("上传失败", e)
        onChange({
          status: 'fail',
          message: '上传失败'
        })
      },
    });
    uploadTask.onProgressUpdate((res) => {
      onChange({
        status: 'uploading',
        progress: res.progress,
      })
    });
  }
}
