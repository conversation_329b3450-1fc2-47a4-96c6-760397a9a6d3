import { TIconfontProps } from "@/components/Iconfont";
import { $currentBackQuery } from "@/store/appStore";
import Taro from "@tarojs/taro";
import dayjs from "dayjs";
import { HttpUtils } from "./http";
import { apiCloseBill } from "@/apis/apis";

export class Utils {
  static imageSrc(avatar: string | undefined): string {
    if (!avatar) return "";
    console.log("avatar", avatar);
    if (avatar.startsWith("D:"))
      return avatar.replace("D:", "/assets/defaultImage/");
    return avatar;
  }

  static windowInfo = Taro.getWindowInfo();

  static pageSize = (() => {
    const statusBarHeight = this.windowInfo.statusBarHeight!;
    const wxMenuRect = Taro.getMenuButtonBoundingClientRect();
    const navbarMenuHeight = wxMenuRect.height;
    const navbarMenuMarginY = wxMenuRect.top - statusBarHeight;
    const safeBottom =
      this.windowInfo.screenHeight - (this.windowInfo.safeArea?.bottom || 0);
    const navbarHeight =
      navbarMenuMarginY * 2 + navbarMenuHeight + statusBarHeight;
    const tabbarHeight = 50;
    return {
      statusBarHeight,
      navbarMenuHeight,
      navbarMenuMarginY,
      navbarHeight,
      safeBottom,
      pageHeight: this.windowInfo.screenHeight - navbarHeight - safeBottom,
      tabbarHeight,
      tabPageHeight:
        this.windowInfo.screenHeight - navbarHeight - safeBottom - tabbarHeight,
    };
  })();

  static isTabbarPage() {
    const currentPath = this.getCurrentPath();
    const isTabbar = (Taro.getApp()?.config.tabBar?.list || []).some(
      (item: any) => {
        const reg = `^/${item.pagePath}`.replace(/\//g, "\\/");
        return new RegExp(reg).test(currentPath);
      }
    );
    return isTabbar;
  }

  static pageHeight = this.windowInfo.screenHeight;

  static appId = (() => {
    return Taro.getAccountInfoSync()?.miniProgram?.appId;
  })();

  static isEnv(key: string) {
    const envVersion = Taro.getAccountInfoSync()?.miniProgram
      ?.envVersion as string;
    return envVersion === key;
  }

  static isDevelop = (() => Utils.isEnv("develop"))();

  static isTrial = (() => Utils.isEnv("trial"))();

  static isDev = (() => Utils.isDevelop || Utils.isTrial)();

  static apiUrl = (() => {
    if (this.cacheGet<string>("apiUrl")) {
      return this.cacheGet<string>("apiUrl")!;
    }
    return this.isEnv("develop")
      ? "http://127.0.0.1:8080/api/client"
      : "https://api.flowerprice.top/api/client";
  })();

  static rpx2px(rpx: number) {
    return Math.round((rpx * this.windowInfo.screenWidth) / 750);
  }

  static px2rpx(px: number) {
    return Math.round((px * 750) / this.windowInfo.screenWidth);
  }

  static toast(title: string) {
    Taro.showToast({
      title,
      icon: "none",
    });
  }

  static isTabbarPath(path: string) {
    return (Taro.getApp().config.tabBar?.list || [])
      .map((item: any) => "/" + item.pagePath)
      .includes(path);
  }

  static routeTo(url: string, query = {}, type = "default") {
    if (!url) return;
    const [path, urlQueryString] = url.split("?");
    const urlQuery = this.qsParse(urlQueryString || "");
    for (const key in query) {
      if (query[key] === undefined) {
        delete query[key];
      }
    }
    query = { ...query, ...urlQuery };

    if (Object.keys(query).length > 0) {
      url = `${path}?${this.qsStringify(query)}`;
    }

    // const stackUrls = Taro.getCurrentPages().map((item) => '/' + item.route);

    // const indexInStackUrls = stackUrls.findIndex((item) => item === path);
    // if (indexInStackUrls > -1) {
    //   const goBackNum = stackUrls.length - indexInStackUrls;
    //   if (goBackNum > 0) {
    //     Taro.navigateBack({ delta: goBackNum });
    //   }
    // }

    const isTabbar = this.isTabbarPath(path);

    if (isTabbar) {
      if (Object.keys(query).length > 0) {
        this.cacheSet("switchTabQuery", { query, path });
      }
      Taro.switchTab({ url: path });
    } else {
      const newUrl =
        path +
        (Object.keys(query).length > 0 ? `?${this.qsStringify(query)}` : "");
      const func = {
        default: Taro.navigateTo,
        relaunch: Taro.reLaunch,
        redirect: Taro.redirectTo,
      }[type];
      func && func({ url: newUrl });
    }
  }

  static getQuery<T = Record<string, string>>(): T {
    const currentPath = this.getCurrentPath();
    const isTabbar = this.isTabbarPath(currentPath);
    let query: T = {} as T;
    if (isTabbar) {
      const switchTabQuery = this.cacheGet<{ query: T; path: string }>(
        "switchTabQuery"
      );
      if (switchTabQuery && switchTabQuery.path === currentPath) {
        query = switchTabQuery.query;
      }
    } else {
      const { router } = Taro.getCurrentInstance();
      query = (router?.params || {}) as T;
      delete (query as any).$taroTimestamp;
    }
    const tranString = (obj: any) => {
      Object.keys(obj).forEach((key) => {
        const value = obj[key];
        if (typeof value === "string") {
          try {
            obj[key] = decodeURIComponent(value);
          } catch (e) {}
        } else if (typeof value === "object") {
          tranString(value);
        }
      });
    };
    tranString(query);
    return query;
  }

  static qsStringify(params: Record<string, string | number>) {
    return Object.keys(params)
      .filter((key) => params[key] !== undefined && params[key] !== null)
      .map((key) => `${key}=${params[key]}`)
      .join("&");
  }

  static qsParse(str: string) {
    const params = {};
    const pairs = str.split("&");
    pairs.forEach((pair) => {
      const [key, value] = pair.split("=");
      params[key] = value;
    });
    return params;
  }

  static backStack: { path: string; query: Record<string, string> }[] = [];

  static backPush(options?: { path?: string; query?: Record<string, string> }) {
    const path = options?.path || this.getCurrentPath() || "";
    const query = options?.query || this.getQuery() || {};
    this.backStack.push({ path, query });
  }

  static back() {
    const current = this.backStack.pop();
    const last = this.backStack.pop();
    if (current && last && current.path === last.path) {
      console.log("current path back", last?.query);
      $currentBackQuery.set({ path: last.path, query: last.query });
      return;
    }
    if (last) {
      this.routeTo(last.path, last.query);
      return;
    }
    $currentBackQuery.set({
      path: "/pages/tabs/market",
      query: { flowerId: "root" },
    });
  }

  static getCurrentPath() {
    return Taro.getCurrentInstance().router?.path || "";
  }
  static safeBottom = (() => {
    try {
      const res = Taro.getWindowInfo();
      return Math.max(
        0,
        Math.min(res.screenHeight - (res.safeArea?.bottom || 0), 60)
      );
    } catch (error) {
      return 0;
    }
  })();

  /**
   * 等待指定时间
   * @param ms 等待时间，单位毫秒
   * @returns Promise
   */
  static waitTime = async (ms: number) => {
    return new Promise((resolve) => setTimeout(resolve, ms));
  };
  /** 生成GUID */
  static guid() {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0,
          v = c == "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      }
    );
  }

  /** base64 编码 */
  static base64Encode = (str: string) => {
    let c1: number, c2: number, c3: number;
    const base64EncodeChars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    let i = 0,
      len = str.length,
      string = "";

    while (i < len) {
      c1 = str.charCodeAt(i++) & 0xff;
      if (i == len) {
        string += base64EncodeChars.charAt(c1 >> 2);
        string += base64EncodeChars.charAt((c1 & 0x3) << 4);
        string += "==";
        break;
      }
      c2 = str.charCodeAt(i++);
      if (i == len) {
        string += base64EncodeChars.charAt(c1 >> 2);
        string += base64EncodeChars.charAt(
          ((c1 & 0x3) << 4) | ((c2 & 0xf0) >> 4)
        );
        string += base64EncodeChars.charAt((c2 & 0xf) << 2);
        string += "=";
        break;
      }
      c3 = str.charCodeAt(i++);
      string += base64EncodeChars.charAt(c1 >> 2);
      string += base64EncodeChars.charAt(
        ((c1 & 0x3) << 4) | ((c2 & 0xf0) >> 4)
      );
      string += base64EncodeChars.charAt(
        ((c2 & 0xf) << 2) | ((c3 & 0xc0) >> 6)
      );
      string += base64EncodeChars.charAt(c3 & 0x3f);
    }
    return string;
  };

  static cacheGet<T>(key: string) {
    const cache = Taro.getStorageSync(key);
    const expireTime = Taro.getStorageSync(key + "_expire_");
    if (expireTime && Date.now() > expireTime) {
      return null;
    }
    return cache as T;
  }

  static cacheSet(key: string, value: any, expireDiffMs: number = 0) {
    Taro.setStorageSync(key, value);
    if (expireDiffMs) {
      Taro.setStorageSync(key + "_expire_", Date.now() + expireDiffMs);
    }
  }

  static cacheRemove(key: string) {
    Taro.removeStorageSync(key);
    Taro.removeStorageSync(key + "_expire_");
  }

  static sourceUrl(url: string) {
    url = url || "";
    if (url.startsWith("http")) {
      return url;
    }
    if (url.startsWith("/api")) {
      url = url.slice(0, 4);
    }
    return `${this.apiUrl || ""}${url}`;
  }

  static richTextTransform(html: string) {
    return html.replace(/src\s*=\s*['"]?([^'"]+)['"]?/gi, (match, p1) => {
      console.log(p1);
      return `src="${this.sourceUrl(p1)}"`;
    });
  }

  static formatDateTime(
    date: string,
    format: string,
    defaultValue: string = "--"
  ) {
    if (!date) return defaultValue;
    return dayjs(date).format(format);
  }

  /**
   * 格式化日期
   * @param date
   * @returns
   */
  static formatDay(date: string) {
    return this.formatDateTime(date, "YYYY-MM-DD");
  }

  /**
   * 格式化日期时间
   * @param date
   * @returns
   */
  static formatDayTime(date: string) {
    return this.formatDateTime(date, "YYYY-MM-DD HH:mm:ss");
  }

  /**
   * 格式化价格
   * @param amountCent
   * @returns
   */
  static formatPrice(amountCent: number) {
    return (amountCent / 100).toFixed(2);
  }

  /**
   * 变化百分比
   */
  static diffPercent(oldValue?: number, newValue?: number) {
    if (!oldValue || !newValue) return "--";
    const diff = ((newValue - oldValue) / oldValue) * 100;
    return diff > 0 ? `+${diff.toFixed(1)}%` : `${diff.toFixed(1)}%`;
  }

  static diffIcon(
    oldValue?: number,
    newValue?: number
  ): {
    name: TIconfontProps["name"];
    color: string;
  } {
    if (!oldValue || !newValue)
      return {
        name: "",
        color: "",
      };
    return newValue > oldValue
      ? {
          name: "rise",
          color: "#ff0000",
        }
      : {
          name: "fall",
          color: "#00ff00",
        };
  }

  static getTemp(diffPrecent?: number) {
    const tempTextArr = ["平", "冻", "寒", "凉", "温", "热", "沸"];
    const tempColorArr = [
      "#FFCA28",
      "#024F9F",
      "#0095BD",
      "#009F3B",
      "#F29102",
      "#ED5E03",
      "#E60211",
    ];
    if (diffPrecent === undefined || diffPrecent === null)
      return {
        text: "",
        color: "",
      };
    if (diffPrecent === 0)
      return {
        text: tempTextArr[0],
        color: tempColorArr[0],
      };
    const tempArr = [-Infinity, -100, -50, 0, 50, 100, Infinity]; // 调整区间
    const index = tempArr.findIndex((item) => item > diffPrecent);
    return {
      text: tempTextArr[index],
      color: tempColorArr[index],
    };
  }

  static previewImage(urls: string[]) {
    Taro.previewImage({
      urls,
    });
  }

  /**
   * 支付并监听支付状态
   * @param payParams 支付参数
   * @returns Promise<boolean> 支付是否成功
   */
  static async payAndListenStatus(params: TWxPayParamsDto): Promise<boolean> {
    return new Promise(async (resolve, reject) => {
      try {
        const { orderId, billId, paid, ...payParams } = params;
        // 如果已经支付，直接返回成功
        if (paid) {
          resolve(true);
          return;
        }

        // 支付成功后，连接WebSocket监听支付状态

        const wsUrl = `${Utils.apiUrl
          .replace("https://", "wss://")
          .replace("http://", "ws://")
          .replace(
            "/api/client",
            "/ws/client"
          )}/bill/listenStatus?billId=${billId}`;

        const socketTask = await Taro.connectSocket({
          url: wsUrl,
          header: {
            Authorization: `Bearer ${HttpUtils.token}`,
          },
        });

        // 设置超时
        const timeout = setTimeout(() => {
          socketTask.close({});
          reject(new Error("支付状态监听超时"));
        }, 300 * 1000); // 300秒超时

        socketTask.onOpen(() => {
          console.log("WebSocket连接已打开");
        });

        socketTask.onMessage((event) => {
          try {
            const message = JSON.parse(event.data as string);
            console.log("收到WebSocket消息:", message);

            if (message.type === "connected") {
              console.log("WebSocket连接确认:", message.message);
            } else if (message.type === "paymentSuccess") {
              console.log("支付成功:", message.message);
              clearTimeout(timeout);
              socketTask.close({});
              resolve(true);
            }
          } catch (error) {
            console.error("解析WebSocket消息失败:", error);
          }
        });

        socketTask.onError((error) => {
          console.error("WebSocket错误:", error);
          clearTimeout(timeout);
          reject(new Error("支付状态监听失败"));
        });

        socketTask.onClose(() => {
          console.log("WebSocket连接已关闭");
          clearTimeout(timeout);
        });
        try {
          await Taro.requestPayment(payParams as any);
        } catch (e) {
          apiCloseBill(billId || "", { reason: "用户取消支付" });
          socketTask.close({});
          throw e;
        }
        // 调用微信支付
      } catch (error) {
        console.error("支付失败:", error);

        reject(error);
      }
    });
  }

  static showDevTools() {
    const apiUrls = [
      { name: "正式环境", value: "https://api.flowerprice.top/api/client" },
      { name: "测试环境", value: "https://api-dev.flowerprice.top/api/client" },
      { name: "开发环境", value: "http://127.0.0.1:8080/api/client" },
    ];
    const names = apiUrls.map((item) => {
      return Utils.apiUrl === item.value ? `${item.name} ✅` : `${item.name}`;
    });
    Taro.showActionSheet({
      itemList: names,
      success: (res) => {
        Utils.apiUrl = apiUrls[res.tapIndex].value;
        Utils.cacheSet("apiUrl", apiUrls[res.tapIndex].value);
        Taro.showToast({
          title: "切换成功，请重启小程序",
          icon: "none",
        });
      },
      fail: () => {},
    });
  }
}
