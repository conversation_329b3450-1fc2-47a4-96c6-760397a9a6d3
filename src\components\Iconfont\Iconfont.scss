@font-face {
  font-family: "iconfont"; /* Project id 4909549 */
  src: url('./iconfont.ttf') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
}

.icon-feedback:before { content: "\e78c"; }
.icon-order:before { content: "\e7d3"; }
.icon-wallet:before { content: "\e798"; }
.icon-bell:before { content: "\e7c4"; }
.icon-top:before { content: "\e6a9"; }
.icon-share:before { content: "\e636"; }
.icon-doubledown:before { content: "\e7ed"; }
.icon-vip:before { content: "\e727"; }
.icon-empty:before { content: "\e621"; }
.icon-loading:before { content: "\e644"; }
.icon-plus:before { content: "\e8fe"; }
.icon-clear:before { content: "\e610"; }
.icon-edit-square:before { content: "\e791"; }
.icon-setting:before { content: "\e78e"; }
.icon-mine-fill:before { content: "\e663"; }
.icon-mine:before { content: "\e664"; }
.icon-check:before { content: "\e7fc"; }
.icon-close:before { content: "\e740"; }
.icon-sort-up:before { content: "\e65b"; }
.icon-sort:before { content: "\e60d"; }
.icon-sort-down:before { content: "\e86b"; }
.icon-swap:before { content: "\e7f4"; }
.icon-search:before { content: "\e840"; }
.icon-question-circle:before { content: "\e782"; }
.icon-warning-circle:before { content: "\e785"; }
.icon-star:before { content: "\e7df"; }
.icon-fall:before { content: "\e7f2"; }
.icon-rise:before { content: "\e7f3"; }
.icon-star-fill:before { content: "\e86a"; }
.icon-calculator:before { content: "\e799"; }
.icon-right:before { content: "\e7eb"; }
.icon-arrowleft:before { content: "\e7ef"; }
.icon-calculator-fill:before { content: "\e84d"; }
.icon-funds:before { content: "\e7a0"; }
.icon-funds-fill:before { content: "\e684"; }
