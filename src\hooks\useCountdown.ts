import dayjs from "dayjs"
import { createEffect, createSignal } from "solid-js"

export const useCountdown = (endTime: string, options?: {
  endText?: string,
  onEnd?: () => void
}) => {
  const { endText = '', onEnd = () => { } } = options || {}

  const [cdText, setCdText] = createSignal(endText)

  createEffect(() => {
    let interval: any
    interval = setInterval(() => {
      const end = dayjs(endTime)
      const now = dayjs()
      const diff = end.diff(now)

      if (diff <= 0) {
        setCdText(endText)
        onEnd()
        clearInterval(interval)
        return
      }
      const minutes = Math.floor(diff / 1000 / 60).toString().padStart(2, '0')
      const seconds = Math.floor((diff / 1000) % 60).toString().padStart(2, '0')
      const cd = `${minutes}:${seconds}`
      setCdText(cd)
    }, 1000)
    return () => clearInterval(interval)
  })

  return cdText as unknown as string
}