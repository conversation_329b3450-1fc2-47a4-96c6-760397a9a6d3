import { View, Text } from '@tarojs/components';
import { Component } from 'solid-js';
import { Iconfont } from '../Iconfont';

interface EmptyProps {
  description?: string;
  className?: string;
}

export const Empty: Component<EmptyProps> = (props) => {
  const description = props.description || '暂无数据';
  
  return (
    <View class={`flex flex-col items-center justify-center gap-2 py-12 ${props.className || ''}`}>
      <Iconfont 
        name='empty'
        size={100}
        class="opacity-50"
      />
      <Text class="text-sm opacity-50">{description}</Text>
    </View>
  );
};

export default Empty;
