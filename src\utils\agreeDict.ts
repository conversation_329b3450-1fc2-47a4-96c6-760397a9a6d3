export type TAgreeOption = {
  value: string;
  label: string;
  data?: any; // 额外数据
  children?: TAgreeOption[];
};

export type TAgreeMapOption = TAgreeOption & {
  parentValues: string[];
  childrenValues: string[];
  upValues: string[];
  downValues: string[];
};

export type TAgreeDict = {
  name: string;
  options: TAgreeOption[];
  labels: string[];
  values: string[];
  valueLabelMap: Record<string, string>;
  labelValueMap: Record<string, string>;
  valueDataMap: {
    [key: string]: TAgreeMapOption;
  };
  labelDataMap: {
    [key: string]: TAgreeMapOption;
  };
  match(value: string | number, ...labels: string[]): boolean;
};

export type TBaseAgreeDicts = typeof BaseAgreeDicts;

export type TAgreeDicts = {
  [key in keyof TBaseAgreeDicts]: TAgreeDict;
};



export const BaseAgreeDicts = {
  Feedback_status: {
    name: '反馈状态',
    options: [
      { value: 'PENDING', label: '待处理', data: { color: 'orange' } },
      { value: 'PROCESSED', label: '已处理', data: { color: 'green' } },
    ],
  },
  VipFee_status: {
    name: '会员费状态',
    options: [
      { value: 'WAIT_PAY', label: '待支付', data: { color: 'orange' } },
      { value: 'PAID', label: '已支付', data: { color: 'green' } },
      { value: 'CLOSED', label: '已关闭', data: { color: 'gray' } },
    ],
  },

  ClientUserPointsRecord_type: {
    name: '积分记录类型',
    options: [
      { value: 'REFERRAL_COMMISSION', label: '推荐返佣', data: { color: 'orange' } },
      { value: 'MANUAL_ADJUSTMENT', label: '客服手动', data: { color: 'blue' } },
      { value: 'CONSUMPTION', label: '消费扣减', data: { color: 'red' } },
      { value: 'REFUND', label: '退款返还', data: { color: 'green' } },
    ],
  },

};

function handleBaseAgreeDicts() {
  const result = {} as Record<string, TAgreeMapOption>;
  function tranToValueDataMap(treeOptions: TAgreeOption[], parentValues: string[] = [], parentChildrenValues: string[] = []) {
    let result: Record<string, TAgreeMapOption> = {};
    for (const item of treeOptions) {
      parentChildrenValues.push(item.value);
      const childrenValues: string[] = [];
      if (item.children) {
        const childrenMap = tranToValueDataMap(item.children, [...parentValues, item.value], childrenValues);
        result = { ...result, ...childrenMap };
        parentChildrenValues.push(...childrenValues);
      }
      result[item.value] = {
        ...item,
        parentValues: [...parentValues],
        childrenValues,
        upValues: [...parentValues, item.value],
        downValues: [item.value, ...childrenValues],
      };
    }
    return result as Record<string, TAgreeMapOption>;
  }
  for (const key in BaseAgreeDicts) {
    const valueDataMap = tranToValueDataMap((BaseAgreeDicts as any)[key].options);
    (result as any)[key] = {
      name: (BaseAgreeDicts as any)[key].name,
      options: (BaseAgreeDicts as any)[key].options,
      values: Object.keys(valueDataMap),
      labels: Object.values(valueDataMap).map((item) => item.label),
      valueLabelMap: Object.values(valueDataMap).reduce((prev: any, item) => {
        return { ...prev, [item.value]: item.label };
      }, {}),
      labelValueMap: Object.values(valueDataMap).reduce((prev, item) => {
        return { ...prev, [item.label]: item.value };
      }, {}),
      valueDataMap,
      labelDataMap: Object.values(valueDataMap).reduce((prev, item) => {
        (prev as any)[item.label] = item;
        return prev;
      }, {}),
      match(value: string | number, ...labels: string[]) {
        return labels.some((label) => this.labelValueMap[label].toString() === value.toString());
      }
    };
  }
  return result as unknown as TAgreeDicts;
}

export const AgreeDicts = handleBaseAgreeDicts();


