module.exports = () => {
  return {
    postcssPlugin: 'postcss-root-to-page',
    Once(root) {
      if(process.env.TARO_PLATFORM !== 'mini') {
        return
      }
      root.walkRules(rule => {
        
        // 将 :root 选择器替换为 page
        if (rule.selector === ':root') {
          rule.selector = 'page';
        }
        // 也可以处理包含 :root 的复合选择器
        if (rule.selector.includes(':root')) {
          rule.selector = rule.selector.replace(/:root/g, 'page');
        }
      });
    }
  }
}
module.exports.postcss = true;