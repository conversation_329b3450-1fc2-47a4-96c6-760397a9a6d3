import { Textarea } from "@tarojs/components";
import { FormItemContext } from "./useMyForm";
import { useContext } from "solid-js";
import clsx from "clsx";

export interface TMyFormTextAreaProps {
  placeholder?: string
  class?: any
  style?: any
  disabled?: boolean
  bordered?: boolean
}

export function MyFormTextArea(props: TMyFormTextAreaProps) {
  const formItem = useContext(FormItemContext)

  const onInput = (e: any) => {
    let value = e.detail.value
    formItem.onChange(value)
  }
  return (
    <Textarea
      class={clsx({
        "py-2 w-full box-border": true,
        'border border-solid border-stone-200 dark:border-stone-700 rounded-lg px-2': props.bordered,
        "text-gray-400 bg-stone-100 dark:bg-stone-800": props.disabled
      }, props.class)}
      style={props.style}
      disabled={props.disabled}
      value={formItem.value() ? formItem.value().toString() : ''}
      onInput={onInput}
      placeholder={props.placeholder}
    />
  )
}