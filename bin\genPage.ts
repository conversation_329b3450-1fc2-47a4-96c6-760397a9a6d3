import path from 'path';
import fs from 'fs';
import _ from 'lodash';



async function main() {
  const isTest = process.argv[3] === 'test';

  const [subPageName, ...otherPagePath] = process.argv[2].split('/');
  const pageName = otherPagePath.pop();

  // 所有页面都放在主包中
  const pagePath = path.join('./src/pages', subPageName, ...otherPagePath);
  if (!fs.existsSync(pagePath)) {
    !isTest && fs.mkdirSync(pagePath, { recursive: true });
  }
  if (fs.existsSync(path.join(pagePath, `${pageName}.tsx`))) {
    throw new Error(`Page ${pageName} 已存在`);
  }
  const pageNameUpper = _.upperFirst(_.camelCase(pageName));

  const tsxFileContent = `import { BasePage } from '@/components/BasePage'

export default function ${pageNameUpper}Page() {
  
  return (
    <BasePage >
      ${pageNameUpper}
    </BasePage>
  )
}
`
  const tsxFilePath = path.join(pagePath, `${pageName}.tsx`);
  !isTest && fs.writeFileSync(tsxFilePath, tsxFileContent);

  const configFileContent = `export default definePageConfig({
  navigationBarTitleText: '${pageNameUpper}',
})`;
  const configFilePath = path.join(pagePath, `${pageName}.config.ts`);
  !isTest && fs.writeFileSync(configFilePath, configFileContent);

  console.log(`生成页面 ${pageName} 成功`)

  // 更新app.config.ts
  const appConfigPath = './src/app.config.ts';
  const appConfigContent = fs.readFileSync(appConfigPath, 'utf-8');

  // 构建新页面路径
  const newPagePath = `pages/${subPageName}${otherPagePath.length ? '/' + otherPagePath.join('/') : ''}/${pageName}`;

  // 更新DEV_ENTRY_PAGE_PATH
  let updatedContent = appConfigContent.replace(
    /\n(const DEV_ENTRY_PAGE_PATH = '.*?')/,
    `\nconst DEV_ENTRY_PAGE_PATH = '${newPagePath}'\n// $1`
  );

  // 在主包pages中添加页面
  const pagesRegex = /pages:\s*\[([\s\S]*?)\](?=,|\s*\/\/|\s*$)/;
  const match = updatedContent.match(pagesRegex);
  
  if (match) {
    const currentPages = match[1].trim();
    const newPages = currentPages 
      ? `${currentPages}\n    '${newPagePath}',`
      : `'${newPagePath}',`;
    updatedContent = updatedContent.replace(pagesRegex, `pages: [${newPages}\n  ]`);
  }

  !isTest && fs.writeFileSync(appConfigPath, updatedContent);
  console.log(`更新 app.config.ts 成功`);

}

main()
