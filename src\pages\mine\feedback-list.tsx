import { BasePage } from '@/components/BasePage';
import { View, ScrollView, Image } from '@tarojs/components';
import { useDidShow } from '@tarojs/taro';
import { createSignal } from 'solid-js';
import Taro from '@tarojs/taro';
import { apiFeedbackMyPageList } from '@/apis/apis';
import dayjs from 'dayjs';
import Loading from '@/components/Loading';
import Empty from '@/components/Empty';
import { Utils } from '@/utils/utils';
import { MyButton } from '@/components/MyButton';
import { MyNavbar } from '@/components/MyNavbar';
import clsx from 'clsx';

const statusMap = {
  PENDING: '待回复',
  PROCESSED: '已回复',
} as const;

export default function FeedbackListPage() {
  const [list, setList] = createSignal<TFeedback[]>([]);
  const [loading, setLoading] = createSignal(false);
  const [finished, setFinished] = createSignal(false);
  const [pageIndex, setPageIndex] = createSignal(0);
  const pageSize = 10;

  const loadData = async (isRefresh = false) => {
    if (loading()) return;

    const currentPage = isRefresh ? 0 : pageIndex();

    try {
      setLoading(true);
      const res = await apiFeedbackMyPageList({
        pageIndex: currentPage,
        pageSize,
      });

      if (isRefresh) {
        setList(res.rows || []);
      } else {
        setList(prev => [...prev, ...(res.rows || [])]);
      }

      setPageIndex(currentPage + 1);
      setFinished(currentPage + 1 >= res.totalPageCount);
    } catch (error) {
      console.error('获取反馈列表失败', error);
      Taro.showToast({
        title: '获取反馈列表失败',
        icon: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  // useLoad(() => {
  //   loadData(true);
  // });

  useDidShow(() => {
    loadData(true);
  });


  const handleCreate = () => {
    Utils.routeTo('/pages/mine/feedback-edit');
  };

  const handleScrollToLower = () => {
    if (!loading() && !finished()) {
      loadData();
    }
  };

  return (
    <BasePage>
      <MyNavbar title='我的反馈' />
      <ScrollView
        scrollY
        style={{ height: `${Utils.pageSize.tabPageHeight - 8}px` }}
        onScrollToLower={handleScrollToLower}
        lowerThreshold={50}
      >
        <View class='px-4 box-border'>
          {list().length > 0 ? (
            <View class='flex flex-col gap-2'>
              {list().map(item => (
                <View class='bg-white dark:bg-stone-900 rounded p-4 flex flex-col gap-4'>
                  <View class='flex items-center justify-between'>
                    <View class='text-xs text-gray-400'>
                      {dayjs(item.createdAt).format('YYYY-MM-DD HH:mm')}
                    </View>
                    <View
                      class={clsx({
                        'text-blue-500': item.status === 'PENDING',
                        'text-green-500': item.status === 'PROCESSED',
                      })}
                    >
                      {statusMap[item.status] || '未知状态'}
                    </View>
                  </View>
                  <View class='text-sm line-clamp-2'>{item.content}</View>
                  {(item.attachments || []).length > 0 && (
                    <View class='flex gap-1 flex-wrap'>
                      {(item.attachments || []).map((url) => (
                        <Image
                          src={url}
                          mode='aspectFill'
                          class='w-16 h-16 rounded-md'
                          onClick={() => Utils.previewImage([url])}
                        />
                      ))}
                    </View>
                  )}
                  {item.reply && (
                    <View class='text-sm line-clamp-2 bg-gray-100 dark:bg-zinc-800 p-2 rounded'>回复：{item.reply}</View>
                  )}

                </View>
              ))}

              {loading() && list().length > 0 && (
                <View class='py-3 flex justify-center'>
                  <Loading />
                </View>
              )}

              {!loading() && list().length > 0 && finished() && (
                <View class='py-3 text-center text-sm text-gray-400'>没有更多了</View>
              )}
            </View>
          ) : !loading() ? (
            <Empty description='暂无数据记录' />
          ) : null}
        </View>
      </ScrollView>
      <View style={{ height: `${Utils.pageSize.tabbarHeight + Utils.pageSize.safeBottom}px` }} class='fixed bottom-0 left-0 right-0 bg-white dark:bg-stone-900 px-4 pt-2'>
        <MyButton
          type='primary'
          block
          onClick={handleCreate}
        >
          新建反馈
        </MyButton>
      </View>
    </BasePage>
  );
}

