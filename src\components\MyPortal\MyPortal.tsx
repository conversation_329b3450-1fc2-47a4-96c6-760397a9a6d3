
import { JSXElement, Show, useContext } from "solid-js"
import { BasePageContext } from "../BasePage"
import { Portal } from "solid-js/web"

export interface TMyPortalProps {
  children: JSXElement
}

export const MyPortal = (props: TMyPortalProps) => {

  const rootPortalRef = useContext(BasePageContext)

  return (
    <Show when={rootPortalRef()}>
      <Portal mount={rootPortalRef()}>
        {props.children}
      </Portal>
    </Show>
  )
}