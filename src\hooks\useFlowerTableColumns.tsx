// import { apiAddFavorite, apiRemoveFavorite } from "@/apis/apis"
import { Iconfont } from "@/components/Iconfont"
import { TMyTableColumn } from "@/components/MyTable"
import { NumDiff } from "@/components/NumDiff"
import { $favoriteFlowerIdsSet, $isDarkTheme, toggleFavoriteFlower } from "@/store/appStore"
import { Utils } from "@/utils/utils"
import { useStore } from "@nanostores/solid"
import { View, Text } from "@tarojs/components"

export const useFlowerTableColumns = ({
  openFlower,
}: {
  openFlower: (flowerId: string) => void
}) => {

  const isDarkTheme = useStore($isDarkTheme)

  const favoriteFlowerIdsSet = useStore($favoriteFlowerIdsSet)

  return [
    { title: <Text class="pl-3">品种</Text>, dataIndex: "name", width: 200, render: (record: TFlower) => <View class='w-full underline pl-3' onClick={() => openFlower(record.id)} >{record.name}</View> },
    {
      title: "温度", dataIndex: "latestFlowerMarket.day7DiffPercent", align: "center", width: 100, render: (record: TFlower) => {
        const temp = Utils.getTemp(record.latestFlowerMarket?.day7DiffPercent)
        if (!temp.text) return "--"
        return (
          <View class="flex items-center justify-center">
            <View class="text-white px-2 py-1 rounded-md" style={{ 'background-color': temp.color + '22', color: temp.color, 'font-weight': isDarkTheme() ? 'bold' : 'normal' }}>
              {temp.text}
            </View>
          </View>
        )
      }
    },
    {
      title: "价格", dataIndex: "latestFlowerMarket.price", align: "right", sortable: true, width: 100
    },
    {
      title: "周涨幅", dataIndex: "latestFlowerMarket.day7DiffPercent", sortable: true, align: "right", render: (record) => {
        return <NumDiff oldValue={record.latestFlowerMarket?.day7Price} newValue={record.latestFlowerMarket?.price} />
      }, width: 150
    },
    {
      title: "强度", dataIndex: "latestFlowerMarket.strength", align: "right", sortable: true, width: 100
    },
    {
      title: "收藏", width: 100, align: "center", render: (record) => (
        <View class="h-full flex items-center justify-center -my-2"
          style={{ color: favoriteFlowerIdsSet().has(record.id) ? '#22c55e' : 'inherit' }}
          onClick={() => toggleFavoriteFlower(record.id)}>
          <Iconfont size={36} name={favoriteFlowerIdsSet().has(record.id) ? "star-fill" : "star"} />
        </View>
      )
    },
  ] as TMyTableColumn[]
}
