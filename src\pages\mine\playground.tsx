import { BasePage } from '@/components/BasePage'
import { MyButton } from '@/components/MyButton'
import { HttpUtils } from '@/utils/http'
import { View } from '@tarojs/components'
import Taro from '@tarojs/taro'

export default function PlaygroundPage() {

  const urls = [
    'wss://api-debug.flowerprice.top',
    // 'wss://api-dev.flowerprice.top',
    // 'wss://192.168.0.178:8888',
    // 'ws://192.168.0.178:8889',
    // 'ws://192.168.0.178:8080',
  ]
  return (
    <BasePage >
      <View class='flex flex-col gap-4 p-4 '>
        {urls.map((url, index) => (
          <MyButton className='w-full' key={index} onClick={async () => {

            const socketTask = Taro.connectSocket({
              url: url + '/ws/client/bill/listenStatus?billId=01K078YJ36KEJX26D84XET2STD',
              header: {
                'Authorization': `Bearer ${HttpUtils.token}`,
              },
              success: (res) => {
                console.log("WebSocket连接已打开", res);
              },
              fail: (err) => {
                console.log("WebSocket连接失败", err);
              },
              complete: (res) => {
                console.log("WebSocket连接完成", res);
              },
              // header: {
              //   Authorization: `Bearer ${HttpUtils.token}`,
              // },
            });
            socketTask.then((socketTask) => {
              socketTask.onOpen(() => {
                console.log("WebSocket连接已打开");
              });
              socketTask.onMessage((event) => {
                try {
                  const message = JSON.parse(event.data as string);
                  console.log("收到WebSocket消息:", message);
                } catch (error) {
                  console.error("解析WebSocket消息失败:", error);
                }
              });
              socketTask.onError((error) => {
                console.error("WebSocket错误:", error);
              });
              socketTask.onClose(() => {
                console.log("WebSocket连接已关闭");
              });
            });
          }}>{url}</MyButton>
        ))}
      </View>
    </BasePage>
  )
}
