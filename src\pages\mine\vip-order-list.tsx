import { View, Text, Button } from "@tarojs/components";
import { createSignal, onMount, For, Show, createMemo } from "solid-js";
import { BasePage } from "@/components/BasePage";
import { MyNavbar } from "@/components/MyNavbar";
import { Utils } from "@/utils/utils";
import { apiVipFeeOrderClose, apiVipFeeOrderMyPageList, apiVipFeeOrderPay } from "@/apis/apis";
import { SafeBottom } from "@/components/SafeBottom";
import Empty from "@/components/Empty";
import { usePullDownRefresh, useReachBottom } from "@tarojs/taro";
import Taro from "@tarojs/taro";
import dayjs from "dayjs";
import { AgreeDicts } from "@/utils/agreeDict";
import { produce } from 'immer'
import { useCountdown } from "@/hooks/useCountdown";
import { $isDarkTheme, login } from "@/store/appStore";
import { useStore } from "@nanostores/solid";

export default function VipOrderList() {
  const [orders, setOrders] = createSignal<TVipFeeOrder[]>([]);
  const [loading, setLoading] = createSignal(false);
  const [hasMore, setHasMore] = createSignal(true);
  const [pageIndex, setPageIndex] = createSignal(0);
  const [payingId, setPayingId] = createSignal<string>('');
  const isDarkTheme = useStore($isDarkTheme)

  const displayOrders = createMemo(() => {
    return orders().map(item => {
      return {
        item,
        id: item.id,
        title: item.vipFeeTypeName,
        statusText: AgreeDicts.VipFee_status.valueLabelMap[item.status],
        statusColor: AgreeDicts.VipFee_status.valueDataMap[item.status]?.data?.color || '#000',
        infos: [
          { title: '时长', value: item.vipFeeTypeDays + '天', show: true },
          { title: '金额', value: <Text class="text-green-500 font-bold">¥{Utils.formatPrice(item.amountCent)}</Text>, show: true },
          { title: '创建时间', value: Utils.formatDayTime(item.createdAt), show: true },
          { title: '自动关闭时间', value: Utils.formatDayTime(item.autoCloseTime!), show: item.status !== 'PAID' },
          { title: '生效开始时间', value: Utils.formatDayTime(item.startTime!), show: item.status === 'PAID' },
          { title: '生效结束时间', value: Utils.formatDayTime(item.endTime!), show: item.status === 'PAID' },
        ].filter(item => item.show),
        actions: [
          {
            text: <Text>{useCountdown(item.autoCloseTime!, {
              onEnd: () => {
                if(item.status !== 'WAIT_PAY') return;
                const index = orders().findIndex(order => order.id === item.id)
                if (index > -1) {
                  setOrders(produce(orders(), draft => {
                    draft[index].status = 'CLOSED'
                  }))
                  apiVipFeeOrderClose(item.id)
                }
              }
            })} 支付</Text>,
            loading: payingId() === item.id,
            disabled: !!payingId(),
            onClick: () => handlePay(item),
            show: item.status === 'WAIT_PAY' && dayjs(item.autoCloseTime!).isAfter(dayjs())
          }
        ].filter(item => item.show),
      }
    });
  });

  // 加载订单列表
  const loadOrders = async (refresh = false) => {
    if (loading()) return;

    try {
      setLoading(true);
      const currentPage = refresh ? 0 : pageIndex();

      const data = await apiVipFeeOrderMyPageList({
        pageIndex: currentPage,
        pageSize: 10
      });

      if (refresh) {
        setOrders(data.rows);
        setPageIndex(1);
      } else {
        setOrders(prev => [...prev, ...data.rows]);
      }

      setHasMore(currentPage + 1 < data.totalPageCount);
      setPageIndex(currentPage + 1);
    } catch (error) {
      Utils.toast('加载失败');
    } finally {
      setLoading(false);
    }
  };

  // 支付订单
  const handlePay = async (order: TVipFeeOrder) => {
    if (payingId()) return;

    try {
      setPayingId(order.id);

      // 获取支付参数
      const payParams = await apiVipFeeOrderPay({ id: order.id });

      // 调用支付并监听状态
      const success = await Utils.payAndListenStatus(payParams);

      if (success) {
        Utils.toast('支付成功');
        // 刷新订单列表
        loadOrders(true);
        login()
      }
    } catch (error: any) {
      Utils.toast(error.message || '支付失败');
    } finally {
      setPayingId('');
    }
  };

  // 查看订单详情
  const viewDetail = (id: string) => {
    Utils.routeTo('/pages/mine/vip-order-detail', { id });
  };


  onMount(() => {
    loadOrders(true);
  });

  usePullDownRefresh(async () => {
    setLoading(false);
    await loadOrders(true);
    Taro.stopPullDownRefresh();
  });

  useReachBottom(() => {
    console.log('上拉加载');
    if (hasMore() && !loading()) {
      loadOrders();
    }
  });

  return (
    <BasePage>
      <MyNavbar title="会员费订单" />
      <View class="p-4 min-h-screen">
        <Show when={orders().length > 0} fallback={
          <Show when={!loading()}>
            <Empty description="暂无订单" />
          </Show>
        }>
          <View class="flex flex-col gap-4">
            <For each={displayOrders()}>
              {(item) => (
                <View class="bg-white dark:bg-stone-900 rounded-2 px-4 shadow text-28rpx" onClick={() => viewDetail(item.id)}>
                  <View class="flex justify-between items-center py-4" style={{ 'border-bottom': isDarkTheme() ? '1rpx solid #333' : '1rpx solid #eee' }}>
                    <View class="font-bold text-gray-800 dark:text-white">{item.title}</View>
                    <View style={{ color: item.statusColor }}>{item.statusText}</View>
                  </View>

                  <View class="flex flex-col gap-2 py-4 text-24rpx">
                    {item.infos.map((info, index) => (
                      <View class="flex justify-between items-center">
                        <View class="opacity-40 dark:text-gray-300 w-30">{info.title}</View>
                        <View>{info.value}</View>
                      </View>
                    ))}
                  </View>

                  <Show when={item.actions.length > 0} fallback={<View class="h-2" />}>
                    <View class="flex justify-end gap-4 py-4" style={{ 'border-top': isDarkTheme() ? '1rpx solid #333' : '1rpx solid #eee' }} onClick={(e) => e.stopPropagation()}>
                      {item.actions.map((action) => (
                        <Button
                          type="primary"
                          size="mini"
                          class="mx-0"
                          loading={action.loading}
                          onClick={action.onClick}
                          disabled={action.disabled}
                        >
                          {action.text}
                        </Button>
                      ))}
                    </View>

                  </Show>
                </View>
              )}
            </For>
          </View>

          <Show when={hasMore()}>
            <View class="flex justify-center items-center mt-4 opacity-50">
              <Text>{loading() ? '加载中...' : '加载更多'}</Text>
            </View>
          </Show>
        </Show>
      </View>
      <SafeBottom />
    </BasePage>
  );
}

