import Taro from '@tarojs/taro'
import { HttpUtils } from './http'
import { Utils } from './utils'
import { atom } from 'nanostores'

// 全局状态管理
export const $unreadMessageCount = atom<number>(0)
export const $latestMessage = atom<TClientMessage | undefined>(undefined)
export const $isWebSocketConnected = atom<boolean>(false)

export type TWebSocketMessage = {
  type: string;
  [key: string]: any;
}

export class MessageWebSocketService {
  private static instance: MessageWebSocketService
  private socketTask: Taro.SocketTask | null = null
  private reconnectTimer: NodeJS.Timeout | null = null
  private heartbeatTimer: NodeJS.Timeout | null = null
  private isConnecting = false
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 3000 // 3秒

  private constructor() {}

  static getInstance(): MessageWebSocketService {
    if (!MessageWebSocketService.instance) {
      MessageWebSocketService.instance = new MessageWebSocketService()
    }
    return MessageWebSocketService.instance
  }

  /**
   * 连接WebSocket
   */
  async connect(): Promise<void> {
    if (this.isConnecting || this.socketTask) {
      return
    }

    this.isConnecting = true

    try {
      const token = HttpUtils.token
      if (!token) {
        console.warn('WebSocket连接失败: 未找到token')
        this.isConnecting = false
        return
      }

      const wsUrl = `${Utils.apiUrl
        .replace('https://', 'wss://')
        .replace('http://', 'ws://')
        .replace('/api/client', '/ws/client')}/message`

      console.log('正在连接WebSocket:', wsUrl)

      this.socketTask = await Taro.connectSocket({
        url: wsUrl,
        header: {
          'Authorization': `Bearer ${token}`
        }
      })

      this.setupEventHandlers()
      this.isConnecting = false
      this.reconnectAttempts = 0

    } catch (error) {
      console.error('WebSocket连接失败:', error)
      this.isConnecting = false
      this.scheduleReconnect()
    }
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    if (!this.socketTask) return

    this.socketTask.onOpen(() => {
      console.log('WebSocket连接已打开')
      $isWebSocketConnected.set(true)
      this.startHeartbeat()
    })

    this.socketTask.onMessage((event) => {
      try {
        if(event.data === 'pong') return
        const message: TWebSocketMessage = JSON.parse(event.data as string)
        this.handleMessage(message)
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    })

    this.socketTask.onError((error) => {
      console.error('WebSocket错误:', error)
      $isWebSocketConnected.set(false)
      this.cleanup()
      this.scheduleReconnect()
    })

    this.socketTask.onClose(() => {
      console.log('WebSocket连接已关闭')
      $isWebSocketConnected.set(false)
      this.cleanup()
      this.scheduleReconnect()
    })
  }

  /**
   * 处理WebSocket消息
   */
  private handleMessage(message: TWebSocketMessage): void {
    console.log('收到WebSocket消息:', message)

    switch (message.type) {
      case 'connected':
        console.log('WebSocket连接确认:', message.message)
        break

      case 'newMessage':
        if (message.latestMessage) {
          $latestMessage.set(message.latestMessage)
          
          // 显示桌面通知
          this.showNotification(message.latestMessage)
        }
        if (message.unreadCount !== undefined) {
          $unreadMessageCount.set(message.unreadCount)
        }
        break

      case 'unreadCount':
        if (message.unreadCount !== undefined) {
          $unreadMessageCount.set(message.unreadCount)
        }
        break

      case 'latestMessage':
        if (message.latestMessage) {
          $latestMessage.set(message.latestMessage)
        }
        break

      default:
        console.warn('未知的WebSocket消息类型:', message.type)
    }
  }

  /**
   * 显示桌面通知
   */
  private showNotification(message: TClientMessage): void {
    // 只在小程序前台时显示通知
    // if (Taro.getAppBaseInfo().appState === 'active') {
      
    // }
    Taro.showToast({
        title: `新消息: ${message.title}`,
        icon: 'none',
        duration: 2000
      })
  }

  /**
   * 开始心跳
   */
  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.socketTask) {
        this.socketTask.send({
          data: 'ping'
        })
      }
    }, 30000) // 每30秒发送一次心跳
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('WebSocket重连次数已达上限，停止重连')
      return
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    this.reconnectTimer = setTimeout(() => {
      this.reconnectAttempts++
      console.log(`WebSocket重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`)
      this.connect()
    }, this.reconnectDelay)
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    this.stopHeartbeat()
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    this.socketTask = null
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    console.log('主动断开WebSocket连接')
    this.cleanup()
    if (this.socketTask) {
      this.socketTask.close({})
      this.socketTask = null
    }
    $isWebSocketConnected.set(false)
  }

  /**
   * 重置重连计数
   */
  resetReconnectAttempts(): void {
    this.reconnectAttempts = 0
  }

  /**
   * 获取连接状态
   */
  isConnected(): boolean {
    return $isWebSocketConnected.get()
  }
}

// 导出单例实例
export const messageWebSocket = MessageWebSocketService.getInstance()
