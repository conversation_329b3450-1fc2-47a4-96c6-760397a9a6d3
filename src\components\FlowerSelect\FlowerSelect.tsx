import { View, Text, Input, ScrollView } from "@tarojs/components"
import { JSXElement, mergeProps, createSignal, createEffect, For, Show } from "solid-js"
import { MyPopup } from "../MyPopup"
import { apiFlowerMarketChildrenPage, apiFlowerSearch } from "../../apis/apis"
import { Iconfont } from "../Iconfont"
import { useDebounce } from "../../hooks/useDebounce"

export interface TFlowerSelectProps {
  value?: TFlower;
  onChange: (value: TFlower) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  renderSelect?: (value: TFlower) => JSXElement;
  rootFlowerId?: string; // 根花朵ID，用于初始加载分类
}

export const FlowerSelect = (_props: TFlowerSelectProps) => {
  const props = mergeProps({
    placeholder: '请选择花朵',
    searchPlaceholder: '搜索花朵',
    rootFlowerId: 'root', // 默认根ID
  }, _props)


  // 状态管理
  const [popupOpen, setPopupOpen] = createSignal(false)
  const [searchText, setSearchText] = createSignal('')
  const [categories, setCategories] = createSignal<TFlower[]>([])
  const [flowers, setFlowers] = createSignal<TFlower[]>([])
  const [selectedFlower, setSelectedFlower] = createSignal<TFlower | null>(null)
  const [currentCategory, setCurrentCategory] = createSignal<TFlower | null>(null)
  const [isSearching, setIsSearching] = createSignal(false)
  const [searchResults, setSearchResults] = createSignal<TFlower[]>([])
  const [searchLoading, setSearchLoading] = createSignal(false)
  const [searchPageIndex, setSearchPageIndex] = createSignal(0)
  const [searchPageTotal, setSearchPageTotal] = createSignal(0)

  // 初始化加载分类
  const loadCategories = async () => {
    try {
      const res = await apiFlowerMarketChildrenPage(props.rootFlowerId!, {
        pageIndex: 0,
        pageSize: 1000,
      })
      setCategories(res.rows)
      if (res.rows.length > 0) {
        setCurrentCategory(res.rows[0])
        loadFlowers(res.rows[0].id)
      }
    } catch (error) {
      console.error('加载分类失败', error)
    }
  }

  // 加载特定分类下的花朵
  const loadFlowers = async (categoryId: string) => {
    try {
      const res = await apiFlowerMarketChildrenPage(categoryId, {
        pageIndex: 0,
        pageSize: 1000,
      })
      setFlowers(res.rows)
    } catch (error) {
      console.error('加载花朵失败', error)
    }
  }

  // 搜索花朵
  const searchFlowers = async (text: string) => {
    if (!text.trim()) {
      setIsSearching(false)
      setSearchResults([])
      return
    }

    setIsSearching(true)
    setSearchPageIndex(0) // 重置页码
    await baseSearchFlowers(text)
  }

  // 使用API搜索花卉数据
  const baseSearchFlowers = async (keyword: string) => {
    const pageSize = 20
    if (!keyword || keyword.length < 1 || (searchPageIndex() > 0 && searchPageIndex() + 1 > searchPageTotal())) return

    try {
      setSearchLoading(true)
      const response = await apiFlowerSearch({
        keyword,
        pageIndex: searchPageIndex(),
        pageSize: pageSize
      })

      // 如果是第一页，替换结果；否则追加结果
      if (searchPageIndex() === 0) {
        setSearchResults(response.rows)
      } else {
        setSearchResults([...searchResults(), ...response.rows])
      }

      setSearchPageTotal(response.totalPageCount)
    } catch (error) {
      console.error('搜索花朵失败', error)
    } finally {
      setSearchLoading(false)
    }
  }

  // 加载更多搜索结果
  const loadMoreSearchResults = () => {
    if (searchLoading() || searchPageIndex() + 1 >= searchPageTotal()) return
    setSearchPageIndex(searchPageIndex() + 1)
    baseSearchFlowers(searchText())
  }

  // 处理搜索输入变化
  const handleSearchChange = (e: any) => {
    const value = e.detail.value
    setSearchText(value)
    debouncedSearch(value)
  }

  // 防抖搜索
  const debouncedSearch = useDebounce((value: string) => {
    searchFlowers(value)
  }, 300)

  // 选择分类
  const handleCategorySelect = (category: TFlower) => {
    setCurrentCategory(category)
    loadFlowers(category.id)
  }

  // 选择花朵
  const selectFlower = (flower: TFlower) => {
    setSelectedFlower(flower)
  }

  // 确认选择
  const handleConfirm = () => {
    const selected = selectedFlower()
    if (selected) {
      props.onChange(selected)
    }
    setPopupOpen(false)
  }

  // 渲染选择器
  const renderDefaultSelect = () => {
    const value = props.value
    return (
      <View class="flex items-center justify-between p-2 border border-gray-300 dark:border-stone-600 rounded-lg bg-white dark:bg-stone-900 text-black dark:text-white">
        <Text class="flex-1 truncate">
          {value ? value.name : props.placeholder}
        </Text>
        <Iconfont name="right" size={24} class="transform rotate-90" />
      </View>
    )
  }

  // 检查花朵是否被选中
  const isFlowerSelected = (flower: TFlower) => {
    const selected = selectedFlower()
    return selected ? selected.id === flower.id : false
  }

  // 组件挂载时初始化
  createEffect(() => {
    if (popupOpen()) {
      loadCategories()
      // 如果已有选中值，设置为当前选中
      if (props.value) {
        setSelectedFlower(props.value)
      }
    }
  })

  return (
    <View class="FlowerSelect">
      <View onClick={() => setPopupOpen(true)}>
        {props.renderSelect ? props.renderSelect(props.value!) : renderDefaultSelect()}
      </View>

      <MyPopup
        open={popupOpen()}
        onOpenChange={setPopupOpen}
        btns="cancelAndOk"
        title="选择花朵"
        onOk={handleConfirm}
      >
        {/* 搜索框 */}
        <View class="p-3 box-border border-b border-gray-200 dark:border-stone-600 bg-white dark:bg-stone-900">
          <Input
            class="p-2 bg-gray-100 dark:bg-stone-600 rounded-lg text-black dark:text-white"
            placeholder={props.searchPlaceholder}
            value={searchText()}
            onInput={handleSearchChange}
          />
        </View>

        {/* 内容区域 */}
        <View class="flex bg-white dark:bg-stone-900 text-black dark:text-white" style={{ height: '60vh' }}>
          {/* 搜索模式下只显示搜索结果 */}
          <Show when={isSearching()} fallback={
            <>
              {/* 左侧分类列表 */}
              <View class="w-1/3 border-r border-gray-200 dark:border-stone-600 overflow-y-auto">
                <For each={categories()}>
                  {(category) => (
                    <View
                      class={`p-3 ${currentCategory()?.id === category.id ? 'bg-green-50  text-green-500 dark:bg-green-500 dark:text-white' : ''}`}
                      onClick={() => handleCategorySelect(category)}
                    >
                      <Text class="truncate">{category.name}</Text>
                    </View>
                  )}
                </For>
              </View>

              {/* 右侧花朵列表 */}
              <View class="w-2/3 overflow-y-auto">
                <For each={flowers()}>
                  {(flower) => (
                    <View
                      class={`p-3 flex items-center justify-between ${isFlowerSelected(flower) ? 'bg-green-50 dark:bg-green-500' : ''}`}
                      onClick={() => selectFlower(flower)}
                    >
                      <Text class="truncate">{flower.name}</Text>
                      <Show when={isFlowerSelected(flower)}>
                        <Iconfont name="check" size={24} class="text-green-500 dark:text-white" />
                      </Show>
                    </View>
                  )}
                </For>
              </View>
            </>
          }>
            {/* 搜索结果列表 */}
            <ScrollView
              class="w-full"
              scrollY
              style={{ height: '100%' }}
              onScrollToLower={loadMoreSearchResults}
              lowerThreshold={50}
            >
              <Show when={searchResults().length > 0} fallback={
                <View class="p-4 text-center text-gray-500 dark:text-gray-400">
                  {searchLoading() ? '搜索中...' : '无搜索结果'}
                </View>
              }>
                <For each={searchResults()}>
                  {(flower) => (
                    <View
                      class={`p-3 flex items-center justify-between ${isFlowerSelected(flower) ? 'bg-green-50 dark:bg-green-500/50' : ''}`}
                      onClick={() => selectFlower(flower)}
                    >
                      <View class="flex items-center">
                        <View class="w-6 shrink-0">
                          <Show when={isFlowerSelected(flower)}>
                            <Iconfont name="check" size={24} class="text-green-500 dark:text-white" />
                          </Show>
                        </View>
                        <Text class="truncate">{flower.name}</Text>
                      </View>
                      <Text class="op-50">{flower.parent?.name || '未分类'}</Text>

                    </View>
                  )}
                </For>

                {/* 加载状态提示 */}
                <Show when={searchLoading() && searchPageIndex() > 0}>
                  <View class="p-3 text-center text-gray-500 dark:text-gray-400">
                    加载中...
                  </View>
                </Show>
              </Show>
            </ScrollView>
          </Show>
        </View>

        {/* 底部已选择区域 */}
        <View class="p-3 border-t border-gray-200 dark:border-stone-600 bg-white dark:bg-stone-900 flex items-center">
          <Text class="text-gray-500 dark:text-gray-400">已选择：</Text>
          <Show when={selectedFlower()}>
            <View class="bg-green-50 text-green-500 dark:bg-green-500 dark:text-white px-2 py-1 rounded-lg flex items-center">
              {selectedFlower()!.name}
              {/* <View onClick={(e) => { e.stopPropagation(); setSelectedFlower(null); }}>
                  <Iconfont name="close" size={16} />
                </View> */}
            </View>
          </Show>
        </View>
      </MyPopup>
    </View>
  )
}
