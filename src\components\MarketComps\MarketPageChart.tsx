import { apiFlowerMarketChartData } from '@/apis/apis';
import { F2Canvas } from '@/components/F2Canvas';
import { View } from '@tarojs/components';
import { createEffect, createSignal, For } from 'solid-js';

interface MarketPageChartProps {
  flowerId: string
  batchTime: string
}

export function MarketPageChart(props: MarketPageChartProps) {
  const [cycle, setCycle] = createSignal('31')
  const [chartData, setChartData] = createSignal<TFlowerChartDto[]>([])

  const cycleList = [{
    value: '8',
    label: '近1周'
  }, {
    value: '31',
    label: '近1月'
  }]


  createEffect(async () => {
    console.log('chart flowerId', props.flowerId)
    const res = await apiFlowerMarketChartData(props.flowerId, Number(cycle()))
    setChartData(res)
  })

  

  const onChartInit = (F2: any, config: any) => {
    const showLegend = new Set(chartData().map(item => item.name)).size > 1
    const chart = new F2.Chart(config);
    const data = chartData().map((item) => ({
      value: item.price,
      name: item.name,
      date: item.date
    }))
    const max = Math.max(...data.map(item => item.value))
    // time: { type: 'timeCat', tickCount: 4, mask: 'DD日HH时', sortable: false },
    chart.source(data, {
      date: {
        // range: [0, 1],
        type: 'timeCat',
        mask: 'MM-DD',
        tickCount: 5,
      },
      value: {
        max: max,
        tickCount: 4
      }
    });
    chart.legend(showLegend ?{
      align: 'center',
      position: 'bottom',
      nameStyle: {
        fill: '#333333'
      },
      valueStyle: {
        fill: '#e8541e'
      }
    } : false);
    chart.tooltip({
      showTitle: true,
      layout: 'vertical',
      // custom: true, // 自定义 tooltip 内容框
      // crosshairsStyle: {
      //   stroke: '#108ee9',
      //   lineDash: [ 4, 4 ]
      // },
      onChange: function onChange(obj) {
        if (!showLegend) return
        const legend = chart.get('legendController').legends.bottom?.[0];
        const tooltipItems = obj.items;
        const legendItems = legend.items;
        const map = {};
        legendItems.forEach(function (item) {
          map[item.name] = { ...item };
        });
        tooltipItems.forEach(function (item) {
          const name = item.name;
          const value = item.value;
          if (map[name]) {
            map[name].value = value;
          }
        });
        legend.setItems(Object.values(map));
      },
      onHide: function onHide() {
        if (!showLegend) return
        const legend = chart.get('legendController').legends.bottom?.[0];
        legend.setItems(chart.getLegendItems().country);
      }
    });
    // chart.legend(false)
    // chart.tooltip({
    //   showTitle: true,
    //   layout: 'vertical'
    // });
    chart.line().position('date*value').color('name').adjust('stack');
    chart.point().position('date*value').color('name').adjust('stack');
    chart.render();
    return chart;
  }
  return (
    <View class="bg-white dark:bg-stone-900 pb-4">
      <View class="p-4 flex flex-col gap-1">
        <View class="text-32rpx font-bold">行情图表</View>
        <View class="text-24rpx op-50">更新时间 {props.batchTime || '--'}</View>
      </View>
      <View class="px-4 py-2">
        <View class="flex items-center gap-4 text-24rpx ">
          <View class="op-50">周期筛选</View>
          <View class="flex items-center gap-2">
            <For each={cycleList}>{(item) => (
              <View class={`flex items-center gap-2 rounded-md  px-4 py-1 
                ${cycle() === item.value ? 'text-black dark:text-white bg-gray-200 dark:bg-stone-600' : 'border border-solid border-gray-200 dark:border-stone-600 text-gray-500 dark:text-gray-300'}`} onClick={() => setCycle(item.value)}>{item.label}</View>
            )}</For>

          </View>
        </View>
      </View>

      <F2Canvas
        onInit={onChartInit}
        width={730}
        data={chartData()}
        shouldUpdate={true}
      />
    </View>
  );
}

export default MarketPageChart;