import type { Preset, SourceCodeTransformer } from 'unocss'
import { defineConfig } from 'unocss'
import presetLegacyCompat from '@unocss/preset-legacy-compat'

import {
  presetApplet,
  presetRemRpx,
} from 'unocss-applet'


const isApplet = process.env.TARO_ENV !== 'h5'
const presets: Preset[] = []
const transformers: SourceCodeTransformer[] = []

if (isApplet) {
  presets.push(presetApplet())
  presets.push(presetRemRpx())
  presets.push(presetLegacyCompat({
    commaStyleColorFunction: true,
    legacyColorSpace: true
  }))
}
else {
  presets.push(presetApplet())
  presets.push(presetRemRpx({ mode: 'rpx2rem' }))
}

export default defineConfig({
  presets,
  transformers,
})